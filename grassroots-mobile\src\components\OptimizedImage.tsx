import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Image,
  StyleSheet,
  Animated,
  ViewStyle,
  ImageStyle,
  ImageSourcePropType,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { Skeleton } from './Loading';

interface OptimizedImageProps {
  source: ImageSourcePropType | { uri: string };
  style?: ImageStyle;
  containerStyle?: ViewStyle;
  placeholder?: React.ReactNode;
  fallbackSource?: ImageSourcePropType;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
  lazy?: boolean;
  progressive?: boolean;
  cacheKey?: string;
  onLoad?: () => void;
  onError?: () => void;
}

// Simple in-memory cache for images
const imageCache = new Map<string, boolean>();

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  source,
  style,
  containerStyle,
  placeholder,
  fallbackSource,
  resizeMode = 'cover',
  lazy = true,
  progressive = true,
  cacheKey,
  onLoad,
  onError,
}) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isVisible, setIsVisible] = useState(!lazy);
  const [shouldLoad, setShouldLoad] = useState(!lazy);
  
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const viewRef = useRef<View>(null);

  // Get cache key from source URI
  const getCacheKey = () => {
    if (cacheKey) return cacheKey;
    if (typeof source === 'object' && 'uri' in source) {
      return source.uri;
    }
    return null;
  };

  // Check if image is cached
  const isCached = () => {
    const key = getCacheKey();
    return key ? imageCache.has(key) : false;
  };

  // Add image to cache
  const addToCache = () => {
    const key = getCacheKey();
    if (key) {
      imageCache.set(key, true);
    }
  };

  // Simple lazy loading for React Native
  useEffect(() => {
    if (!lazy) return;

    // For React Native, we'll use a simple timeout to simulate lazy loading
    // In a real implementation, you'd use a proper viewport detection library
    const timer = setTimeout(() => {
      setIsVisible(true);
      setShouldLoad(true);
    }, 100);

    return () => {
      clearTimeout(timer);
    };
  }, [lazy]);

  // Handle image load
  const handleLoad = () => {
    setIsLoading(false);
    addToCache();
    
    // Animate in
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: progressive ? 300 : 150,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();

    onLoad?.();
  };

  // Handle image error
  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
    onError?.();
  };

  // Reset animations when source changes
  useEffect(() => {
    if (shouldLoad) {
      fadeAnim.setValue(isCached() ? 1 : 0);
      scaleAnim.setValue(isCached() ? 1 : 0.95);
      setIsLoading(!isCached());
      setHasError(false);
    }
  }, [source, shouldLoad]);

  const renderPlaceholder = () => {
    if (placeholder) {
      return placeholder;
    }
    
    return (
      <Skeleton
        width="100%"
        height="100%"
        style={styles.skeleton}
      />
    );
  };

  const renderFallback = () => {
    if (fallbackSource) {
      return (
        <Image
          source={fallbackSource}
          style={[styles.image, style]}
          resizeMode={resizeMode}
        />
      );
    }
    
    return (
      <View style={[styles.fallback, style]}>
        <View style={styles.fallbackIcon} />
      </View>
    );
  };

  return (
    <View ref={viewRef} style={[styles.container, containerStyle]}>
      {/* Placeholder/Loading state */}
      {isLoading && shouldLoad && (
        <View style={[styles.placeholder, style]}>
          {renderPlaceholder()}
        </View>
      )}

      {/* Error state */}
      {hasError && (
        <View style={[styles.errorContainer, style]}>
          {renderFallback()}
        </View>
      )}

      {/* Actual image */}
      {shouldLoad && !hasError && (
        <Animated.View
          style={[
            styles.imageContainer,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          <Image
            source={source}
            style={[styles.image, style]}
            resizeMode={resizeMode}
            onLoad={handleLoad}
            onError={handleError}
            // Progressive loading for better UX
            progressiveRenderingEnabled={progressive}
            // Optimize for memory
            fadeDuration={progressive ? 300 : 0}
          />
        </Animated.View>
      )}
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    position: 'relative',
    overflow: 'hidden',
  },
  imageContainer: {
    width: '100%',
    height: '100%',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  placeholder: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: theme.colors.background.secondary,
  },
  skeleton: {
    borderRadius: 0,
  },
  errorContainer: {
    backgroundColor: theme.colors.background.tertiary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fallback: {
    backgroundColor: theme.colors.background.tertiary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fallbackIcon: {
    width: 40,
    height: 40,
    backgroundColor: theme.colors.text.tertiary,
    borderRadius: 20,
    opacity: 0.5,
  },
});

export default OptimizedImage;

# Bug Documentation: Expo Auth Session Plugin Configuration Error

## Error Description
The application failed to start with the error:
"PluginError: Package "expo-auth-session" does not contain a valid config plugin."
"Cannot use import statement outside a module"

This error occurred when running `npx expo start` after switching to Node.js v18 and reinstalling dependencies.

## Root Cause Analysis
**PRIMARY ISSUE: Incorrect Plugin Configuration**

The error was caused by incorrectly configuring `expo-auth-session` as a config plugin in `app.json`. Specific issues:

1. **Wrong Plugin Usage**: `expo-auth-session` is a library, not a config plugin
2. **ES Module Confusion**: Expo tried to load the library as a plugin, causing module syntax errors
3. **Configuration Misunderstanding**: The library should be imported in code, not added to plugins array

## Understanding expo-auth-session
- **What it is**: A library for handling OAuth authentication flows
- **What it's NOT**: A config plugin that modifies app configuration
- **How to use**: Import and use in your React Native components
- **No plugin needed**: Works without being added to the plugins array

## Code Changes Made

### File: grassroots-mobile/app.json
**REMOVED**: "expo-auth-session" from the plugins array

**Before:**
```json
"plugins": [
  "expo-auth-session"
]
```

**After:**
```json
// plugins array removed entirely since no plugins are needed
```

**Reason**: expo-auth-session is not a config plugin and should not be in the plugins array.

## Files Modified
- grassroots-mobile/app.json (removed incorrect plugin configuration)

## Steps Taken to Fix

### Step 1: Identified the Root Cause
1. Analyzed the error message showing plugin loading failure
2. Recognized that expo-auth-session is a library, not a plugin
3. Located the incorrect configuration in app.json

### Step 2: Fixed app.json Configuration
1. Opened grassroots-mobile/app.json
2. Removed "expo-auth-session" from the plugins array
3. Removed the entire plugins array since no plugins are currently needed
4. Kept the scheme configuration which is needed for OAuth redirects

### Step 3: Verified Configuration
1. Ensured all other app.json settings remain intact
2. Confirmed scheme "grassroots-mobile" is still configured for OAuth
3. Verified no other incorrect plugin configurations exist

## Why These Changes Were Necessary

### Removing expo-auth-session from Plugins
- **Correct Usage**: expo-auth-session should be imported in code, not configured as a plugin
- **Plugin vs Library**: Plugins modify app configuration; libraries provide functionality
- **ES Module Issues**: Loading a library as a plugin causes module syntax errors
- **Expo Standards**: Following Expo's recommended usage patterns

### Keeping Scheme Configuration
- **OAuth Requirement**: The custom scheme is needed for OAuth redirects
- **Deep Linking**: Allows the app to handle authentication callbacks
- **No Plugin Needed**: Scheme configuration works without additional plugins

## How expo-auth-session Should Be Used

### Correct Implementation (in your React components):
```javascript
import { AuthRequest, AuthSessionResult } from 'expo-auth-session';
import { makeRedirectUri } from 'expo-auth-session';

// Use in component
const redirectUri = makeRedirectUri({
  scheme: 'grassroots-mobile'
});
```

### What NOT to do:
```json
// DON'T add to app.json plugins
"plugins": ["expo-auth-session"]
```

## Additional Steps Needed
None. The fix is complete by removing the incorrect plugin configuration.

## Environment Details
- Node.js version: v18.x (correctly downgraded from v22)
- Expo SDK: ~53.0.17
- Package manager: npm (after yarn.lock removal)
- expo-auth-session version: ~5.5.2

## Verification Steps
After completing the fix:
1. Run `npx expo start` - should start without plugin errors
2. Verify app loads in Expo Go or simulator
3. Test OAuth functionality if implemented
4. Confirm no other plugin-related errors

## Common Plugin vs Library Confusion

### Actual Expo Config Plugins (add to plugins array):
- expo-camera
- expo-location
- expo-notifications
- @react-native-google-signin/google-signin

### Libraries (import in code, NOT plugins):
- expo-auth-session
- expo-crypto
- expo-constants
- expo-linking

## Prevention for Future
1. Check Expo documentation before adding items to plugins array
2. Understand difference between config plugins and libraries
3. Only add items to plugins if they explicitly state they are config plugins
4. Test configuration changes immediately after making them

## Team Communication
Inform team members:
1. expo-auth-session is not a plugin
2. Remove it from plugins array if they have it
3. Import and use it in code instead
4. Follow Expo documentation for plugin vs library usage

This prevents similar configuration errors in the future.
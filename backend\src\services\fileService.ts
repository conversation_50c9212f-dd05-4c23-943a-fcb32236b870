import sharp from 'sharp';
import fs from 'fs/promises';
import path from 'path';
import { logger } from '../utils/logger';

export const fileService = {
  async processEventImage(filePath: string): Promise<string> {
    try {
      const processedPath = filePath.replace(/\.[^/.]+$/, '_processed.jpg');
      
      await sharp(filePath)
        .resize(800, 600, { fit: 'cover' })
        .jpeg({ quality: 80 })
        .toFile(processedPath);
      
      // Delete original file
      await fs.unlink(filePath);
      
      logger.info(`Image processed: ${processedPath}`);
      return processedPath;
    } catch (error) {
      logger.error('Image processing error:', error);
      throw new Error('Failed to process image');
    }
  },

  async processProfileImage(filePath: string): Promise<string> {
    try {
      const processedPath = filePath.replace(/\.[^/.]+$/, '_profile.jpg');
      
      await sharp(filePath)
        .resize(300, 300, { fit: 'cover' })
        .jpeg({ quality: 85 })
        .toFile(processedPath);
      
      // Delete original file
      await fs.unlink(filePath);
      
      logger.info(`Profile image processed: ${processedPath}`);
      return processedPath;
    } catch (error) {
      logger.error('Profile image processing error:', error);
      throw new Error('Failed to process profile image');
    }
  },

  async deleteFile(filePath: string): Promise<void> {
    try {
      await fs.unlink(filePath);
      logger.info(`File deleted: ${filePath}`);
    } catch (error) {
      logger.error('File deletion error:', error);
    }
  }
};
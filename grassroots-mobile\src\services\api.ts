import AsyncStorage from '@react-native-async-storage/async-storage';

// API Configuration
const API_BASE_URL = '';

// Token storage keys
const ACCESS_TOKEN_KEY = 'access_token';
const REFRESH_TOKEN_KEY = 'refresh_token';

// Types
export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  error?: boolean;
  details?: any;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

export interface AuthResponse {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    isAdmin: boolean;
    adminRole?: string;
    verificationStatus: string;
  };
  tokens: {
    accessToken: string;
    refreshToken: string;
  };
}

// Token management
export const tokenManager = {
  async getAccessToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(ACCESS_TOKEN_KEY);
    } catch (error) {
      console.error('Error getting access token:', error);
      return null;
    }
  },

  async getRefreshToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(REFRESH_TOKEN_KEY);
    } catch (error) {
      console.error('Error getting refresh token:', error);
      return null;
    }
  },

  async setTokens(accessToken: string, refreshToken: string): Promise<void> {
    try {
      await AsyncStorage.multiSet([
        [ACCESS_TOKEN_KEY, accessToken],
        [REFRESH_TOKEN_KEY, refreshToken],
      ]);
    } catch (error) {
      console.error('Error setting tokens:', error);
    }
  },

  async clearTokens(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([ACCESS_TOKEN_KEY, REFRESH_TOKEN_KEY]);
    } catch (error) {
      console.error('Error clearing tokens:', error);
    }
  },
};

// HTTP client with automatic token handling
class ApiClient {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    
    // Get access token
    const accessToken = await tokenManager.getAccessToken();
    
    // Set default headers
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    // Add authorization header if token exists
    if (accessToken) {
      headers.Authorization = `Bearer ${accessToken}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      // Handle 401 (unauthorized) - try to refresh token
      if (response.status === 401 && accessToken) {
        const refreshed = await this.refreshAccessToken();
        if (refreshed) {
          // Retry the original request with new token
          const newAccessToken = await tokenManager.getAccessToken();
          if (newAccessToken) {
            headers.Authorization = `Bearer ${newAccessToken}`;
            const retryResponse = await fetch(url, {
              ...options,
              headers,
            });
            return await this.handleResponse<T>(retryResponse);
          }
        }
        // If refresh failed, clear tokens and throw error
        await tokenManager.clearTokens();
        throw new Error('Authentication failed');
      }

      return await this.handleResponse<T>(response);
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    const contentType = response.headers.get('content-type');
    const isJson = contentType && contentType.includes('application/json');
    
    if (isJson) {
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`);
      }
      
      return data;
    } else {
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return { data: await response.text() as any };
    }
  }

  private async refreshAccessToken(): Promise<boolean> {
    try {
      const refreshToken = await tokenManager.getRefreshToken();
      if (!refreshToken) {
        return false;
      }

      const response = await fetch(`${this.baseURL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken }),
      });

      if (response.ok) {
        const data = await response.json();
        await tokenManager.setTokens(
          data.tokens.accessToken,
          data.tokens.refreshToken
        );
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Token refresh failed:', error);
      return false;
    }
  }

  // HTTP methods
  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { method: 'DELETE' });
  }

  async postFormData<T>(endpoint: string, formData: FormData): Promise<ApiResponse<T>> {
    const accessToken = await tokenManager.getAccessToken();
    const headers: HeadersInit = {};
    
    if (accessToken) {
      headers.Authorization = `Bearer ${accessToken}`;
    }

    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers,
      body: formData,
    });

    return await this.handleResponse<T>(response);
  }
}

// Create API client instance
export const apiClient = new ApiClient(API_BASE_URL);

// API service methods
export const authAPI = {
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/auth/login', credentials);
    if (response.data) {
      await tokenManager.setTokens(
        response.data.tokens.accessToken,
        response.data.tokens.refreshToken
      );
    }
    return response.data!;
  },

  async register(data: RegisterData): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/auth/register', data);
    if (response.data) {
      await tokenManager.setTokens(
        response.data.tokens.accessToken,
        response.data.tokens.refreshToken
      );
    }
    return response.data!;
  },

  async logout(): Promise<void> {
    const refreshToken = await tokenManager.getRefreshToken();
    if (refreshToken) {
      try {
        await apiClient.post('/auth/logout', { refreshToken });
      } catch (error) {
        console.error('Logout API call failed:', error);
      }
    }
    await tokenManager.clearTokens();
  },

  async refreshToken(): Promise<boolean> {
    const refreshToken = await tokenManager.getRefreshToken();
    if (!refreshToken) return false;

    try {
      const response = await apiClient.post('/auth/refresh', { refreshToken });
      if (response.data) {
        await tokenManager.setTokens(
          response.data.tokens.accessToken,
          response.data.tokens.refreshToken
        );
        return true;
      }
      return false;
    } catch (error) {
      await tokenManager.clearTokens();
      return false;
    }
  },

  async getProfile(): Promise<any> {
    const response = await apiClient.get('/users/profile');
    return response.data;
  },

  async googleAuth(data: any): Promise<AuthResponse> {
    try {
      console.log('Making Google auth API request to:', `${this.baseURL}/auth/google`);
      console.log('Request data:', {
        ...data,
        idToken: data.idToken ? '[PRESENT]' : '[MISSING]',
        accessToken: data.accessToken ? '[PRESENT]' : '[MISSING]',
      });
      
      const response = await this.post<AuthResponse>('/auth/google', data);
      console.log('Google auth API response received');

      if (response.data && response.data.tokens) {
        await tokenManager.setTokens(
          response.data.tokens.accessToken,
          response.data.tokens.refreshToken
        );
        return response.data;
      } else {
        throw new Error('Invalid response format from server');
      }
    } catch (error: any) {
      console.error('Google auth API error:', error);
      throw new Error(error.message || 'Google authentication failed');
    }
  },
};

export default apiClient;

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useDispatch, useSelector } from 'react-redux';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { AppDispatch, RootState } from '../../store';
import { logoutUser } from '../../store/slices/authSlice';
import { Button, Card, Modal } from '../../components';
import { useTheme } from '../../contexts/ThemeContext';
import { createTypographyStyles, createCommonStyles } from '../../utils/styles';
import GradientBackground from '../../components/GradientBackground';

const ProfileScreen = ({ navigation }: { navigation: any }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const { profile } = useSelector((state: RootState) => state.user);
  const { theme, themeMode, setThemeMode, isDark } = useTheme();
  const styles = createStyles(theme);

  const [showThemeModal, setShowThemeModal] = useState(false);

  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: () => dispatch(logoutUser())
        }
      ]
    );
  };

  const handleEditProfile = () => {
    // TODO: Navigate to edit profile screen
    Alert.alert('Coming Soon', 'Profile editing will be available soon!');
  };

  const handleConnections = () => {
    navigation.navigate('Connections');
  };

  const handleSettings = () => {
    // TODO: Navigate to settings screen
    Alert.alert('Coming Soon', 'Settings will be available soon!');
  };

  const renderProfileHeader = () => (
    <LinearGradient
      colors={[theme.colors.primary[500], theme.colors.primary[600]]}
      style={styles.headerGradient}
    >
      <View style={styles.profileHeader}>
        <Image
          source={{
            uri: profile?.profilePhoto || 'https://i.pravatar.cc/150?img=8'
          }}
          style={styles.profileImage}
        />
        <Text style={styles.profileName}>
          {profile?.firstName || user?.firstName} {profile?.lastName || user?.lastName}
        </Text>
        <Text style={styles.profileEmail}>{user?.email}</Text>

        <Button
          title="Edit Profile"
          onPress={handleEditProfile}
          variant="secondary"
          size="small"
          icon="pencil"
          style={styles.editButton}
        />
      </View>
    </LinearGradient>
  );

  const renderStats = () => (
    <View style={styles.statsContainer}>
      <Card variant="default" padding="medium" style={styles.statCard}>
        <View style={styles.statIconContainer}>
          <Ionicons name="calendar" size={16} color={theme.colors.primary[500]} />
        </View>
        <Text style={styles.statNumber}>{profile?.eventsAttended || 0}</Text>
        <Text style={styles.statLabel}>Events</Text>
      </Card>
      <Card variant="default" padding="medium" style={styles.statCard}>
        <View style={styles.statIconContainer}>
          <Ionicons name="people" size={16} color={theme.colors.accent.blue} />
        </View>
        <Text style={styles.statNumber}>{profile?.connectionsMade || 0}</Text>
        <Text style={styles.statLabel}>Connections</Text>
      </Card>
      <Card variant="default" padding="medium" style={styles.statCard}>
        <View style={styles.statIconContainer}>
          <Ionicons name="heart" size={16} color={theme.colors.accent.pink} />
        </View>
        <Text style={styles.statNumber}>{profile?.interests?.length || 0}</Text>
        <Text style={styles.statLabel}>Interests</Text>
      </Card>
    </View>
  );

  const renderBio = () => (
    <Card variant="default" padding="large" style={styles.bioCard}>
      <Text style={styles.sectionTitle}>About Me</Text>
      <Text style={styles.bioText}>
        {profile?.bio || 'No bio added yet. Edit your profile to add a bio!'}
      </Text>
    </Card>
  );

  const renderInterests = () => (
    <Card variant="default" padding="large" style={styles.interestsCard}>
      <Text style={styles.sectionTitle}>My Interests</Text>
      {profile?.interests && profile.interests.length > 0 ? (
        <View style={styles.interestsContainer}>
          {profile.interests.map((interest, index) => (
            <View key={index} style={styles.interestChip}>
              <Text style={styles.interestText}>{interest}</Text>
            </View>
          ))}
        </View>
      ) : (
        <Text style={styles.emptyText}>No interests added yet</Text>
      )}
    </Card>
  );

  const renderMenuItems = () => (
    <Card variant="default" padding="none" style={styles.menuCard}>
      <TouchableOpacity style={styles.menuItem} onPress={handleConnections}>
        <View style={styles.menuItemLeft}>
          <Ionicons name="people" size={24} color={theme.colors.primary[500]} />
          <Text style={styles.menuItemText}>My Connections</Text>
        </View>
        <Ionicons name="chevron-forward" size={20} color={theme.colors.text.tertiary} />
      </TouchableOpacity>

      <View style={styles.menuDivider} />

      <TouchableOpacity
        style={styles.menuItem}
        onPress={() => navigation.navigate('WallOfShame')}
      >
        <View style={styles.menuItemLeft}>
          <Ionicons name="warning" size={24} color={theme.colors.secondary[500]} />
          <Text style={styles.menuItemText}>Wall of Shame</Text>
        </View>
        <Ionicons name="chevron-forward" size={20} color={theme.colors.text.tertiary} />
      </TouchableOpacity>

      <View style={styles.menuDivider} />

      <TouchableOpacity
        style={styles.menuItem}
        onPress={() => navigation.navigate('SafetyReport', {
          reportedUserId: '',
          reportedUserName: '',
          reportedUserPhoto: '',
        })}
      >
        <View style={styles.menuItemLeft}>
          <Ionicons name="flag" size={24} color={theme.colors.accent.pink} />
          <Text style={styles.menuItemText}>Report a User</Text>
        </View>
        <Ionicons name="chevron-forward" size={20} color={theme.colors.text.tertiary} />
      </TouchableOpacity>

      <View style={styles.menuDivider} />

      <TouchableOpacity
        style={styles.menuItem}
        onPress={() => setShowThemeModal(true)}
      >
        <View style={styles.menuItemLeft}>
          <Ionicons name="color-palette" size={24} color={theme.colors.secondary[500]} />
          <Text style={styles.menuItemText}>Theme</Text>
        </View>
        <View style={styles.menuItemRight}>
          <Text style={styles.menuItemSubtext}>
            {themeMode === 'system' ? 'System' : themeMode === 'dark' ? 'Dark' : 'Light'}
          </Text>
          <Ionicons name="chevron-forward" size={20} color={theme.colors.text.tertiary} />
        </View>
      </TouchableOpacity>

      <View style={styles.menuDivider} />

      <TouchableOpacity style={styles.menuItem} onPress={handleSettings}>
        <View style={styles.menuItemLeft}>
          <Ionicons name="settings" size={24} color={theme.colors.primary[500]} />
          <Text style={styles.menuItemText}>Settings</Text>
        </View>
        <Ionicons name="chevron-forward" size={20} color={theme.colors.text.tertiary} />
      </TouchableOpacity>

      <View style={styles.menuDivider} />

      <TouchableOpacity style={styles.menuItem} onPress={handleLogout}>
        <View style={styles.menuItemLeft}>
          <Ionicons name="log-out" size={24} color={theme.colors.error} />
          <Text style={[styles.menuItemText, { color: theme.colors.error }]}>Logout</Text>
        </View>
        <Ionicons name="chevron-forward" size={20} color={theme.colors.text.tertiary} />
      </TouchableOpacity>
    </Card>
  );

  const renderThemeModal = () => (
    <Modal
      visible={showThemeModal}
      onClose={() => setShowThemeModal(false)}
      animationType="slide"
      position="bottom"
    >
      <View style={styles.themeModal}>
        <Text style={styles.themeModalTitle}>Choose Theme</Text>
        <Text style={styles.themeModalSubtitle}>
          Select your preferred theme for the VIBE app
        </Text>

        <View style={styles.themeOptions}>
          {/* Light Theme Option */}
          <TouchableOpacity
            style={[
              styles.themeOption,
              themeMode === 'light' && styles.themeOptionSelected,
            ]}
            onPress={() => {
              setThemeMode('light');
              setShowThemeModal(false);
            }}
          >
            <View style={styles.themePreview}>
              <View style={[styles.themePreviewCard, { backgroundColor: '#FFFFFF' }]}>
                <View style={[styles.themePreviewHeader, { backgroundColor: '#F8F9FA' }]} />
                <View style={[styles.themePreviewContent, { backgroundColor: '#FFFFFF' }]} />
              </View>
            </View>
            <Text style={styles.themeOptionTitle}>Light</Text>
            <Text style={styles.themeOptionDescription}>Clean and bright</Text>
          </TouchableOpacity>

          {/* Dark Theme Option */}
          <TouchableOpacity
            style={[
              styles.themeOption,
              themeMode === 'dark' && styles.themeOptionSelected,
            ]}
            onPress={() => {
              setThemeMode('dark');
              setShowThemeModal(false);
            }}
          >
            <View style={styles.themePreview}>
              <GradientBackground variant="primary" style={styles.themePreviewCard}>
                <GradientBackground variant="secondary" style={styles.themePreviewHeader} />
                <View style={[styles.themePreviewContent, { backgroundColor: theme.colors.background.card }]} />
              </GradientBackground>
            </View>
            <Text style={styles.themeOptionTitle}>Dark</Text>
            <Text style={styles.themeOptionDescription}>Beautiful purple & pink</Text>
          </TouchableOpacity>

          {/* System Theme Option */}
          <TouchableOpacity
            style={[
              styles.themeOption,
              themeMode === 'system' && styles.themeOptionSelected,
            ]}
            onPress={() => {
              setThemeMode('system');
              setShowThemeModal(false);
            }}
          >
            <View style={styles.themePreview}>
              <View style={styles.themePreviewCard}>
                <View style={[styles.themePreviewSplit, { backgroundColor: '#F8F9FA' }]} />
                <GradientBackground variant="primary" style={styles.themePreviewSplit} />
              </View>
            </View>
            <Text style={styles.themeOptionTitle}>System</Text>
            <Text style={styles.themeOptionDescription}>Follow device setting</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  return (
    <GradientBackground variant="primary" style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {renderProfileHeader()}
          {renderStats()}
          {renderBio()}
          {renderInterests()}
          {renderMenuItems()}
        </ScrollView>
        {renderThemeModal()}
      </SafeAreaView>
    </GradientBackground>
  );
};

const createStyles = (theme: any) => {
  const typography = createTypographyStyles(theme);

  return StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  headerGradient: {
    paddingBottom: theme.spacing[8],
  },
  profileHeader: {
    alignItems: 'center',
    paddingTop: theme.spacing[6],
    paddingHorizontal: theme.spacing[6],
  },
  profileImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 4,
    borderColor: theme.colors.background.primary,
    marginBottom: theme.spacing[4],
  },
  profileName: {
    ...typography.h3,
    color: theme.colors.text.inverse,
    marginBottom: theme.spacing[1],
  },
  profileEmail: {
    ...typography.body1,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: theme.spacing[6],
  },
  editButton: {
    backgroundColor: theme.colors.background.primary,
    borderColor: theme.colors.background.primary,
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: theme.spacing[5],
    paddingVertical: theme.spacing[3],
    marginTop: -theme.spacing[6],
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.background.primary,
    marginHorizontal: theme.spacing[1],
    borderRadius: theme.borderRadius.lg,
    height: 80,
    paddingVertical: theme.spacing[2],
    paddingHorizontal: theme.spacing[1],
    overflow: 'hidden',
  },
  statIconContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.spacing[1],
  },
  statNumber: {
    ...typography.h6,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.bold,
    marginBottom: 2,
  },
  statLabel: {
    ...typography.caption,
    color: theme.colors.text.secondary,
    fontSize: 9,
    textAlign: 'center',
    lineHeight: 12,
  },
  bioCard: {
    marginHorizontal: theme.spacing[5],
    marginBottom: theme.spacing[4],
  },
  interestsCard: {
    marginHorizontal: theme.spacing[5],
    marginBottom: theme.spacing[4],
  },
  sectionTitle: {
    ...typography.h6,
    color: theme.colors.text.primary,
    marginBottom: theme.spacing[3],
  },
  bioText: {
    ...typography.body1,
    color: theme.colors.text.secondary,
    lineHeight: 24,
  },
  interestsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  interestChip: {
    backgroundColor: theme.colors.primary[100],
    borderRadius: theme.borderRadius.full,
    paddingVertical: theme.spacing[1],
    paddingHorizontal: theme.spacing[3],
    marginRight: theme.spacing[2],
    marginBottom: theme.spacing[2],
  },
  interestText: {
    ...typography.body2,
    color: theme.colors.primary[700],
    fontWeight: theme.typography.fontWeight.medium,
  },
  emptyText: {
    ...typography.body1,
    color: theme.colors.text.tertiary,
    fontStyle: 'italic',
  },
  menuCard: {
    marginHorizontal: theme.spacing[5],
    marginBottom: theme.spacing[6],
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: theme.spacing[4],
    paddingHorizontal: theme.spacing[5],
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuItemText: {
    ...typography.body1,
    color: theme.colors.text.primary,
    marginLeft: theme.spacing[3],
    fontWeight: theme.typography.fontWeight.medium,
  },
  menuDivider: {
    height: 1,
    backgroundColor: theme.colors.border.light,
    marginHorizontal: theme.spacing[5],
  },
  menuItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuItemSubtext: {
    ...typography.body2,
    color: theme.colors.text.secondary,
    marginRight: theme.spacing[2],
  },

  // Theme Modal Styles
  themeModal: {
    padding: theme.spacing[6],
  },
  themeModalTitle: {
    ...typography.h4,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.bold,
    textAlign: 'center',
    marginBottom: theme.spacing[2],
  },
  themeModalSubtitle: {
    ...typography.body1,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    marginBottom: theme.spacing[8],
  },
  themeOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: theme.spacing[4],
  },
  themeOption: {
    flex: 1,
    alignItems: 'center',
    padding: theme.spacing[4],
    borderRadius: theme.borderRadius.lg,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  themeOptionSelected: {
    borderColor: theme.colors.primary[500],
    backgroundColor: theme.colors.primary[50],
  },
  themePreview: {
    width: 60,
    height: 80,
    marginBottom: theme.spacing[3],
    borderRadius: theme.borderRadius.md,
    overflow: 'hidden',
  },
  themePreviewCard: {
    flex: 1,
    borderRadius: theme.borderRadius.md,
  },
  themePreviewHeader: {
    height: 20,
    borderTopLeftRadius: theme.borderRadius.md,
    borderTopRightRadius: theme.borderRadius.md,
  },
  themePreviewContent: {
    flex: 1,
    borderBottomLeftRadius: theme.borderRadius.md,
    borderBottomRightRadius: theme.borderRadius.md,
  },
  themePreviewSplit: {
    flex: 1,
  },
  themeOptionTitle: {
    ...typography.h6,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.semibold,
    marginBottom: theme.spacing[1],
  },
  themeOptionDescription: {
    ...typography.caption,
    color: theme.colors.text.secondary,
    textAlign: 'center',
  },
  });
};

export default ProfileScreen;

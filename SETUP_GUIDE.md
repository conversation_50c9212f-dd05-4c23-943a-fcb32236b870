# Vibe App - Setup Guide

This guide will walk you through the process of setting up the Vibe application for local development.

## Prerequisites

Before you begin, ensure you have the following installed on your system:

- **Node.js** (v18 or higher)
- **npm** or **Yarn**
- **PostgreSQL** (a local or remote instance)
- **Expo Go** app on your mobile device (for testinng)
- **Git**

## 1. Backend Setup

The backend server powers the mobile application.

1.  **Navigate to the backend directory:**
    ```bash
    cd backend
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    ```

3.  **Set up environment variables:**
    Create a `.env` file by copying the example:
    ```bash
    cp .env.example .env
    ```
    Open the `.env` file and set the `DATABASE_URL` to your PostgreSQL connection string. It should look like this:
    ```
    DATABASE_URL="postgresql://USER:PASSWORD@HOST:PORT/DATABASE"
    ```

4.  **Set up the database:**
    Run the following command to apply the database schema:
    ```bash
    npm run db:push
    ```

5.  **Start the backend server:**
    ```bash
    npm run dev
    ```
    The server will start on `http://localhost:3000`.

## 2. Mobile App Setup

The mobile app is built with React Native and Expo.

1.  **Navigate to the mobile app directory:**
    ```bash
    cd ../grassroots-mobile
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    ```

3.  **Connect the app to your local backend:**
    Open `src/services/api.ts` in your code editor. Find the `API_BASE_URL` constant and change it to your computer's local IP address.

    For example:
    ```typescript
    // Before
    const API_BASE_URL = '';

    // After (replace with your IP)
    const API_BASE_URL = 'http://*************:3000/api';
    ```
    > **Tip:** You can find your local IP address by running `ipconfig` on Windows or `ifconfig` on macOS/Linux.

4.  **Start the mobile app:**
    ```bash
    npx expo start
    ```
    This will open a Metro Bundler window in your browser.

5.  **Run the app:**
    - **On your phone:** Scan the QR code from the Metro Bundler with the Expo Go app.
    - **On a simulator/emulator:** Follow the instructions in the Metro Bundler terminal.

You should now have the Vibe app running on your device, connected to your local backend.

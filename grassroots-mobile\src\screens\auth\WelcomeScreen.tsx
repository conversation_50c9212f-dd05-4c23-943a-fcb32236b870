import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Button } from '../../components';
import GradientBackground from '../../components/GradientBackground';
import { useTheme } from '../../contexts/ThemeContext';
import { createTypographyStyles } from '../../utils/styles';

const { width, height } = Dimensions.get('window');

interface WelcomeScreenProps {
  navigation: any;
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleGetStarted = () => {
    console.log('Get Started pressed');
    navigation.navigate('Register');
  };

  const handleSignIn = () => {
    console.log('Sign In pressed');
    navigation.navigate('Login');
  };

  return (
    <GradientBackground variant="hero" style={styles.container}>
        <Animated.View 
          style={[
            styles.content,
            {
              opacity: fadeAnim,
              transform: [
                { translateY: slideAnim },
                { scale: scaleAnim }
              ]
            }
          ]}
        >
          {/* Logo/Icon */}
          <View style={styles.logoContainer}>
            <View style={styles.logoCircle}>
              <Ionicons name="leaf" size={60} color="#fff" />
            </View>
          </View>

          {/* App Name */}
          <Text style={styles.appName}>VIBE</Text>
          
          {/* Tagline */}
          <Text style={styles.tagline}>
            "Go outside, touch some grass"
          </Text>

          {/* Description */}
          <Text style={styles.description}>
            Connect with fellow students through real-world experiences. 
            Make genuine friendships and discover your campus community.
          </Text>

          {/* Features */}
          <View style={styles.featuresContainer}>
            <View style={styles.feature}>
              <Ionicons name="people" size={24} color="#fff" />
              <Text style={styles.featureText}>Meet New People</Text>
            </View>
            <View style={styles.feature}>
              <Ionicons name="calendar" size={24} color="#fff" />
              <Text style={styles.featureText}>Join Events</Text>
            </View>
            <View style={styles.feature}>
              <Ionicons name="heart" size={24} color="#fff" />
              <Text style={styles.featureText}>Make Connections</Text>
            </View>
          </View>

          {/* Buttons */}
          <View style={styles.buttonContainer}>
            <Button
              title="Get Started"
              onPress={handleGetStarted}
              variant="secondary"
              size="large"
              icon="arrow-forward"
              iconPosition="right"
              fullWidth
              style={styles.primaryButton}
            />

            <Button
              title="I already have an account"
              onPress={handleSignIn}
              variant="ghost"
              size="medium"
              style={styles.secondaryButton}
              textStyle={styles.secondaryButtonText}
            />
          </View>
        </Animated.View>

        {/* Decorative Elements */}
        <View style={styles.decorativeCircle1} />
        <View style={styles.decorativeCircle2} />
        <View style={styles.decorativeCircle3} />
    </GradientBackground>
  );
};

const createStyles = (theme: any) => {
  const typography = createTypographyStyles(theme);

  return StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.spacing[6],
  },
  content: {
    alignItems: 'center',
    width: '100%',
  },
  logoContainer: {
    marginBottom: 30,
  },
  logoCircle: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  appName: {
    ...typography.h1,
    color: theme.colors.text.inverse,
    marginBottom: theme.spacing[3],
    textAlign: 'center',
  },
  tagline: {
    ...typography.h6,
    color: 'rgba(255, 255, 255, 0.9)',
    fontStyle: 'italic',
    marginBottom: theme.spacing[5],
    textAlign: 'center',
  },
  description: {
    ...typography.body1,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    marginBottom: theme.spacing[10],
    paddingHorizontal: theme.spacing[3],
  },
  featuresContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginBottom: 50,
  },
  feature: {
    alignItems: 'center',
    flex: 1,
  },
  featureText: {
    color: '#fff',
    fontSize: 12,
    marginTop: 8,
    textAlign: 'center',
    fontWeight: '600',
  },
  buttonContainer: {
    width: '100%',
    alignItems: 'center',
  },
  primaryButton: {
    marginBottom: theme.spacing[4],
    backgroundColor: theme.colors.background.primary,
    borderColor: theme.colors.background.primary,
  },
  secondaryButton: {
    paddingVertical: theme.spacing[3],
    paddingHorizontal: theme.spacing[5],
  },
  secondaryButtonText: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: theme.typography.fontSize.base,
    textDecorationLine: 'underline',
  },
  decorativeCircle1: {
    position: 'absolute',
    top: 100,
    right: -50,
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  decorativeCircle2: {
    position: 'absolute',
    bottom: 150,
    left: -30,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  decorativeCircle3: {
    position: 'absolute',
    top: 200,
    left: 50,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  });
};

export default WelcomeScreen;

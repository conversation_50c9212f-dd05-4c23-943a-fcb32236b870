# 📱 **GRASSROOTS** React Native & Expo Setup Guide

*Complete beginner-friendly guide to setting up React Native and Expo development environment*

---

## 🎯 What You'll Learn

This guide will help you set up everything needed to develop the Grassroots mobile app, even if you've never done mobile development before. By the end, you'll have a complete development environment running on your computer.

---

## 📋 Prerequisites

### System Requirements
- **Windows 10/11**, **macOS 10.15+**, or **Linux Ubuntu 18.04+**
- **8GB RAM minimum** (16GB recommended)
- **20GB free disk space**
- **Stable internet connection**

### What We'll Install (Expo Development)
1. **Node.js** - JavaScript runtime for development tools
2. **Git** - Version control system
3. **VS Code** - Code editor with React Native extensions
4. **Expo CLI** - Development tools for React Native
5. **Expo Go App** - For testing on your phone (no Android Studio/Xcode needed!)

### Optional (Advanced Development Only)
- **Android Studio** - Only if you need custom native modules
- **Xcode** - Only if you need custom native modules (macOS only)

---

## 🚀 Step 1: Install Node.js

Node.js is required for all React Native development tools.

### Windows & macOS
1. Go to [nodejs.org](https://nodejs.org/)
2. Download the **LTS version** (Long Term Support)
3. Run the installer and follow the setup wizard
4. Accept all default settings

### Linux (Ubuntu/Debian)
```bash
# Update package index
sudo apt update

# Install Node.js LTS
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installation
node --version
npm --version
```

### Verify Installation
Open terminal/command prompt and run:
```bash
node --version
npm --version
```
You should see version numbers (e.g., v18.17.0 and 9.6.7).

---

## 🔧 Step 2: Install Git

Git is essential for version control and collaboration.

### Windows
1. Download from [git-scm.com](https://git-scm.com/download/win)
2. Run installer with default settings
3. Choose "Git Bash Here" and "Git GUI Here" options

### macOS
```bash
# Install using Homebrew (recommended)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
brew install git

# Or download from git-scm.com
```

### Linux
```bash
sudo apt update
sudo apt install git
```

### Configure Git
```bash
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
```

---

## 💻 Step 3: Install VS Code

VS Code is the recommended editor for React Native development.

### All Platforms
1. Download from [code.visualstudio.com](https://code.visualstudio.com/)
2. Install with default settings
3. Launch VS Code

### Essential Extensions
Install these extensions for React Native development:

1. **React Native Tools** (Microsoft)
2. **ES7+ React/Redux/React-Native snippets**
3. **Prettier - Code formatter**
4. **ESLint**
5. **Auto Rename Tag**
6. **Bracket Pair Colorizer**
7. **GitLens**
8. **Thunder Client** (for API testing)

### Install Extensions
1. Open VS Code
2. Click Extensions icon (Ctrl+Shift+X)
3. Search for each extension name
4. Click "Install" for each one

---

## 📱 Step 4: Install Expo CLI

Expo CLI is the main tool for React Native development.

### Install Globally
```bash
npm install -g @expo/cli
```

### Verify Installation
```bash
expo --version
```

### Install Expo Go App
Download on your phone for testing:
- **iOS:** [App Store - Expo Go](https://apps.apple.com/app/expo-go/id982107779)
- **Android:** [Google Play - Expo Go](https://play.google.com/store/apps/details?id=host.exp.exponent)

---

## 🤖 Step 5: Android Development Setup

### Install Android Studio
1. Download from [developer.android.com/studio](https://developer.android.com/studio)
2. Run installer and follow setup wizard
3. Choose "Standard" installation
4. Wait for SDK downloads to complete

### Configure Android SDK
1. Open Android Studio
2. Go to **File > Settings** (Windows) or **Android Studio > Preferences** (macOS)
3. Navigate to **Appearance & Behavior > System Settings > Android SDK**
4. In **SDK Platforms** tab, check:
   - Android 13 (API Level 33)
   - Android 12 (API Level 31)
   - Android 11 (API Level 30)
5. In **SDK Tools** tab, check:
   - Android SDK Build-Tools
   - Android Emulator
   - Android SDK Platform-Tools
   - Intel x86 Emulator Accelerator (if using Intel processor)
6. Click "Apply" and wait for downloads

### Set Environment Variables

#### Windows
1. Open System Properties > Advanced > Environment Variables
2. Add new system variable:
   - **Variable name:** `ANDROID_HOME`
   - **Variable value:** `C:\Users\<USER>\AppData\Local\Android\Sdk`
3. Edit PATH variable and add:
   - `%ANDROID_HOME%\platform-tools`
   - `%ANDROID_HOME%\emulator`
   - `%ANDROID_HOME%\tools`
   - `%ANDROID_HOME%\tools\bin`

#### macOS/Linux
Add to your shell profile (~/.bashrc, ~/.zshrc, etc.):
```bash
export ANDROID_HOME=$HOME/Android/Sdk
export PATH=$PATH:$ANDROID_HOME/emulator
export PATH=$PATH:$ANDROID_HOME/platform-tools
export PATH=$PATH:$ANDROID_HOME/tools
export PATH=$PATH:$ANDROID_HOME/tools/bin
```

### Create Android Virtual Device (AVD)
1. Open Android Studio
2. Click "More Actions" > "Virtual Device Manager"
3. Click "Create Device"
4. Choose "Phone" > "Pixel 4" > "Next"
5. Download and select "API Level 33" system image
6. Click "Next" > "Finish"
7. Click play button to start emulator

---

## 🍎 Step 6: iOS Development Setup (macOS Only)

### Install Xcode
1. Open Mac App Store
2. Search for "Xcode"
3. Click "Install" (this will take a while - Xcode is large)
4. Launch Xcode and accept license agreements

### Install Xcode Command Line Tools
```bash
xcode-select --install
```

### Install iOS Simulator
1. Open Xcode
2. Go to **Xcode > Preferences > Components**
3. Download iOS simulators for latest iOS versions
4. Go to **Window > Devices and Simulators**
5. Click "+" to create new simulator
6. Choose iPhone 14 with latest iOS version

### Install CocoaPods
```bash
sudo gem install cocoapods
```

---

## 🎉 Step 7: Create Your First Expo Project

### Create New Project
```bash
# Navigate to your development folder
cd ~/Documents

# Create new Expo project
npx create-expo-app GrassrootsApp --template

# Choose "Blank (TypeScript)" template

# Navigate to project
cd GrassrootsApp
```

### Start Development Server
```bash
expo start
```

### Test on Device
1. Open Expo Go app on your phone
2. Scan QR code from terminal
3. App should load on your device

### Test on Emulator
- **Android:** Press 'a' in terminal to open Android emulator
- **iOS:** Press 'i' in terminal to open iOS simulator

---

## 🛠️ Step 8: Development Tools Setup

### Install Additional Tools
```bash
# React Native Debugger (optional but helpful)
npm install -g react-devtools

# Flipper (advanced debugging)
# Download from https://fbflipper.com/
```

### VS Code Configuration
Create `.vscode/settings.json` in your project:
```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "emmet.includeLanguages": {
    "javascript": "javascriptreact"
  }
}
```

---

## 🔍 Troubleshooting Common Issues

### Node.js Issues
- **Permission errors:** Use `sudo` on macOS/Linux or run as administrator on Windows
- **Version conflicts:** Use Node Version Manager (nvm) to manage multiple Node versions

### Android Issues
- **Emulator won't start:** Enable virtualization in BIOS settings
- **SDK not found:** Double-check ANDROID_HOME environment variable
- **Build errors:** Clear cache with `expo r -c`

### iOS Issues (macOS)
- **Simulator not opening:** Restart Xcode and try again
- **Build errors:** Run `npx pod-install` in iOS folder
- **Certificate issues:** Check Apple Developer account settings

### General Issues
- **Metro bundler errors:** Clear cache with `expo r -c`
- **Network issues:** Check firewall and antivirus settings
- **Slow performance:** Close unnecessary applications and restart development server

---

## 📚 Next Steps

### Learn React Native Basics
1. **React Native Documentation:** [reactnative.dev](https://reactnative.dev/)
2. **Expo Documentation:** [docs.expo.dev](https://docs.expo.dev/)
3. **JavaScript ES6+ Features:** Arrow functions, async/await, destructuring
4. **TypeScript Basics:** Type annotations, interfaces, generics

### Recommended Learning Path
1. Complete React Native tutorial
2. Build a simple todo app
3. Learn navigation with React Navigation
4. Practice with state management (Redux)
5. Explore native device features (camera, location)

### Join Communities
- **React Native Community Discord**
- **Expo Discord Server**
- **Stack Overflow** (react-native tag)
- **Reddit:** r/reactnative

---

## ✅ Verification Checklist

Before starting Grassroots development, ensure:

- [ ] Node.js and npm are installed and working
- [ ] Git is configured with your information
- [ ] VS Code is installed with React Native extensions
- [ ] Expo CLI is installed globally
- [ ] Android Studio is set up with SDK and emulator
- [ ] iOS development tools are installed (macOS only)
- [ ] You can create and run a basic Expo project
- [ ] App runs on both device and emulator/simulator
- [ ] Development tools are configured properly

---

*You're now ready to start building Grassroots! Proceed to the development phases and use the detailed prompts to guide your development process.*

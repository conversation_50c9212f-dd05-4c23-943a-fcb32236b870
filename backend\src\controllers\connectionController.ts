import { Request, Response } from 'express';
import { connectionService } from '../services/connectionService';
import { AuthRequest } from '../middleware/auth';
import { logger } from '../utils/logger';

export const createConnection = async (req: AuthRequest, res: Response) => {
  try {
    const { eventId, toUserId, connectionType, feedback, isAnonymous } = req.body;
    const fromUserId = req.user!.id;

    if (!eventId || !toUserId || !connectionType) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    if (!['FRIEND', 'SPARK', 'PASS'].includes(connectionType)) {
      return res.status(400).json({ error: 'Invalid connection type' });
    }

    const connection = await connectionService.createConnection(fromUserId, {
      eventId,
      toUserId,
      connectionType,
      feedback,
      isAnonymous
    });

    res.status(201).json({ connection });
  } catch (error: any) {
    logger.error('Create connection error:', error);
    
    if (error.message === 'User did not participate in this event' || 
        error.message === 'Connection already exists') {
      return res.status(400).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to create connection' });
  }
};

export const getUserConnections = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const connections = await connectionService.getUserConnections(userId);
    res.json({ connections });
  } catch (error: any) {
    logger.error('Get user connections error:', error);
    res.status(500).json({ error: 'Failed to fetch connections' });
  }
};

export const getEventParticipantsForReview = async (req: AuthRequest, res: Response) => {
  try {
    const { eventId } = req.params;
    const userId = req.user!.id;

    const participants = await connectionService.getEventParticipantsForReview(eventId, userId);
    res.json({ participants });
  } catch (error: any) {
    logger.error('Get event participants error:', error);
    res.status(500).json({ error: 'Failed to fetch participants' });
  }
};

export const sendDirectMessage = async (req: AuthRequest, res: Response) => {
  try {
    const { connectionId } = req.params;
    const { content } = req.body;
    const senderId = req.user!.id;

    if (!content || content.trim().length === 0) {
      return res.status(400).json({ error: 'Message content is required' });
    }

    const message = await connectionService.sendDirectMessage(connectionId, senderId, content);
    res.status(201).json({ message });
  } catch (error: any) {
    logger.error('Send direct message error:', error);
    
    if (error.message === 'Invalid connection' || error.message === 'Unauthorized to send message') {
      return res.status(403).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to send message' });
  }
};

export const getDirectMessages = async (req: AuthRequest, res: Response) => {
  try {
    const { connectionId } = req.params;
    const userId = req.user!.id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 50;

    const messages = await connectionService.getDirectMessages(connectionId, userId, page, limit);
    res.json({ messages });
  } catch (error: any) {
    logger.error('Get direct messages error:', error);
    
    if (error.message === 'Unauthorized access') {
      return res.status(403).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to fetch messages' });
  }
};
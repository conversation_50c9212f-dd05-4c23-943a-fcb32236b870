import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator, TransitionPresets } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useSelector } from 'react-redux';
import { RootState } from '../store';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import {
  HomeStackParamList,
  EventsStackParamList,
  ChatStackParamList,
  ProfileStackParamList,
  MainTabParamList,
} from '../types/navigation';

// Auth Screens
import LoginScreen from '../screens/auth/LoginScreen';
import ProfileSetupScreen from '../screens/auth/ProfileSetupScreen';

// Main App Screens
import HomeScreen from '../screens/main/HomeScreen';
import EventsScreen from '../screens/main/EventsScreen';
import EventDetailScreen from '../screens/main/EventDetailScreen';
import ProfileScreen from '../screens/main/ProfileScreen';
import ChatScreen from '../screens/main/ChatScreen';
import ChatListScreen from '../screens/main/ChatListScreen';
import ConnectionsScreen from '../screens/main/ConnectionsScreen';
import PostEventReviewScreen from '../screens/main/PostEventReviewScreen';
import SafetyReportScreen from '../screens/main/SafetyReportScreen';
import WallOfShameScreen from '../screens/main/WallOfShameScreen';
import NotificationsScreen from '../screens/main/NotificationsScreen';
import CreateEventScreen from '../screens/main/CreateEventScreen';

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator<MainTabParamList>();
const HomeStackNavigator = createStackNavigator<HomeStackParamList>();
const EventsStackNavigator = createStackNavigator<EventsStackParamList>();
const ChatStackNavigator = createStackNavigator<ChatStackParamList>();
const ProfileStackNavigator = createStackNavigator<ProfileStackParamList>();

// Custom transition configurations
const slideFromRightTransition = {
  ...TransitionPresets.SlideFromRightIOS,
  transitionSpec: {
    open: {
      animation: 'timing' as const,
      config: {
        duration: 300,
      },
    },
    close: {
      animation: 'timing' as const,
      config: {
        duration: 250,
      },
    },
  },
};

const modalTransition = {
  ...TransitionPresets.ModalSlideFromBottomIOS,
  transitionSpec: {
    open: {
      animation: 'timing' as const,
      config: {
        duration: 350,
      },
    },
    close: {
      animation: 'timing' as const,
      config: {
        duration: 300,
      },
    },
  },
};

const fadeTransition = {
  ...TransitionPresets.FadeFromBottomAndroid,
  transitionSpec: {
    open: {
      animation: 'timing' as const,
      config: {
        duration: 200,
      },
    },
    close: {
      animation: 'timing' as const,
      config: {
        duration: 150,
      },
    },
  },
};

const AuthStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen
        name="Login"
        component={LoginScreen}
      />
    </Stack.Navigator>
  );
};

const HomeStack = () => (
  <HomeStackNavigator.Navigator
    screenOptions={{
      headerShown: false,
      cardStyle: { backgroundColor: 'transparent' },
      ...slideFromRightTransition,
    }}
  >
    <HomeStackNavigator.Screen name="HomeMain" component={HomeScreen} />
    <HomeStackNavigator.Screen
      name="EventDetail"
      component={EventDetailScreen}
      options={{
        ...modalTransition,
      }}
    />

    <HomeStackNavigator.Screen
      name="Notifications"
      component={NotificationsScreen}
      options={{
        ...slideFromRightTransition,
      }}
    />
    <HomeStackNavigator.Screen
      name="CreateEvent"
      component={CreateEventScreen}
      options={{
        ...slideFromRightTransition,
      }}
    />
  </HomeStackNavigator.Navigator>
);

const EventsStack = () => (
  <EventsStackNavigator.Navigator
    screenOptions={{
      headerShown: false,
      cardStyle: { backgroundColor: 'transparent' },
      ...slideFromRightTransition,
    }}
  >
    <EventsStackNavigator.Screen name="EventsMain" component={EventsScreen} />
    <EventsStackNavigator.Screen
      name="EventDetail"
      component={EventDetailScreen}
      options={{
        ...modalTransition,
      }}
    />
    <EventsStackNavigator.Screen
      name="PostEventReview"
      component={PostEventReviewScreen}
      options={{
        ...slideFromRightTransition,
      }}
    />
  </EventsStackNavigator.Navigator>
);

const ChatStack = () => {
  const { theme } = useTheme();

  return (
    <ChatStackNavigator.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: 'transparent' },
        ...slideFromRightTransition,
      }}
    >
      <ChatStackNavigator.Screen name="ChatList" component={ChatListScreen} />
      <ChatStackNavigator.Screen
        name="Chat"
        component={ChatScreen}
        options={{
          headerShown: true,
          headerStyle: {
            backgroundColor: theme.colors.background.primary,
            elevation: 0,
            shadowOpacity: 0,
          },
          headerTintColor: theme.colors.text.primary,
          ...slideFromRightTransition,
        }}
      />
    </ChatStackNavigator.Navigator>
  );
};

const ProfileStack = () => (
  <ProfileStackNavigator.Navigator
    screenOptions={{
      headerShown: false,
      cardStyle: { backgroundColor: 'transparent' },
      ...slideFromRightTransition,
    }}
  >
    <ProfileStackNavigator.Screen name="ProfileMain" component={ProfileScreen} />
    <ProfileStackNavigator.Screen
      name="Connections"
      component={ConnectionsScreen}
      options={{
        ...slideFromRightTransition,
      }}
    />
    <ProfileStackNavigator.Screen
      name="SafetyReport"
      component={SafetyReportScreen}
      options={{
        ...modalTransition,
      }}
    />
    <ProfileStackNavigator.Screen
      name="WallOfShame"
      component={WallOfShameScreen}
      options={{
        ...slideFromRightTransition,
      }}
    />
  </ProfileStackNavigator.Navigator>
);

const MainTabs = () => {
  const { theme } = useTheme();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Events') {
            iconName = focused ? 'calendar' : 'calendar-outline';
          } else if (route.name === 'Chat') {
            iconName = focused ? 'chatbubbles' : 'chatbubbles-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          } else {
            iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: theme.colors.primary[500],
        tabBarInactiveTintColor: theme.colors.text.tertiary,
      tabBarStyle: {
        backgroundColor: theme.colors.background.primary,
        borderTopWidth: 1,
        borderTopColor: theme.colors.border.light,
        paddingBottom: 5,
        paddingTop: 5,
        height: 60,
        elevation: 8,
        shadowColor: theme.colors.text.primary,
        shadowOffset: { width: 0, height: -2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      tabBarLabelStyle: {
        fontSize: theme.typography.fontSize.xs,
        fontWeight: '600' as const,
      },
    })}
  >
    <Tab.Screen
      name="Home"
      component={HomeStack}
      options={{ tabBarLabel: 'Discover' }}
    />
    <Tab.Screen
      name="Events"
      component={EventsStack}
      options={{ tabBarLabel: 'Events' }}
    />
    <Tab.Screen
      name="Chat"
      component={ChatStack}
      options={{ tabBarLabel: 'Chats' }}
    />
    <Tab.Screen
      name="Profile"
      component={ProfileStack}
      options={{ tabBarLabel: 'Profile' }}
    />
    </Tab.Navigator>
  );
};

const AppNavigator = () => {
  const { isAuthenticated, user } = useSelector((state: RootState) => state.auth);
  const { isProfileComplete, profile } = useSelector((state: RootState) => state.user);



  return (
    <NavigationContainer>
      {!isAuthenticated ? (
        <AuthStack />
      ) : !isProfileComplete ? (
        <Stack.Navigator screenOptions={{ headerShown: false }}>
          <Stack.Screen name="ProfileSetup" component={ProfileSetupScreen} />
        </Stack.Navigator>
      ) : (
        <MainTabs />
      )}
    </NavigationContainer>
  );
};

export default AppNavigator;

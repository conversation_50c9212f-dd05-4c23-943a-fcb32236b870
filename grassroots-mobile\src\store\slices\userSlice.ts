import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

interface UserProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  profilePhoto?: string;
  bio?: string;
  voiceNote?: string;
  interests: string[];
  vibePrompts: {
    question: string;
    answer: string;
  }[];
  dateOfBirth?: string;
  university: string;
  eventsAttended: number;
  connectionsMade: number;
}

interface UserState {
  profile: UserProfile | null;
  isLoading: boolean;
  error: string | null;
  profileSetupStep: number;
  isProfileComplete: boolean;
}

const initialState: UserState = {
  profile: null,
  isLoading: false,
  error: null,
  profileSetupStep: 0,
  isProfileComplete: false,
};

// Async thunks
export const updateProfile = createAsyncThunk(
  'user/updateProfile',
  async (profileData: Partial<UserProfile>) => {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    return profileData;
  }
);

export const uploadProfilePhoto = createAsyncThunk(
  'user/uploadProfilePhoto',
  async (photoUri: string) => {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    return `https://example.com/photos/${Date.now()}.jpg`;
  }
);

export const uploadVoiceNote = createAsyncThunk(
  'user/uploadVoiceNote',
  async (audioUri: string) => {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    return `https://example.com/audio/${Date.now()}.mp3`;
  }
);

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setProfileSetupStep: (state, action: PayloadAction<number>) => {
      state.profileSetupStep = action.payload;
    },
    updateProfileField: (state, action: PayloadAction<{ field: string; value: any }>) => {
      if (state.profile) {
        (state.profile as any)[action.payload.field] = action.payload.value;
      }
    },
    addVibePrompt: (state, action: PayloadAction<{ question: string; answer: string }>) => {
      if (state.profile) {
        state.profile.vibePrompts.push(action.payload);
      }
    },
    updateVibePrompt: (state, action: PayloadAction<{ index: number; answer: string }>) => {
      if (state.profile && state.profile.vibePrompts[action.payload.index]) {
        state.profile.vibePrompts[action.payload.index].answer = action.payload.answer;
      }
    },
    addInterest: (state, action: PayloadAction<string>) => {
      if (state.profile && !state.profile.interests.includes(action.payload)) {
        state.profile.interests.push(action.payload);
      }
    },
    removeInterest: (state, action: PayloadAction<string>) => {
      if (state.profile) {
        state.profile.interests = state.profile.interests.filter(
          interest => interest !== action.payload
        );
      }
    },
    initializeProfile: (state, action: PayloadAction<Partial<UserProfile>>) => {
      state.profile = {
        id: action.payload.id || '',
        email: action.payload.email || '',
        firstName: action.payload.firstName || '',
        lastName: action.payload.lastName || '',
        interests: [],
        vibePrompts: [],
        university: action.payload.email?.split('@')[1] || '',
        eventsAttended: 0,
        connectionsMade: 0,
        ...action.payload,
      };
      console.log('Profile initialized:', state.profile);
    },
    clearError: (state) => {
      state.error = null;
    },
    incrementEventsAttended: (state) => {
      if (state.profile) {
        state.profile.eventsAttended += 1;
      }
    },
    incrementConnectionsMade: (state) => {
      if (state.profile) {
        state.profile.connectionsMade += 1;
      }
    },
    updateAnalytics: (state, action: PayloadAction<{ eventsAttended?: number; connectionsMade?: number }>) => {
      if (state.profile) {
        if (action.payload.eventsAttended !== undefined) {
          state.profile.eventsAttended = action.payload.eventsAttended;
        }
        if (action.payload.connectionsMade !== undefined) {
          state.profile.connectionsMade = action.payload.connectionsMade;
        }
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Update Profile
      .addCase(updateProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        if (state.profile) {
          state.profile = { ...state.profile, ...action.payload };
        }
        // Check if profile is complete
        state.isProfileComplete = !!(
          state.profile?.profilePhoto &&
          state.profile?.bio &&
          state.profile?.interests.length > 0 &&
          state.profile?.vibePrompts.length >= 3
        );
        console.log('Profile completion status:', state.isProfileComplete);
      })
      .addCase(updateProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to update profile';
      })
      // Upload Profile Photo
      .addCase(uploadProfilePhoto.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(uploadProfilePhoto.fulfilled, (state, action) => {
        state.isLoading = false;
        if (state.profile) {
          state.profile.profilePhoto = action.payload;
        }
      })
      .addCase(uploadProfilePhoto.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to upload photo';
      })
      // Upload Voice Note
      .addCase(uploadVoiceNote.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(uploadVoiceNote.fulfilled, (state, action) => {
        state.isLoading = false;
        if (state.profile) {
          state.profile.voiceNote = action.payload;
        }
      })
      .addCase(uploadVoiceNote.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to upload voice note';
      });
  },
});

export const {
  setProfileSetupStep,
  updateProfileField,
  addVibePrompt,
  updateVibePrompt,
  addInterest,
  removeInterest,
  initializeProfile,
  clearError,
  incrementEventsAttended,
  incrementConnectionsMade,
  updateAnalytics,
} = userSlice.actions;

export default userSlice.reducer;

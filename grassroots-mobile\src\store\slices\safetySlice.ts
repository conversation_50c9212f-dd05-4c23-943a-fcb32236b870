import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// Types for Phase 5 - Safety & Moderation
export interface SafetyReport {
  id: string;
  reporterId: string;
  reportedUserId: string;
  reportedUserName: string;
  reportedUserPhoto: string;
  eventId?: string;
  eventTitle?: string;
  reportType: 'harassment' | 'inappropriate_behavior' | 'fake_profile' | 'safety_concern' | 'other';
  description: string;
  evidence?: {
    screenshots: string[];
    chatLogs: string[];
  };
  status: 'pending' | 'under_review' | 'resolved' | 'dismissed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: string;
  updatedAt: string;
  moderatorNotes?: string;
  actionTaken?: string;
}

export interface WallOfShameEntry {
  id: string;
  userId: string;
  userName: string;
  userPhoto: string;
  university: string;
  reportCount: number;
  verifiedReports: number;
  lastIncident: string;
  incidentTypes: string[];
  warningLevel: 'yellow' | 'orange' | 'red';
  isActive: boolean;
  createdAt: string;
}

export interface SafetyAlert {
  id: string;
  type: 'user_warning' | 'event_safety' | 'general_safety';
  title: string;
  message: string;
  severity: 'info' | 'warning' | 'danger';
  targetUserId?: string;
  eventId?: string;
  isRead: boolean;
  createdAt: string;
  expiresAt?: string;
}

export interface BlockedUser {
  id: string;
  userId: string;
  userName: string;
  userPhoto: string;
  blockedAt: string;
  reason?: string;
}

interface SafetyState {
  reports: SafetyReport[];
  wallOfShame: WallOfShameEntry[];
  safetyAlerts: SafetyAlert[];
  blockedUsers: BlockedUser[];
  isLoading: boolean;
  error: string | null;
  reportSubmissionStatus: 'idle' | 'submitting' | 'success' | 'error';
}

const initialState: SafetyState = {
  reports: [],
  wallOfShame: [],
  safetyAlerts: [],
  blockedUsers: [],
  isLoading: false,
  error: null,
  reportSubmissionStatus: 'idle',
};

// Async thunks
export const submitSafetyReport = createAsyncThunk(
  'safety/submitSafetyReport',
  async (report: Omit<SafetyReport, 'id' | 'status' | 'priority' | 'createdAt' | 'updatedAt'>) => {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const newReport: SafetyReport = {
      ...report,
      id: Date.now().toString(),
      status: 'pending',
      priority: 'medium',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    return newReport;
  }
);

export const fetchWallOfShame = createAsyncThunk(
  'safety/fetchWallOfShame',
  async () => {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // Mock data
    const mockWallOfShame: WallOfShameEntry[] = [
      {
        id: 'shame1',
        userId: 'user123',
        userName: 'John D.',
        userPhoto: 'https://i.pravatar.cc/150?img=4',
        university: 'University of California',
        reportCount: 5,
        verifiedReports: 3,
        lastIncident: '2025-07-10T18:30:00Z',
        incidentTypes: ['harassment', 'inappropriate_behavior'],
        warningLevel: 'red',
        isActive: true,
        createdAt: '2025-07-08T10:00:00Z',
      },
      {
        id: 'shame2',
        userId: 'user456',
        userName: 'Mike S.',
        userPhoto: 'https://i.pravatar.cc/150?img=7',
        university: 'Stanford University',
        reportCount: 3,
        verifiedReports: 2,
        lastIncident: '2025-07-09T14:20:00Z',
        incidentTypes: ['fake_profile', 'safety_concern'],
        warningLevel: 'orange',
        isActive: true,
        createdAt: '2025-07-07T16:45:00Z',
      },
    ];
    
    return mockWallOfShame;
  }
);

export const fetchSafetyAlerts = createAsyncThunk(
  'safety/fetchSafetyAlerts',
  async (userId: string) => {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 600));
    
    // Mock data
    const mockAlerts: SafetyAlert[] = [
      {
        id: 'alert1',
        type: 'user_warning',
        title: 'User Safety Alert',
        message: 'A user you recently interacted with has been reported for inappropriate behavior. Please be cautious.',
        severity: 'warning',
        targetUserId: 'user123',
        isRead: false,
        createdAt: '2025-07-11T09:00:00Z',
        expiresAt: '2025-07-18T09:00:00Z',
      },
    ];
    
    return mockAlerts;
  }
);

export const blockUser = createAsyncThunk(
  'safety/blockUser',
  async (blockData: { userId: string; userName: string; userPhoto: string; reason?: string }) => {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const blockedUser: BlockedUser = {
      id: Date.now().toString(),
      ...blockData,
      blockedAt: new Date().toISOString(),
    };
    
    return blockedUser;
  }
);

export const unblockUser = createAsyncThunk(
  'safety/unblockUser',
  async (userId: string) => {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return userId;
  }
);

const safetySlice = createSlice({
  name: 'safety',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    markAlertAsRead: (state, action: PayloadAction<string>) => {
      const alert = state.safetyAlerts.find(a => a.id === action.payload);
      if (alert) {
        alert.isRead = true;
      }
    },
    dismissAlert: (state, action: PayloadAction<string>) => {
      state.safetyAlerts = state.safetyAlerts.filter(a => a.id !== action.payload);
    },
    resetReportSubmissionStatus: (state) => {
      state.reportSubmissionStatus = 'idle';
    },
  },
  extraReducers: (builder) => {
    builder
      // Submit Safety Report
      .addCase(submitSafetyReport.pending, (state) => {
        state.reportSubmissionStatus = 'submitting';
        state.error = null;
      })
      .addCase(submitSafetyReport.fulfilled, (state, action) => {
        state.reportSubmissionStatus = 'success';
        state.reports.push(action.payload);
      })
      .addCase(submitSafetyReport.rejected, (state, action) => {
        state.reportSubmissionStatus = 'error';
        state.error = action.error.message || 'Failed to submit safety report';
      })
      // Fetch Wall of Shame
      .addCase(fetchWallOfShame.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchWallOfShame.fulfilled, (state, action) => {
        state.isLoading = false;
        state.wallOfShame = action.payload;
      })
      .addCase(fetchWallOfShame.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch wall of shame';
      })
      // Fetch Safety Alerts
      .addCase(fetchSafetyAlerts.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchSafetyAlerts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.safetyAlerts = action.payload;
      })
      .addCase(fetchSafetyAlerts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch safety alerts';
      })
      // Block User
      .addCase(blockUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(blockUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.blockedUsers.push(action.payload);
      })
      .addCase(blockUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to block user';
      })
      // Unblock User
      .addCase(unblockUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(unblockUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.blockedUsers = state.blockedUsers.filter(u => u.userId !== action.payload);
      })
      .addCase(unblockUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to unblock user';
      });
  },
});

export const {
  clearError,
  markAlertAsRead,
  dismissAlert,
  resetReportSubmissionStatus,
} = safetySlice.actions;

export default safetySlice.reducer;

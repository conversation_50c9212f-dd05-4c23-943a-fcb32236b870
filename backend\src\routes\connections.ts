import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import {
  createConnection,
  getUserConnections,
  getEventParticipantsForReview,
  sendDirectMessage,
  getDirectMessages
} from '../controllers/connectionController';

const router = Router();

// Protected routes
router.post('/', authenticateToken, createConnection);
router.get('/user', authenticateToken, getUserConnections);
router.get('/event/:eventId/participants', authenticateToken, getEventParticipantsForReview);
router.post('/:connectionId/messages', authenticateToken, sendDirectMessage);
router.get('/:connectionId/messages', authenticateToken, getDirectMessages);

export default router;
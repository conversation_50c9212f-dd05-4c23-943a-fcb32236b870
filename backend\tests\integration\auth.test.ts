import request from 'supertest';
import { app } from '../../src/app';
import { createTestUser, generateTestToken, cleanupDatabase } from '../utils/testHelpers';

describe('Auth Endpoints', () => {
  afterEach(async () => {
    await cleanupDatabase();
  });

  describe('POST /api/auth/refresh', () => {
    it('should refresh access token', async () => {
      const user = await createTestUser();
      const token = generateTestToken(user.id);

      // First generate a refresh token
      const loginResponse = await request(app)
        .post('/api/auth/google')
        .send({
          token: 'mock-google-token',
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName
        });

      const refreshToken = loginResponse.body.refreshToken;

      const response = await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken });

      expect(response.status).toBe(200);
      expect(response.body.accessToken).toBeDefined();
    });

    it('should return 401 for invalid refresh token', async () => {
      const response = await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken: 'invalid-token' });

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Invalid refresh token');
    });
  });

  describe('POST /api/auth/logout', () => {
    it('should logout user and revoke refresh token', async () => {
      const user = await createTestUser();
      const token = generateTestToken(user.id);

      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${token}`)
        .send({ refreshToken: 'some-refresh-token' });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Logged out successfully');
    });
  });
});
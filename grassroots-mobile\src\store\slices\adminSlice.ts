import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface AdminUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'super_admin' | 'event_admin' | 'moderator';
  permissions: AdminPermission[];
  university: string;
  isActive: boolean;
  createdAt: string;
}

export interface AdminPermission {
  id: string;
  name: string;
  description: string;
  category: 'events' | 'users' | 'content' | 'safety' | 'analytics';
}

interface AdminState {
  currentAdmin: AdminUser | null;
  isAdmin: boolean;
  adminPermissions: AdminPermission[];
  isLoading: boolean;
  error: string | null;
}

const initialState: AdminState = {
  currentAdmin: null,
  isAdmin: false,
  adminPermissions: [],
  isLoading: false,
  error: null,
};

// List of admin email domains and specific emails
const ADMIN_EMAIL_DOMAINS = [
  '@vibe-admin.com',
  '@university-admin.edu',
];

const ADMIN_EMAILS = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  // Add specific admin emails here
];

// Mock admin permissions
const mockAdminPermissions: AdminPermission[] = [
  {
    id: 'create_events',
    name: 'Create Events',
    description: 'Can create and publish new events',
    category: 'events'
  },
  {
    id: 'edit_events',
    name: 'Edit Events',
    description: 'Can modify existing events',
    category: 'events'
  },
  {
    id: 'delete_events',
    name: 'Delete Events',
    description: 'Can delete events',
    category: 'events'
  },
  {
    id: 'moderate_users',
    name: 'Moderate Users',
    description: 'Can suspend or ban users',
    category: 'users'
  },
  {
    id: 'view_reports',
    name: 'View Reports',
    description: 'Can view safety reports and user complaints',
    category: 'safety'
  },
  {
    id: 'manage_content',
    name: 'Manage Content',
    description: 'Can moderate posts and comments',
    category: 'content'
  },
  {
    id: 'view_analytics',
    name: 'View Analytics',
    description: 'Can access platform analytics and metrics',
    category: 'analytics'
  }
];

// Helper function to check if email is admin
export const isAdminEmail = (email: string): boolean => {
  // Check specific admin emails
  if (ADMIN_EMAILS.includes(email.toLowerCase())) {
    return true;
  }
  
  // Check admin domains
  return ADMIN_EMAIL_DOMAINS.some(domain => 
    email.toLowerCase().endsWith(domain.toLowerCase())
  );
};

// Helper function to get admin role based on email
const getAdminRole = (email: string): AdminUser['role'] => {
  if (email.includes('super') || email.includes('admin@')) {
    return 'super_admin';
  } else if (email.includes('event') || email.includes('events@')) {
    return 'event_admin';
  } else {
    return 'moderator';
  }
};

// Helper function to get permissions based on role
const getPermissionsByRole = (role: AdminUser['role']): AdminPermission[] => {
  switch (role) {
    case 'super_admin':
      return mockAdminPermissions; // All permissions
    case 'event_admin':
      return mockAdminPermissions.filter(p => 
        p.category === 'events' || p.category === 'analytics'
      );
    case 'moderator':
      return mockAdminPermissions.filter(p => 
        p.category === 'safety' || p.category === 'content'
      );
    default:
      return [];
  }
};

// Async thunks
export const checkAdminStatus = createAsyncThunk(
  'admin/checkAdminStatus',
  async (email: string) => {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    if (!isAdminEmail(email)) {
      throw new Error('Not an admin email');
    }
    
    const role = getAdminRole(email);
    const permissions = getPermissionsByRole(role);
    
    const adminUser: AdminUser = {
      id: 'admin_' + Date.now(),
      email,
      firstName: 'Admin',
      lastName: 'User',
      role,
      permissions,
      university: 'VIBE Platform',
      isActive: true,
      createdAt: new Date().toISOString(),
    };
    
    return adminUser;
  }
);

export const validateAdminAction = createAsyncThunk(
  'admin/validateAdminAction',
  async ({ action, adminId }: { action: string; adminId: string }) => {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // Mock validation - in real app, this would check against backend
    return { action, isValid: true };
  }
);

const adminSlice = createSlice({
  name: 'admin',
  initialState,
  reducers: {
    clearAdminStatus: (state) => {
      state.currentAdmin = null;
      state.isAdmin = false;
      state.adminPermissions = [];
      state.error = null;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Check admin status
      .addCase(checkAdminStatus.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(checkAdminStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentAdmin = action.payload;
        state.isAdmin = true;
        state.adminPermissions = action.payload.permissions;
      })
      .addCase(checkAdminStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.isAdmin = false;
        state.currentAdmin = null;
        state.adminPermissions = [];
        state.error = action.error.message || 'Failed to verify admin status';
      })
      
      // Validate admin action
      .addCase(validateAdminAction.fulfilled, (state, action) => {
        // Handle successful validation
        console.log('Admin action validated:', action.payload);
      })
      .addCase(validateAdminAction.rejected, (state, action) => {
        state.error = action.error.message || 'Admin action not authorized';
      });
  },
});

export const { clearAdminStatus, clearError } = adminSlice.actions;
export default adminSlice.reducer;

import { Request, Response } from 'express';
import { adminService } from '../services/adminService';
import { AuthRequest } from '../middleware/auth';
import { logger } from '../utils/logger';

export const getDashboardStats = async (req: Request, res: Response) => {
  try {
    const stats = await adminService.getDashboardStats();
    res.json(stats);
  } catch (error: any) {
    logger.error('Get dashboard stats error:', error);
    res.status(500).json({ error: 'Failed to fetch dashboard stats' });
  }
};

export const getUsers = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const search = req.query.search as string;
    const status = req.query.status as string;

    const result = await adminService.getUsers(page, limit, search, status);
    res.json(result);
  } catch (error: any) {
    logger.error('Get users error:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
};

export const getUserDetails = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const user = await adminService.getUserDetails(userId);
    res.json({ user });
  } catch (error: any) {
    logger.error('Get user details error:', error);
    
    if (error.message === 'User not found') {
      return res.status(404).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to fetch user details' });
  }
};

export const updateUserStatus = async (req: AuthRequest, res: Response) => {
  try {
    const { userId } = req.params;
    const { isActive, reason } = req.body;

    const user = await adminService.updateUserStatus(userId, isActive, reason);
    res.json({ user, message: 'User status updated successfully' });
  } catch (error: any) {
    logger.error('Update user status error:', error);
    res.status(500).json({ error: 'Failed to update user status' });
  }
};

export const getEventAnalytics = async (req: Request, res: Response) => {
  try {
    const { eventId } = req.params;
    const analytics = await adminService.getEventAnalytics(eventId);
    res.json(analytics);
  } catch (error: any) {
    logger.error('Get event analytics error:', error);
    
    if (error.message === 'Event not found') {
      return res.status(404).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to fetch event analytics' });
  }
};

export const getSystemHealth = async (req: Request, res: Response) => {
  try {
    const health = await adminService.getSystemHealth();
    res.json(health);
  } catch (error: any) {
    logger.error('Get system health error:', error);
    res.status(500).json({ error: 'Failed to fetch system health' });
  }
};
import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useDispatch, useSelector } from 'react-redux';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { AppDispatch, RootState } from '../../store';
import { fetchEvents, optInToEvent } from '../../store/slices/eventsSlice';
import { incrementEventsAttended } from '../../store/slices/userSlice';
import { fetchNotifications } from '../../store/slices/notificationsSlice';
import { But<PERSON>, Card } from '../../components';
import GradientBackground from '../../components/GradientBackground';
import { useTheme } from '../../contexts/ThemeContext';
import { createTypographyStyles } from '../../utils/styles';

const { width } = Dimensions.get('window');

interface HomeScreenProps {
  navigation: any;
}

const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { featuredEvents, isLoading } = useSelector((state: RootState) => state.events);
  const { profile } = useSelector((state: RootState) => state.user);
  const { mutualConnections } = useSelector((state: RootState) => state.connections);
  const { unreadCount } = useSelector((state: RootState) => state.notifications);
  const { theme } = useTheme();
  const styles = createStyles(theme);

  useEffect(() => {
    dispatch(fetchEvents({}));
    dispatch(fetchNotifications());
  }, [dispatch]);

  const handleOptIn = (eventId: string) => {
    dispatch(optInToEvent(eventId));
    // Update user analytics when joining an event
    dispatch(incrementEventsAttended());
  };

  const handleNotifications = () => {
    navigation.navigate('Notifications');
  };

  const handleCreateEvent = () => {
    navigation.navigate('CreateEvent');
  };

  const handleConnections = () => {
    // Navigate to Profile tab, then to Connections screen
    navigation.getParent()?.navigate('Profile', { screen: 'Connections' });
  };

  const renderEventCard = (event: any) => (
    <TouchableOpacity
      key={event.id}
      style={styles.eventCard}
      onPress={() => navigation.navigate('EventDetail', { event })}
      activeOpacity={0.9}
    >
      <Image source={{ uri: event.imageUrl }} style={styles.eventImage} />
      <LinearGradient
        colors={['transparent', 'rgba(0,0,0,0.8)']}
        style={styles.eventGradient}
      >
        <View style={styles.eventContent}>
          <View style={styles.eventBadge}>
            <Text style={styles.eventBadgeText}>
              {event.eventType === 'curated' ? '✨ VIBE Curated' : '👥 Community'}
            </Text>
          </View>
          <Text style={styles.eventTitle}>{event.title}</Text>
          <Text style={styles.eventLocation}>📍 {event.location.name}</Text>
          <Text style={styles.eventTime}>
            🕐 {new Date(event.startTime).toLocaleDateString()} at{' '}
            {new Date(event.startTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </Text>
          <View style={styles.eventFooter}>
            <Text style={styles.eventAttendees}>
              {event.currentAttendees}/{event.capacity} joined
            </Text>
            <Button
              title={event.isOptedIn ? '✓ Joined' : 'Join'}
              onPress={() => handleOptIn(event.id)}
              variant={event.isOptedIn ? 'secondary' : 'primary'}
              size="small"
              style={styles.optInButton}
            />
          </View>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );

  return (
    <GradientBackground variant="primary" style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <GradientBackground variant="secondary" style={styles.header}>
          <View style={styles.headerContent}>
            <View>
              <Text style={styles.greeting}>
                Hey {profile?.firstName || 'there'}! 👋
              </Text>
              <Text style={styles.tagline}>Ready to vibe with new people?</Text>
            </View>
            <TouchableOpacity style={styles.notificationButton} onPress={handleNotifications}>
              <Ionicons name="notifications-outline" size={24} color={theme.colors.text.secondary} />
              {unreadCount > 0 && (
                <View style={styles.notificationBadge}>
                  <Text style={styles.notificationBadgeText}>
                    {unreadCount > 99 ? '99+' : unreadCount}
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          </View>
        </GradientBackground>

        {/* Quick Stats */}
        <View style={styles.statsContainer}>
          <Card variant="default" padding="medium" style={styles.statCard}>
            <View style={styles.statIconContainer}>
              <Ionicons name="calendar" size={16} color={theme.colors.primary[500]} />
            </View>
            <Text style={styles.statNumber}>{profile?.eventsAttended || 0}</Text>
            <Text style={styles.statLabel}>Events</Text>
          </Card>
          <Card variant="default" padding="medium" style={styles.statCard}>
            <View style={styles.statIconContainer}>
              <Ionicons name="people" size={16} color={theme.colors.accent.blue} />
            </View>
            <Text style={styles.statNumber}>{profile?.connectionsMade || 0}</Text>
            <Text style={styles.statLabel}>Connections</Text>
          </Card>
          <Card variant="default" padding="medium" style={styles.statCard}>
            <View style={styles.statIconContainer}>
              <Ionicons name="trending-up" size={16} color={theme.colors.secondary[500]} />
            </View>
            <Text style={styles.statNumber}>{mutualConnections?.length || 0}</Text>
            <Text style={styles.statLabel}>This Week</Text>
          </Card>
        </View>

        {/* Featured Events */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>🔥 Trending Events</Text>
            <TouchableOpacity onPress={() => navigation.navigate('Events')}>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>

          {isLoading ? (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>Loading amazing events...</Text>
            </View>
          ) : (
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.eventsScrollContainer}
            >
              {featuredEvents.map(renderEventCard)}
            </ScrollView>
          )}
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>⚡ Quick Actions</Text>
          <View style={styles.quickActions}>
            <TouchableOpacity style={styles.actionCard} onPress={handleCreateEvent}>
              <Ionicons name="add-circle" size={32} color="#2E8B57" />
              <Text style={styles.actionText}>Create Event</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionCard}
              onPress={() => navigation.getParent()?.navigate('Chat')}
            >
              <Ionicons name="chatbubbles" size={32} color="#FF6B35" />
              <Text style={styles.actionText}>Group Chats</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionCard} onPress={handleConnections}>
              <Ionicons name="heart" size={32} color="#E74C3C" />
              <Text style={styles.actionText}>Connections</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Safety Reminder */}
        <View style={styles.safetyCard}>
          <Ionicons name="shield-checkmark" size={24} color="#2E8B57" />
          <View style={styles.safetyContent}>
            <Text style={styles.safetyTitle}>Stay Safe Out There!</Text>
            <Text style={styles.safetyText}>
              Always meet in public places and trust your instincts. Report any concerns immediately.
            </Text>
          </View>
        </View>
        </ScrollView>
      </SafeAreaView>
    </GradientBackground>
  );
};

const createStyles = (theme: any) => {
  const typography = createTypographyStyles(theme);

  return StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: theme.spacing[5],
    paddingVertical: theme.spacing[6],
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  notificationButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: theme.colors.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: theme.colors.accent.pink,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: theme.colors.background.primary,
  },
  notificationBadgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: theme.typography.fontWeight.bold,
    textAlign: 'center',
  },
  greeting: {
    ...typography.h4,
    color: theme.colors.text.primary,
  },
  tagline: {
    ...typography.body1,
    color: theme.colors.text.secondary,
    marginTop: theme.spacing[1],
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: theme.spacing[5],
    paddingVertical: theme.spacing[3],
    backgroundColor: theme.colors.background.primary,
    marginBottom: theme.spacing[3],
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.background.primary,
    marginHorizontal: theme.spacing[1],
    borderRadius: theme.borderRadius.lg,
    height: 80,
    paddingVertical: theme.spacing[2],
    paddingHorizontal: theme.spacing[1],
    overflow: 'hidden',
  },
  statIconContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.spacing[1],
  },
  statNumber: {
    ...typography.h6,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.bold,
    marginBottom: 2,
  },
  statLabel: {
    ...typography.caption,
    color: theme.colors.text.secondary,
    fontSize: 9,
    textAlign: 'center',
    lineHeight: 12,
  },
  section: {
    backgroundColor: theme.colors.background.primary,
    marginBottom: theme.spacing[4],
    paddingVertical: theme.spacing[6],
    borderRadius: theme.borderRadius.lg,
    marginHorizontal: theme.spacing[5],
    ...theme.shadows.base,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: theme.spacing[5],
    marginBottom: theme.spacing[4],
  },
  sectionTitle: {
    ...typography.h5,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.bold,
  },
  seeAllText: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.primary[500],
    fontWeight: theme.typography.fontWeight.semibold,
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  eventsScrollContainer: {
    paddingLeft: 20,
  },
  eventCard: {
    width: width * 0.8,
    height: 200,
    marginRight: 15,
    borderRadius: 16,
    overflow: 'hidden',
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  eventImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  eventGradient: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  eventContent: {
    padding: 15,
  },
  eventBadge: {
    alignSelf: 'flex-start',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 8,
  },
  eventBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#333',
  },
  eventTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 4,
  },
  eventLocation: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 2,
  },
  eventTime: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 10,
  },
  eventFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  eventAttendees: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  optInButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  optInButtonActive: {
    backgroundColor: '#2E8B57',
    borderColor: '#2E8B57',
  },
  optInButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  optInButtonTextActive: {
    color: '#fff',
  },
  quickActions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
  },
  actionCard: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 20,
    backgroundColor: '#f8f9fa',
    marginHorizontal: 5,
    borderRadius: 12,
  },
  actionText: {
    fontSize: 14,
    color: '#333',
    marginTop: 8,
    fontWeight: '600',
  },
  safetyCard: {
    flexDirection: 'row',
    backgroundColor: '#E8F5E8',
    marginHorizontal: 20,
    marginBottom: 20,
    padding: 15,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#2E8B57',
  },
  safetyContent: {
    flex: 1,
    marginLeft: 12,
  },
  safetyTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2E8B57',
    marginBottom: 4,
  },
  safetyText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  });
};

export default HomeScreen;

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  TextInput,
  Platform,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import * as ImagePicker from 'expo-image-picker';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../store';
import { GradientBackground, Card, Button, Input } from '../../components';
import { useTheme } from '../../contexts/ThemeContext';
import { createTypographyStyles } from '../../utils/styles';
import { checkAdminStatus, isAdminEmail } from '../../store/slices/adminSlice';

interface CreateEventScreenProps {
  navigation: any;
}

interface EventFormData {
  title: string;
  description: string;
  category: string;
  date: Date | null;
  time: Date | null;
  location: string;
  capacity: string;
  isPrivate: boolean;
  image: string | null;
}

const categories = [
  { id: 'social', name: 'Social', icon: 'people', color: '#FF6B35' },
  { id: 'study', name: 'Study', icon: 'book', color: '#4ECDC4' },
  { id: 'sports', name: 'Sports', icon: 'fitness', color: '#45B7D1' },
  { id: 'food', name: 'Food', icon: 'restaurant', color: '#96CEB4' },
  { id: 'entertainment', name: 'Entertainment', icon: 'musical-notes', color: '#FFEAA7' },
  { id: 'networking', name: 'Networking', icon: 'business', color: '#DDA0DD' },
];

const CreateEventScreen: React.FC<CreateEventScreenProps> = ({ navigation }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const { isAdmin, currentAdmin, isLoading: adminLoading } = useSelector((state: RootState) => state.admin);
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const typography = createTypographyStyles(theme);

  const [formData, setFormData] = useState<EventFormData>({
    title: '',
    description: '',
    category: '',
    date: null,
    time: null,
    location: '',
    capacity: '',
    isPrivate: false,
    image: null,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [hasCheckedAdmin, setHasCheckedAdmin] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);

  // Check admin status when component mounts
  useEffect(() => {
    if (user?.email && !hasCheckedAdmin) {
      if (isAdminEmail(user.email)) {
        dispatch(checkAdminStatus(user.email));
      }
      setHasCheckedAdmin(true);
    }
  }, [user?.email, dispatch, hasCheckedAdmin]);

  // Show loading while checking admin status
  if (!hasCheckedAdmin || adminLoading) {
    return (
      <GradientBackground variant="primary" style={styles.container}>
        <SafeAreaView style={styles.safeArea}>
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Checking permissions...</Text>
          </View>
        </SafeAreaView>
      </GradientBackground>
    );
  }

  // Show access denied if not admin
  if (!isAdmin) {
    return (
      <GradientBackground variant="primary" style={styles.container}>
        <SafeAreaView style={styles.safeArea}>
          <View style={styles.accessDeniedContainer}>
            <Ionicons name="lock-closed" size={64} color={theme.colors.text.tertiary} />
            <Text style={styles.accessDeniedTitle}>Access Restricted</Text>
            <Text style={styles.accessDeniedText}>
              Only authorized administrators can create events. If you believe you should have access, please contact support.
            </Text>
            <Button
              title="Go Back"
              onPress={() => navigation.goBack()}
              variant="secondary"
              size="medium"
              style={styles.goBackButton}
            />
          </View>
        </SafeAreaView>
      </GradientBackground>
    );
  }

  const updateFormData = (field: keyof EventFormData, value: string | boolean | Date | null) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const pickImage = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Sorry, we need camera roll permissions to upload images.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [16, 9],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        updateFormData('image', result.assets[0].uri);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick image');
    }
  };

  const takePhoto = async () => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Sorry, we need camera permissions to take photos.');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [16, 9],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        updateFormData('image', result.assets[0].uri);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to take photo');
    }
  };

  const showImagePicker = () => {
    Alert.alert(
      'Select Image',
      'Choose how you want to add an image',
      [
        { text: 'Camera', onPress: takePhoto },
        { text: 'Gallery', onPress: pickImage },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const validateForm = () => {
    if (!formData.title.trim()) {
      Alert.alert('Error', 'Please enter an event title');
      return false;
    }
    if (!formData.description.trim()) {
      Alert.alert('Error', 'Please enter an event description');
      return false;
    }
    if (!formData.category) {
      Alert.alert('Error', 'Please select a category');
      return false;
    }
    if (!formData.date) {
      Alert.alert('Error', 'Please select a date');
      return false;
    }
    if (!formData.time) {
      Alert.alert('Error', 'Please select a time');
      return false;
    }
    if (!formData.location.trim()) {
      Alert.alert('Error', 'Please enter a location');
      return false;
    }
    if (!formData.capacity || parseInt(formData.capacity) < 2) {
      Alert.alert('Error', 'Please enter a valid capacity (minimum 2 people)');
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      Alert.alert(
        'Event Created!',
        'Your event has been submitted for review. You\'ll be notified once it\'s approved.',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to create event. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDatePicker = () => {
    setShowDatePicker(true);
  };

  const handleTimePicker = () => {
    setShowTimePicker(true);
  };

  const onDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      updateFormData('date', selectedDate);
    }
  };

  const onTimeChange = (event: any, selectedTime?: Date) => {
    setShowTimePicker(Platform.OS === 'ios');
    if (selectedTime) {
      updateFormData('time', selectedTime);
    }
  };

  const formatDate = (date: Date | null) => {
    if (!date) return 'Select Date';
    return date.toLocaleDateString();
  };

  const formatTime = (time: Date | null) => {
    if (!time) return 'Select Time';
    return time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const renderCategoryOption = (category: any) => (
    <TouchableOpacity
      key={category.id}
      style={[
        styles.categoryOption,
        formData.category === category.id && styles.categoryOptionSelected
      ]}
      onPress={() => updateFormData('category', category.id)}
    >
      <View style={[styles.categoryIcon, { backgroundColor: `${category.color}20` }]}>
        <Ionicons name={category.icon as any} size={24} color={category.color} />
      </View>
      <Text style={[
        styles.categoryText,
        formData.category === category.id && styles.categoryTextSelected
      ]}>
        {category.name}
      </Text>
    </TouchableOpacity>
  );

  return (
    <GradientBackground variant="primary" style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color={theme.colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Create Event</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <View style={styles.form}>
            {/* Event Title */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Event Title</Text>
              <Input
                placeholder="What's your event called?"
                value={formData.title}
                onChangeText={(text) => updateFormData('title', text)}
                maxLength={50}
              />
            </View>

            {/* Event Description */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Description</Text>
              <TextInput
                style={styles.textArea}
                placeholder="Tell people what your event is about..."
                value={formData.description}
                onChangeText={(text) => updateFormData('description', text)}
                multiline
                numberOfLines={4}
                maxLength={500}
                placeholderTextColor={theme.colors.text.tertiary}
              />
            </View>

            {/* Category Selection */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Category</Text>
              <View style={styles.categoriesGrid}>
                {categories.map(renderCategoryOption)}
              </View>
            </View>

            {/* Event Image */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Event Image</Text>
              <TouchableOpacity style={styles.imageUploadContainer} onPress={showImagePicker}>
                {formData.image ? (
                  <View style={styles.imagePreviewContainer}>
                    <Image source={{ uri: formData.image }} style={styles.imagePreview} />
                    <View style={styles.imageOverlay}>
                      <Ionicons name="camera" size={24} color="#FFFFFF" />
                      <Text style={styles.imageOverlayText}>Change Image</Text>
                    </View>
                  </View>
                ) : (
                  <View style={styles.imagePlaceholder}>
                    <Ionicons name="camera" size={32} color={theme.colors.text.tertiary} />
                    <Text style={styles.imagePlaceholderText}>Add Event Image</Text>
                    <Text style={styles.imagePlaceholderSubtext}>Tap to select from gallery or take photo</Text>
                  </View>
                )}
              </TouchableOpacity>
            </View>

            {/* Date and Time */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>When</Text>
              <View style={styles.dateTimeRow}>
                <TouchableOpacity style={styles.dateTimeButton} onPress={handleDatePicker}>
                  <Ionicons name="calendar" size={20} color={theme.colors.primary[500]} />
                  <Text style={styles.dateTimeText}>
                    {formatDate(formData.date)}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.dateTimeButton} onPress={handleTimePicker}>
                  <Ionicons name="time" size={20} color={theme.colors.primary[500]} />
                  <Text style={styles.dateTimeText}>
                    {formatTime(formData.time)}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Location */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Location</Text>
              <Input
                placeholder="Where will this happen?"
                value={formData.location}
                onChangeText={(text) => updateFormData('location', text)}
                icon="location"
              />
            </View>

            {/* Capacity */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Capacity</Text>
              <Input
                placeholder="How many people can join?"
                value={formData.capacity}
                onChangeText={(text) => updateFormData('capacity', text)}
                keyboardType="numeric"
                icon="people"
              />
            </View>

            {/* Privacy Toggle */}
            <View style={styles.section}>
              <TouchableOpacity
                style={styles.privacyToggle}
                onPress={() => updateFormData('isPrivate', !formData.isPrivate)}
              >
                <View style={styles.privacyInfo}>
                  <Text style={styles.privacyTitle}>Private Event</Text>
                  <Text style={styles.privacyDescription}>
                    Only people you invite can see and join this event
                  </Text>
                </View>
                <View style={[
                  styles.toggle,
                  formData.isPrivate && styles.toggleActive
                ]}>
                  <View style={[
                    styles.toggleThumb,
                    formData.isPrivate && styles.toggleThumbActive
                  ]} />
                </View>
              </TouchableOpacity>
            </View>

            {/* Submit Button */}
            <View style={styles.submitSection}>
              <Button
                title="Create Event"
                onPress={handleSubmit}
                variant="primary"
                size="large"
                loading={isLoading}
                disabled={isLoading}
                icon="add-circle"
                iconPosition="left"
                fullWidth
              />
              <Text style={styles.submitNote}>
                Your event will be reviewed before being published to ensure it meets our community guidelines.
              </Text>
            </View>
          </View>
        </ScrollView>

        {/* Date Picker */}
        {showDatePicker && (
          <DateTimePicker
            value={formData.date || new Date()}
            mode="date"
            display="default"
            onChange={onDateChange}
            minimumDate={new Date()}
          />
        )}

        {/* Time Picker */}
        {showTimePicker && (
          <DateTimePicker
            value={formData.time || new Date()}
            mode="time"
            display="default"
            onChange={onTimeChange}
          />
        )}
      </SafeAreaView>
    </GradientBackground>
  );
};

const createStyles = (theme: any) => {
  const typography = createTypographyStyles(theme);
  
  return StyleSheet.create({
    container: {
      flex: 1,
    },
    safeArea: {
      flex: 1,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: theme.spacing[5],
      paddingVertical: theme.spacing[4],
      backgroundColor: theme.colors.background.primary,
    },
    backButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: theme.colors.background.secondary,
      justifyContent: 'center',
      alignItems: 'center',
    },
    headerTitle: {
      ...typography.h4,
      color: theme.colors.text.primary,
      fontWeight: theme.typography.fontWeight.bold,
    },
    placeholder: {
      width: 40,
    },
    scrollView: {
      flex: 1,
    },
    form: {
      padding: theme.spacing[5],
    },
    section: {
      marginBottom: theme.spacing[6],
    },
    sectionTitle: {
      ...typography.h6,
      color: theme.colors.text.primary,
      fontWeight: theme.typography.fontWeight.bold,
      marginBottom: theme.spacing[3],
    },
    textArea: {
      backgroundColor: theme.colors.background.card,
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing[4],
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.text.primary,
      textAlignVertical: 'top',
      minHeight: 100,
      borderWidth: 1,
      borderColor: theme.colors.border.light,
    },
    categoriesGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: theme.spacing[3],
    },
    categoryOption: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.background.card,
      paddingHorizontal: theme.spacing[4],
      paddingVertical: theme.spacing[3],
      borderRadius: theme.borderRadius.lg,
      borderWidth: 1,
      borderColor: theme.colors.border.light,
      minWidth: '45%',
    },
    categoryOptionSelected: {
      borderColor: theme.colors.primary[500],
      backgroundColor: `${theme.colors.primary[500]}10`,
    },
    categoryIcon: {
      width: 32,
      height: 32,
      borderRadius: 16,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: theme.spacing[2],
    },
    categoryText: {
      ...typography.body2,
      color: theme.colors.text.secondary,
      fontWeight: theme.typography.fontWeight.medium,
    },
    categoryTextSelected: {
      color: theme.colors.primary[500],
      fontWeight: theme.typography.fontWeight.bold,
    },
    dateTimeRow: {
      flexDirection: 'row',
      gap: theme.spacing[3],
    },
    dateTimeButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.background.card,
      paddingHorizontal: theme.spacing[4],
      paddingVertical: theme.spacing[4],
      borderRadius: theme.borderRadius.lg,
      borderWidth: 1,
      borderColor: theme.colors.border.light,
    },
    dateTimeText: {
      ...typography.body2,
      color: theme.colors.text.primary,
      marginLeft: theme.spacing[2],
    },
    privacyToggle: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: theme.colors.background.card,
      padding: theme.spacing[4],
      borderRadius: theme.borderRadius.lg,
      borderWidth: 1,
      borderColor: theme.colors.border.light,
    },
    privacyInfo: {
      flex: 1,
    },
    privacyTitle: {
      ...typography.body1,
      color: theme.colors.text.primary,
      fontWeight: theme.typography.fontWeight.semibold,
      marginBottom: theme.spacing[1],
    },
    privacyDescription: {
      ...typography.body2,
      color: theme.colors.text.secondary,
    },
    toggle: {
      width: 50,
      height: 30,
      borderRadius: 15,
      backgroundColor: theme.colors.background.tertiary,
      justifyContent: 'center',
      paddingHorizontal: 2,
    },
    toggleActive: {
      backgroundColor: theme.colors.primary[500],
    },
    toggleThumb: {
      width: 26,
      height: 26,
      borderRadius: 13,
      backgroundColor: theme.colors.background.primary,
      alignSelf: 'flex-start',
    },
    toggleThumbActive: {
      alignSelf: 'flex-end',
    },
    submitSection: {
      marginTop: theme.spacing[4],
    },
    submitNote: {
      ...typography.caption,
      color: theme.colors.text.tertiary,
      textAlign: 'center',
      marginTop: theme.spacing[3],
      lineHeight: 16,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      ...typography.body1,
      color: theme.colors.text.secondary,
    },
    accessDeniedContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: theme.spacing[8],
    },
    accessDeniedTitle: {
      ...typography.h4,
      color: theme.colors.text.primary,
      marginTop: theme.spacing[4],
      marginBottom: theme.spacing[3],
      textAlign: 'center',
    },
    accessDeniedText: {
      ...typography.body1,
      color: theme.colors.text.secondary,
      textAlign: 'center',
      lineHeight: 24,
      marginBottom: theme.spacing[6],
    },
    goBackButton: {
      minWidth: 120,
    },
    imageUploadContainer: {
      borderRadius: theme.borderRadius.lg,
      borderWidth: 1,
      borderColor: theme.colors.border.light,
      borderStyle: 'dashed',
      overflow: 'hidden',
    },
    imagePreviewContainer: {
      position: 'relative',
      height: 200,
    },
    imagePreview: {
      width: '100%',
      height: '100%',
      resizeMode: 'cover',
    },
    imageOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    imageOverlayText: {
      color: '#FFFFFF',
      fontSize: theme.typography.fontSize.sm,
      fontWeight: theme.typography.fontWeight.medium,
      marginTop: theme.spacing[2],
    },
    imagePlaceholder: {
      height: 200,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: theme.colors.background.secondary,
    },
    imagePlaceholderText: {
      ...typography.body1,
      color: theme.colors.text.primary,
      fontWeight: theme.typography.fontWeight.medium,
      marginTop: theme.spacing[3],
    },
    imagePlaceholderSubtext: {
      ...typography.body2,
      color: theme.colors.text.tertiary,
      textAlign: 'center',
      marginTop: theme.spacing[1],
    },
  });
};

export default CreateEventScreen;

import { InteractionManager, Platform } from 'react-native';

// Performance monitoring utilities
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number> = new Map();
  private timers: Map<string, number> = new Map();

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  // Start timing an operation
  startTimer(name: string): void {
    this.timers.set(name, Date.now());
  }

  // End timing and record metric
  endTimer(name: string): number {
    const startTime = this.timers.get(name);
    if (!startTime) {
      console.warn(`Timer ${name} was not started`);
      return 0;
    }

    const duration = Date.now() - startTime;
    this.metrics.set(name, duration);
    this.timers.delete(name);

    if (__DEV__) {
      console.log(`⏱️ ${name}: ${duration}ms`);
    }

    return duration;
  }

  // Get recorded metrics
  getMetrics(): Record<string, number> {
    return Object.fromEntries(this.metrics);
  }

  // Clear all metrics
  clearMetrics(): void {
    this.metrics.clear();
    this.timers.clear();
  }

  // Log performance summary
  logSummary(): void {
    if (__DEV__) {
      console.log('📊 Performance Summary:', this.getMetrics());
    }
  }
}

// Memory management utilities
export const MemoryUtils = {
  // Force garbage collection (development only)
  forceGC: () => {
    if (__DEV__ && global.gc) {
      global.gc();
      console.log('🗑️ Forced garbage collection');
    }
  },

  // Monitor memory usage
  logMemoryUsage: () => {
    if (__DEV__ && Platform.OS === 'android') {
      // Android-specific memory monitoring
      console.log('📱 Memory monitoring available on Android');
    }
  },

  // Clean up large objects
  cleanup: (objects: any[]) => {
    objects.forEach(obj => {
      if (obj && typeof obj === 'object') {
        Object.keys(obj).forEach(key => {
          delete obj[key];
        });
      }
    });
  },
};

// Image optimization utilities
export const ImageOptimizer = {
  // Get optimized image dimensions
  getOptimizedDimensions: (
    originalWidth: number,
    originalHeight: number,
    maxWidth: number = 800,
    maxHeight: number = 600
  ) => {
    const aspectRatio = originalWidth / originalHeight;
    
    let width = originalWidth;
    let height = originalHeight;

    if (width > maxWidth) {
      width = maxWidth;
      height = width / aspectRatio;
    }

    if (height > maxHeight) {
      height = maxHeight;
      width = height * aspectRatio;
    }

    return {
      width: Math.round(width),
      height: Math.round(height),
    };
  },

  // Generate optimized image URI
  getOptimizedImageUri: (
    originalUri: string,
    width?: number,
    height?: number,
    quality: number = 80
  ) => {
    // This would typically integrate with a CDN or image optimization service
    // For now, return the original URI with query parameters
    const url = new URL(originalUri);
    
    if (width) url.searchParams.set('w', width.toString());
    if (height) url.searchParams.set('h', height.toString());
    url.searchParams.set('q', quality.toString());
    url.searchParams.set('f', 'webp'); // Prefer WebP format
    
    return url.toString();
  },
};

// Network optimization utilities
export const NetworkOptimizer = {
  // Debounce function for API calls
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): ((...args: Parameters<T>) => void) => {
    let timeoutId: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), delay);
    };
  },

  // Throttle function for frequent operations
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): ((...args: Parameters<T>) => void) => {
    let inThrottle: boolean;
    
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },

  // Request deduplication
  createRequestDeduplicator: () => {
    const pendingRequests = new Map<string, Promise<any>>();

    return <T>(key: string, requestFn: () => Promise<T>): Promise<T> => {
      if (pendingRequests.has(key)) {
        return pendingRequests.get(key)!;
      }

      const request = requestFn().finally(() => {
        pendingRequests.delete(key);
      });

      pendingRequests.set(key, request);
      return request;
    };
  },
};

// Animation optimization utilities
export const AnimationOptimizer = {
  // Run animations after interactions complete
  runAfterInteractions: (callback: () => void): void => {
    InteractionManager.runAfterInteractions(callback);
  },

  // Create optimized animation config
  createOptimizedConfig: (duration: number = 250) => ({
    duration,
    useNativeDriver: true,
  }),

  // Batch animations for better performance
  batchAnimations: (animations: (() => void)[]): void => {
    InteractionManager.runAfterInteractions(() => {
      animations.forEach(animation => animation());
    });
  },
};

// Component optimization utilities
export const ComponentOptimizer = {
  // Memoization helper
  memoize: <T extends (...args: any[]) => any>(fn: T): T => {
    const cache = new Map();
    
    return ((...args: any[]) => {
      const key = JSON.stringify(args);
      
      if (cache.has(key)) {
        return cache.get(key);
      }
      
      const result = fn(...args);
      cache.set(key, result);
      
      return result;
    }) as T;
  },

  // Shallow comparison for props
  shallowEqual: (obj1: any, obj2: any): boolean => {
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);

    if (keys1.length !== keys2.length) {
      return false;
    }

    for (let key of keys1) {
      if (obj1[key] !== obj2[key]) {
        return false;
      }
    }

    return true;
  },
};

// Bundle size optimization
export const BundleOptimizer = {
  // Lazy load components
  lazyLoad: <T>(importFn: () => Promise<{ default: T }>): (() => Promise<T>) => {
    let component: T | null = null;
    
    return async (): Promise<T> => {
      if (!component) {
        const module = await importFn();
        component = module.default;
      }
      return component;
    };
  },

  // Dynamic imports for code splitting
  dynamicImport: async <T>(modulePath: string): Promise<T> => {
    try {
      const module = await import(modulePath);
      return module.default || module;
    } catch (error) {
      console.error(`Failed to dynamically import ${modulePath}:`, error);
      throw error;
    }
  },
};

// Performance hooks
export const usePerformanceTimer = (name: string) => {
  const monitor = PerformanceMonitor.getInstance();
  
  return {
    start: () => monitor.startTimer(name),
    end: () => monitor.endTimer(name),
  };
};

// Export singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance();

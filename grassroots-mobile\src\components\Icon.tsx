import React from 'react';
import { ViewStyle } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';

// Icon size presets
export const iconSizes = {
  xs: 12,
  sm: 16,
  md: 20,
  lg: 24,
  xl: 32,
  '2xl': 40,
  '3xl': 48,
} as const;

// Icon color presets function
const getIconColors = (theme: any) => ({
  primary: theme.colors.primary[500],
  secondary: theme.colors.secondary[500],
  accent: theme.colors.accent.blue,
  success: theme.colors.success,
  warning: theme.colors.warning,
  error: theme.colors.error,
  info: theme.colors.info,
  text: theme.colors.text.primary,
  textSecondary: theme.colors.text.secondary,
  textTertiary: theme.colors.text.tertiary,
  inverse: theme.colors.text.inverse,
  disabled: theme.colors.text.placeholder,
});

interface IconProps {
  name: keyof typeof Ionicons.glyphMap;
  size?: keyof typeof iconSizes | number;
  color?: string;
  style?: ViewStyle;
}

const Icon: React.FC<IconProps> = ({
  name,
  size = 'md',
  color = 'text',
  style,
}) => {
  const { theme } = useTheme();
  const iconColors = getIconColors(theme);

  const iconSize = typeof size === 'number' ? size : iconSizes[size];
  const iconColor = typeof color === 'string' && color.startsWith('#')
    ? color
    : iconColors[color as keyof typeof iconColors] || iconColors.text;

  return (
    <Ionicons
      name={name}
      size={iconSize}
      color={iconColor}
      style={style}
    />
  );
};

export default Icon;

import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import {
  getEventGroups,
  getGroupById,
  getUserGroups,
  sendMessage,
  getGroupMessages,
  createGroupsForEvent
} from '../controllers/groupController';

const router = Router();

// Protected routes
router.get('/user', authenticateToken, getUserGroups);
router.get('/event/:eventId', authenticateToken, getEventGroups);
router.get('/:id', authenticateToken, getGroupById);
router.get('/:id/messages', authenticateToken, getGroupMessages);
router.post('/:id/messages', authenticateToken, sendMessage);
router.post('/event/:eventId/create', authenticateToken, createGroupsForEvent);

export default router;
import express from 'express';
import { googleAuth, refreshToken, logout } from '../controllers/authController';
import { validateGoogleAuth, validateRefreshToken } from '../middleware/validation';

const router = express.Router();

router.post('/google', validateGoogleAuth, googleAuth);
router.post('/refresh', validateRefreshToken, refreshToken);
router.post('/logout', logout);

export default router;
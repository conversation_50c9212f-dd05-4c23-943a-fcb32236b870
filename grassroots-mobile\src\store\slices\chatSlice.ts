import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

interface Message {
  _id: string;
  text: string;
  createdAt: string; // Changed from Date to string for serialization
  user: {
    _id: string;
    name: string;
    avatar?: string;
  };
  image?: string;
  video?: string;
  audio?: string;
  system?: boolean;
  sent?: boolean;
  received?: boolean;
  pending?: boolean;
}

interface ChatRoom {
  id: string;
  name: string;
  eventId: string;
  eventTitle: string;
  participants: {
    id: string;
    name: string;
    avatar?: string;
  }[];
  lastMessage?: Message;
  unreadCount: number;
  isActive: boolean;
  createdAt: string;
}

interface ChatState {
  chatRooms: ChatRoom[];
  currentChatRoom: ChatRoom | null;
  messages: { [chatRoomId: string]: Message[] };
  isLoading: boolean;
  isConnected: boolean;
  error: string | null;
  typingUsers: { [chatRoomId: string]: string[] };
}

const initialState: ChatState = {
  chatRooms: [],
  currentChatRoom: null,
  messages: {},
  isLoading: false,
  isConnected: false,
  error: null,
  typingUsers: {},
};

// Mock chat rooms data
const mockChatRooms: ChatRoom[] = [
  {
    id: 'chat1',
    name: 'Coffee & Connections Group',
    eventId: '1',
    eventTitle: 'Coffee & Connections',
    participants: [
      { id: 'user1', name: 'Alex Johnson', avatar: 'https://i.pravatar.cc/150?img=1' },
      { id: 'user2', name: 'Sarah Chen', avatar: 'https://i.pravatar.cc/150?img=2' },
      { id: 'user3', name: 'Mike Rodriguez', avatar: 'https://i.pravatar.cc/150?img=3' },
      { id: 'user4', name: 'Emma Wilson', avatar: 'https://i.pravatar.cc/150?img=4' },
    ],
    unreadCount: 2,
    isActive: true,
    createdAt: '2025-07-11T08:00:00Z',
    lastMessage: {
      _id: 'msg1',
      text: 'Looking forward to meeting everyone tomorrow!',
      createdAt: '2025-07-11T10:30:00Z',
      user: { _id: 'user2', name: 'Sarah Chen' },
    },
  },
  {
    id: 'chat2',
    name: 'Taco Tuesday Group',
    eventId: '2',
    eventTitle: 'Taco Tuesday Fiesta',
    participants: [
      { id: 'user5', name: 'Carlos Martinez', avatar: 'https://i.pravatar.cc/150?img=5' },
      { id: 'user6', name: 'Lisa Park', avatar: 'https://i.pravatar.cc/150?img=6' },
      { id: 'user7', name: 'David Kim', avatar: 'https://i.pravatar.cc/150?img=7' },
    ],
    unreadCount: 0,
    isActive: true,
    createdAt: '2025-07-12T15:00:00Z',
    lastMessage: {
      _id: 'msg2',
      text: 'Should we meet at the entrance?',
      createdAt: '2025-07-12T16:45:00Z',
      user: { _id: 'user5', name: 'Carlos Martinez' },
    },
  },
];

// Mock messages data
const mockMessages: { [key: string]: Message[] } = {
  chat1: [
    {
      _id: 'msg1',
      text: 'Hey everyone! Excited for tomorrow\'s coffee meetup! ☕',
      createdAt: '2025-07-11T09:00:00Z',
      user: { _id: 'user1', name: 'Alex Johnson', avatar: 'https://i.pravatar.cc/150?img=1' },
    },
    {
      _id: 'msg2',
      text: 'Same here! I\'ll be wearing a blue jacket so you can spot me easily 😊',
      createdAt: '2025-07-11T09:15:00Z',
      user: { _id: 'user2', name: 'Sarah Chen', avatar: 'https://i.pravatar.cc/150?img=2' },
    },
    {
      _id: 'msg3',
      text: 'Perfect! I\'ll grab us a table when I arrive',
      createdAt: '2025-07-11T09:30:00Z',
      user: { _id: 'user3', name: 'Mike Rodriguez', avatar: 'https://i.pravatar.cc/150?img=3' },
    },
    {
      _id: 'msg4',
      text: 'Looking forward to meeting everyone tomorrow!',
      createdAt: '2025-07-11T10:30:00Z',
      user: { _id: 'user2', name: 'Sarah Chen', avatar: 'https://i.pravatar.cc/150?img=2' },
    },
  ],
  chat2: [
    {
      _id: 'msg5',
      text: 'Taco Tuesday here we come! 🌮',
      createdAt: '2025-07-12T15:30:00Z',
      user: { _id: 'user5', name: 'Carlos Martinez', avatar: 'https://i.pravatar.cc/150?img=5' },
    },
    {
      _id: 'msg6',
      text: 'I heard they have amazing guacamole!',
      createdAt: '2025-07-12T16:00:00Z',
      user: { _id: 'user6', name: 'Lisa Park', avatar: 'https://i.pravatar.cc/150?img=6' },
    },
    {
      _id: 'msg7',
      text: 'Should we meet at the entrance?',
      createdAt: '2025-07-12T16:45:00Z',
      user: { _id: 'user5', name: 'Carlos Martinez', avatar: 'https://i.pravatar.cc/150?img=5' },
    },
  ],
};

// Async thunks
export const fetchChatRooms = createAsyncThunk(
  'chat/fetchChatRooms',
  async () => {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    return mockChatRooms;
  }
);

export const fetchMessages = createAsyncThunk(
  'chat/fetchMessages',
  async (chatRoomId: string) => {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 500));
    return { chatRoomId, messages: mockMessages[chatRoomId] || [] };
  }
);

export const sendMessage = createAsyncThunk(
  'chat/sendMessage',
  async ({ chatRoomId, message }: { chatRoomId: string; message: Omit<Message, '_id' | 'createdAt'> }) => {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 300));

    const newMessage: Message = {
      ...message,
      _id: Date.now().toString(),
      createdAt: new Date().toISOString(), // Convert to ISO string for serialization
    };

    return { chatRoomId, message: newMessage };
  }
);

const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    setCurrentChatRoom: (state, action: PayloadAction<ChatRoom | null>) => {
      state.currentChatRoom = action.payload;
      if (action.payload) {
        // Mark messages as read
        const chatRoom = state.chatRooms.find(room => room.id === action.payload!.id);
        if (chatRoom) {
          chatRoom.unreadCount = 0;
        }
      }
    },
    addMessage: (state, action: PayloadAction<{ chatRoomId: string; message: Message }>) => {
      const { chatRoomId, message } = action.payload;
      if (!state.messages[chatRoomId]) {
        state.messages[chatRoomId] = [];
      }
      state.messages[chatRoomId].unshift(message);
      
      // Update last message in chat room
      const chatRoom = state.chatRooms.find(room => room.id === chatRoomId);
      if (chatRoom) {
        chatRoom.lastMessage = message;
        if (state.currentChatRoom?.id !== chatRoomId) {
          chatRoom.unreadCount += 1;
        }
      }
    },
    setTypingUsers: (state, action: PayloadAction<{ chatRoomId: string; users: string[] }>) => {
      const { chatRoomId, users } = action.payload;
      state.typingUsers[chatRoomId] = users;
    },
    setConnectionStatus: (state, action: PayloadAction<boolean>) => {
      state.isConnected = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    markMessagesAsRead: (state, action: PayloadAction<string>) => {
      const chatRoomId = action.payload;
      const chatRoom = state.chatRooms.find(room => room.id === chatRoomId);
      if (chatRoom) {
        chatRoom.unreadCount = 0;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Chat Rooms
      .addCase(fetchChatRooms.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchChatRooms.fulfilled, (state, action) => {
        state.isLoading = false;
        state.chatRooms = action.payload;
      })
      .addCase(fetchChatRooms.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch chat rooms';
      })
      // Fetch Messages
      .addCase(fetchMessages.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchMessages.fulfilled, (state, action) => {
        state.isLoading = false;
        const { chatRoomId, messages } = action.payload;
        state.messages[chatRoomId] = messages;
      })
      .addCase(fetchMessages.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch messages';
      })
      // Send Message
      .addCase(sendMessage.fulfilled, (state, action) => {
        const { chatRoomId, message } = action.payload;
        if (!state.messages[chatRoomId]) {
          state.messages[chatRoomId] = [];
        }
        state.messages[chatRoomId].unshift(message);
        
        // Update last message in chat room
        const chatRoom = state.chatRooms.find(room => room.id === chatRoomId);
        if (chatRoom) {
          chatRoom.lastMessage = message;
        }
      });
  },
});

export const {
  setCurrentChatRoom,
  addMessage,
  setTypingUsers,
  setConnectionStatus,
  clearError,
  markMessagesAsRead,
} = chatSlice.actions;

export default chatSlice.reducer;

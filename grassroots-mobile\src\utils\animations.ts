import { Animated, Easing } from 'react-native';

// Animation utility functions for the VIBE app
export class AnimationUtils {
  // Fade animations
  static fadeIn(animatedValue: Animated.Value, duration: number = 250): Animated.CompositeAnimation {
    return Animated.timing(animatedValue, {
      toValue: 1,
      duration,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: true,
    });
  }

  static fadeOut(animatedValue: Animated.Value, duration: number = 250): Animated.CompositeAnimation {
    return Animated.timing(animatedValue, {
      toValue: 0,
      duration,
      easing: Easing.in(Easing.cubic),
      useNativeDriver: true,
    });
  }

  // Scale animations
  static scaleIn(animatedValue: Animated.Value, duration: number = 250): Animated.CompositeAnimation {
    return Animated.timing(animatedValue, {
      toValue: 1,
      duration,
      easing: Easing.out(Easing.back(1.2)),
      useNativeDriver: true,
    });
  }

  static scaleOut(animatedValue: Animated.Value, duration: number = 250): Animated.CompositeAnimation {
    return Animated.timing(animatedValue, {
      toValue: 0,
      duration,
      easing: Easing.in(Easing.back(1.2)),
      useNativeDriver: true,
    });
  }

  // Slide animations
  static slideInUp(animatedValue: Animated.Value, distance: number = 50, duration: number = 250): Animated.CompositeAnimation {
    return Animated.timing(animatedValue, {
      toValue: 0,
      duration,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: true,
    });
  }

  static slideInDown(animatedValue: Animated.Value, distance: number = 50, duration: number = 250): Animated.CompositeAnimation {
    return Animated.timing(animatedValue, {
      toValue: 0,
      duration,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: true,
    });
  }

  static slideInLeft(animatedValue: Animated.Value, distance: number = 50, duration: number = 250): Animated.CompositeAnimation {
    return Animated.timing(animatedValue, {
      toValue: 0,
      duration,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: true,
    });
  }

  static slideInRight(animatedValue: Animated.Value, distance: number = 50, duration: number = 250): Animated.CompositeAnimation {
    return Animated.timing(animatedValue, {
      toValue: 0,
      duration,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: true,
    });
  }

  // Spring animations
  static springIn(animatedValue: Animated.Value): Animated.CompositeAnimation {
    return Animated.spring(animatedValue, {
      toValue: 1,
      tension: 100,
      friction: 8,
      useNativeDriver: true,
    });
  }

  static springOut(animatedValue: Animated.Value): Animated.CompositeAnimation {
    return Animated.spring(animatedValue, {
      toValue: 0,
      tension: 100,
      friction: 8,
      useNativeDriver: true,
    });
  }

  // Pulse animation
  static pulse(animatedValue: Animated.Value, duration: number = 1000): Animated.CompositeAnimation {
    return Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1.1,
          duration: duration / 2,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: duration / 2,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ])
    );
  }

  // Shake animation
  static shake(animatedValue: Animated.Value, intensity: number = 10): Animated.CompositeAnimation {
    return Animated.sequence([
      Animated.timing(animatedValue, { toValue: intensity, duration: 50, useNativeDriver: true }),
      Animated.timing(animatedValue, { toValue: -intensity, duration: 50, useNativeDriver: true }),
      Animated.timing(animatedValue, { toValue: intensity, duration: 50, useNativeDriver: true }),
      Animated.timing(animatedValue, { toValue: -intensity, duration: 50, useNativeDriver: true }),
      Animated.timing(animatedValue, { toValue: 0, duration: 50, useNativeDriver: true }),
    ]);
  }

  // Stagger animation for lists
  static staggerIn(
    animatedValues: Animated.Value[],
    staggerDelay: number = 100,
    duration: number = 250
  ): Animated.CompositeAnimation {
    const animations = animatedValues.map((value, index) =>
      Animated.timing(value, {
        toValue: 1,
        duration,
        delay: index * staggerDelay,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      })
    );

    return Animated.parallel(animations);
  }

  // Combined entrance animation
  static entranceAnimation(
    fadeValue: Animated.Value,
    scaleValue: Animated.Value,
    slideValue: Animated.Value
  ): Animated.CompositeAnimation {
    return Animated.parallel([
      this.fadeIn(fadeValue),
      this.scaleIn(scaleValue),
      this.slideInUp(slideValue),
    ]);
  }

  // Button press animation
  static buttonPress(scaleValue: Animated.Value): {
    onPressIn: () => void;
    onPressOut: () => void;
  } {
    return {
      onPressIn: () => {
        Animated.timing(scaleValue, {
          toValue: 0.96,
          duration: 100,
          useNativeDriver: true,
        }).start();
      },
      onPressOut: () => {
        Animated.timing(scaleValue, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }).start();
      },
    };
  }

  // Card hover animation
  static cardHover(scaleValue: Animated.Value, shadowValue: Animated.Value): {
    onPressIn: () => void;
    onPressOut: () => void;
  } {
    return {
      onPressIn: () => {
        Animated.timing(scaleValue, {
          toValue: 0.98,
          duration: 150,
          useNativeDriver: true,
        }).start();
      },
      onPressOut: () => {
        Animated.timing(scaleValue, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }).start();
      },
    };
  }

  // Loading animation
  static loading(rotateValue: Animated.Value): Animated.CompositeAnimation {
    return Animated.loop(
      Animated.timing(rotateValue, {
        toValue: 1,
        duration: 1000,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    );
  }

  // Success animation
  static success(scaleValue: Animated.Value): Animated.CompositeAnimation {
    return Animated.sequence([
      Animated.timing(scaleValue, {
        toValue: 1.2,
        duration: 200,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(scaleValue, {
        toValue: 1,
        duration: 200,
        easing: Easing.in(Easing.cubic),
        useNativeDriver: true,
      }),
    ]);
  }

  // Error animation
  static error(shakeValue: Animated.Value): Animated.CompositeAnimation {
    return this.shake(shakeValue, 5);
  }
}

// Hook for creating animated values
export const useAnimatedValue = (initialValue: number = 0): Animated.Value => {
  return new Animated.Value(initialValue);
};

// Hook for creating multiple animated values
export const useAnimatedValues = (count: number, initialValue: number = 0): Animated.Value[] => {
  return Array.from({ length: count }, () => new Animated.Value(initialValue));
};

export default AnimationUtils;

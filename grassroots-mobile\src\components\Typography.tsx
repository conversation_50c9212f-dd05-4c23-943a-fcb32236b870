import React from 'react';
import { Text, TextStyle, StyleSheet } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';

// Typography variants
export type TypographyVariant = 
  | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'
  | 'body1' | 'body2' | 'caption' | 'overline'
  | 'button' | 'subtitle1' | 'subtitle2';

// Typography colors
export type TypographyColor = 
  | 'primary' | 'secondary' | 'tertiary' | 'inverse'
  | 'success' | 'warning' | 'error' | 'info'
  | 'link' | 'placeholder';

interface TypographyProps {
  variant?: TypographyVariant;
  color?: TypographyColor;
  align?: 'left' | 'center' | 'right' | 'justify';
  weight?: string;
  children: React.ReactNode;
  numberOfLines?: number;
  style?: TextStyle;
  onPress?: () => void;
}

const Typography: React.FC<TypographyProps> = ({
  variant = 'body1',
  color = 'primary',
  align = 'left',
  weight,
  children,
  numberOfLines,
  style,
  onPress,
}) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const getTextColor = (colorKey: TypographyColor): string => {
    switch (colorKey) {
      case 'primary': return theme.colors.text.primary;
      case 'secondary': return theme.colors.text.secondary;
      case 'tertiary': return theme.colors.text.tertiary;
      case 'inverse': return theme.colors.text.inverse;
      case 'success': return theme.colors.success;
      case 'warning': return theme.colors.warning;
      case 'error': return theme.colors.error;
      case 'info': return theme.colors.info;
      case 'link': return theme.colors.text.link;
      case 'placeholder': return theme.colors.text.placeholder;
      default: return theme.colors.text.primary;
    }
  };

  const textStyle = [
    styles.base,
    styles[variant],
    {
      color: getTextColor(color),
      textAlign: align,
      fontWeight: weight as any || undefined,
    },
    style,
  ];

  return (
    <Text
      style={textStyle}
      numberOfLines={numberOfLines}
      onPress={onPress}
    >
      {children}
    </Text>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  base: {
    fontFamily: theme.typography.fontFamily.regular,
  },
  h1: {
    fontSize: theme.typography.fontSize['4xl'],
    fontWeight: theme.typography.fontWeight.bold,
    lineHeight: theme.typography.fontSize['4xl'] * theme.typography.lineHeight.tight,
  },
  h2: {
    fontSize: theme.typography.fontSize['3xl'],
    fontWeight: theme.typography.fontWeight.bold,
    lineHeight: theme.typography.fontSize['3xl'] * theme.typography.lineHeight.tight,
  },
  h3: {
    fontSize: theme.typography.fontSize['2xl'],
    fontWeight: theme.typography.fontWeight.semibold,
    lineHeight: theme.typography.fontSize['2xl'] * theme.typography.lineHeight.tight,
  },
  h4: {
    fontSize: theme.typography.fontSize.xl,
    fontWeight: theme.typography.fontWeight.semibold,
    lineHeight: theme.typography.fontSize.xl * theme.typography.lineHeight.normal,
  },
  h5: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.medium,
    lineHeight: theme.typography.fontSize.lg * theme.typography.lineHeight.normal,
  },
  h6: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.medium,
    lineHeight: theme.typography.fontSize.base * theme.typography.lineHeight.normal,
  },
  body1: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.normal,
    lineHeight: theme.typography.fontSize.base * theme.typography.lineHeight.normal,
  },
  body2: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.normal,
    lineHeight: theme.typography.fontSize.sm * theme.typography.lineHeight.normal,
  },
  subtitle1: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.medium,
    lineHeight: theme.typography.fontSize.base * theme.typography.lineHeight.normal,
  },
  subtitle2: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.medium,
    lineHeight: theme.typography.fontSize.sm * theme.typography.lineHeight.normal,
  },
  caption: {
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.normal,
    lineHeight: theme.typography.fontSize.xs * theme.typography.lineHeight.normal,
  },
  overline: {
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.medium,
    textTransform: 'uppercase' as any,
    letterSpacing: 1,
    lineHeight: theme.typography.fontSize.xs * theme.typography.lineHeight.normal,
  },
  button: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold,
    lineHeight: theme.typography.fontSize.base * theme.typography.lineHeight.tight,
  },
});

export default Typography;

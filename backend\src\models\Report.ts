export interface Report {
  id: string;
  reporterId: string;
  reportedUserId: string;
  eventId?: string;
  messageId?: string;
  groupId?: string;
  reason: 'HARASSMENT' | 'INAPPROPRIATE_CONTENT' | 'SPAM' | 'FAKE_PROFILE' | 'SAFETY_CONCERN' | 'OTHER';
  description: string;
  status: 'PENDING' | 'REVIEWED' | 'RESOLVED' | 'DISMISSED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  createdAt: Date;
  reviewedAt?: Date;
  reviewedBy?: string;
  resolution?: string;
}

export interface ModerationAction {
  id: string;
  userId: string;
  actionType: 'WARNING' | 'TEMPORARY_BAN' | 'PERMANENT_BAN' | 'CONTENT_REMOVAL';
  reason: string;
  duration?: number; // in hours for temporary bans
  createdBy: string;
  createdAt: Date;
  expiresAt?: Date;
}

export interface CreateReportData {
  reportedUserId: string;
  eventId?: string;
  messageId?: string;
  groupId?: string;
  reason: string;
  description: string;
}
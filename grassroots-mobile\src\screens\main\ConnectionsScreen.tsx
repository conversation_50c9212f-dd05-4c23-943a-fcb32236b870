import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useDispatch, useSelector } from 'react-redux';
import { Ionicons } from '@expo/vector-icons';
import { AppDispatch, RootState } from '../../store';
import {
  fetchMutualConnections,
  updateConnectionStatus,
  MutualConnection,
} from '../../store/slices/connectionsSlice';
import { Card, Button } from '../../components';
import { useTheme } from '../../contexts/ThemeContext';
import { createTypographyStyles } from '../../utils/styles';

interface ConnectionsScreenProps {
  navigation: any;
}

const ConnectionsScreen: React.FC<ConnectionsScreenProps> = ({ navigation }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { mutualConnections, isLoading } = useSelector((state: RootState) => state.connections);
  const { theme } = useTheme();
  const styles = createStyles(theme);

  useEffect(() => {
    dispatch(fetchMutualConnections());
  }, [dispatch]);

  const handleRefresh = () => {
    dispatch(fetchMutualConnections());
  };

  const handleStartChat = (connection: MutualConnection) => {
    if (connection.chatRoomId) {
      navigation.navigate('Chat', { chatRoomId: connection.chatRoomId });
    }
  };

  const handleArchiveConnection = (connectionId: string) => {
    dispatch(updateConnectionStatus({ connectionId, status: 'archived' }));
  };

  const renderConnectionCard = (connection: MutualConnection) => {
    const isNewConnection = new Date(connection.matchedAt) > new Date(Date.now() - 24 * 60 * 60 * 1000);
    
    return (
      <Card key={connection.id} variant="elevated" padding="large" style={styles.connectionCard}>
        <View style={styles.connectionHeader}>
          <View style={styles.connectionInfo}>
            <View style={styles.connectionBadge}>
              <Ionicons
                name={connection.connectionType === 'spark' ? 'heart' : 'people'}
                size={16}
                color={connection.connectionType === 'spark' ? theme.colors.accent.pink : theme.colors.accent.blue}
              />
              <Text style={[
                styles.connectionType,
                { color: connection.connectionType === 'spark' ? theme.colors.accent.pink : theme.colors.accent.blue }
              ]}>
                {connection.connectionType === 'spark' ? 'Spark' : 'Friend'} Match
              </Text>
            </View>
            {isNewConnection && (
              <View style={styles.newBadge}>
                <Text style={styles.newBadgeText}>NEW</Text>
              </View>
            )}
          </View>
          <Text style={styles.matchDate}>
            {new Date(connection.matchedAt).toLocaleDateString()}
          </Text>
        </View>

        <View style={styles.connectionContent}>
          <Image
            source={{ uri: 'https://i.pravatar.cc/150?img=2' }}
            style={styles.connectionPhoto}
          />
          <View style={styles.connectionDetails}>
            <Text style={styles.connectionName}>Sarah Chen</Text>
            <Text style={styles.connectionEvent}>From Coffee & Connections</Text>
            <Text style={styles.connectionDescription}>
              You both rated each other as a {connection.connectionType} connection!
            </Text>
          </View>
        </View>

        <View style={styles.connectionActions}>
          <Button
            title="Start Chat"
            onPress={() => handleStartChat(connection)}
            variant="primary"
            size="medium"
            style={styles.chatButton}
            icon="chatbubble"
            iconPosition="left"
          />
          <TouchableOpacity
            style={styles.archiveButton}
            onPress={() => handleArchiveConnection(connection.id)}
          >
            <Ionicons name="archive" size={20} color={theme.colors.text.tertiary} />
          </TouchableOpacity>
        </View>
      </Card>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="heart-outline" size={64} color={theme.colors.text.tertiary} />
      <Text style={styles.emptyTitle}>No Connections Yet</Text>
      <Text style={styles.emptySubtitle}>
        Attend events and review your connections to find mutual matches!
      </Text>
      <Button
        title="Discover Events"
        onPress={() => navigation.navigate('Events')}
        variant="primary"
        size="medium"
        style={styles.discoverButton}
        icon="calendar"
        iconPosition="left"
      />
    </View>
  );

  const activeConnections = mutualConnections.filter(c => c.status === 'active');
  const archivedConnections = mutualConnections.filter(c => c.status === 'archived');

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Your Connections</Text>
        <Text style={styles.headerSubtitle}>
          {activeConnections.length} active connection{activeConnections.length !== 1 ? 's' : ''}
        </Text>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={isLoading} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {activeConnections.length === 0 ? (
          renderEmptyState()
        ) : (
          <>
            {/* Active Connections */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Active Connections</Text>
              {activeConnections.map(renderConnectionCard)}
            </View>

            {/* Archived Connections */}
            {archivedConnections.length > 0 && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Archived</Text>
                {archivedConnections.map(renderConnectionCard)}
              </View>
            )}
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme: any) => {
  const typography = createTypographyStyles(theme);

  return StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.secondary,
  },
  header: {
    paddingHorizontal: theme.spacing[5],
    paddingVertical: theme.spacing[6],
    backgroundColor: theme.colors.background.primary,
    ...theme.shadows.sm,
  },
  headerTitle: {
    ...typography.h2,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.bold,
  },
  headerSubtitle: {
    ...typography.body1,
    color: theme.colors.text.secondary,
    marginTop: theme.spacing[2],
  },
  content: {
    flex: 1,
    paddingHorizontal: theme.spacing[5],
    paddingTop: theme.spacing[4],
  },
  section: {
    marginBottom: theme.spacing[6],
  },
  sectionTitle: {
    ...typography.h6,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.bold,
    marginBottom: theme.spacing[4],
  },
  connectionCard: {
    marginBottom: theme.spacing[4],
  },
  connectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing[4],
  },
  connectionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  connectionBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.background.secondary,
    paddingVertical: theme.spacing[1],
    paddingHorizontal: theme.spacing[3],
    borderRadius: theme.borderRadius.full,
    marginRight: theme.spacing[2],
  },
  connectionType: {
    ...typography.caption,
    fontWeight: theme.typography.fontWeight.semibold,
    marginLeft: theme.spacing[1],
  },
  newBadge: {
    backgroundColor: theme.colors.secondary[500],
    paddingVertical: theme.spacing[1],
    paddingHorizontal: theme.spacing[2],
    borderRadius: theme.borderRadius.sm,
  },
  newBadgeText: {
    ...typography.caption,
    color: theme.colors.text.inverse,
    fontWeight: theme.typography.fontWeight.bold,
    fontSize: 10,
  },
  matchDate: {
    ...typography.caption,
    color: theme.colors.text.tertiary,
  },
  connectionContent: {
    flexDirection: 'row',
    marginBottom: theme.spacing[4],
  },
  connectionPhoto: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: theme.spacing[4],
  },
  connectionDetails: {
    flex: 1,
    justifyContent: 'center',
  },
  connectionName: {
    ...typography.h6,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.bold,
  },
  connectionEvent: {
    ...typography.body2,
    color: theme.colors.text.secondary,
    marginTop: theme.spacing[1],
  },
  connectionDescription: {
    ...typography.body2,
    color: theme.colors.text.primary,
    marginTop: theme.spacing[2],
    lineHeight: 20,
  },
  connectionActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  chatButton: {
    flex: 1,
    marginRight: theme.spacing[3],
  },
  archiveButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: theme.colors.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: theme.spacing[16],
  },
  emptyTitle: {
    ...typography.h5,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.bold,
    marginTop: theme.spacing[4],
    marginBottom: theme.spacing[2],
  },
  emptySubtitle: {
    ...typography.body1,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: theme.spacing[6],
    paddingHorizontal: theme.spacing[8],
  },
  discoverButton: {
    paddingHorizontal: theme.spacing[8],
  },
  });
};

export default ConnectionsScreen;

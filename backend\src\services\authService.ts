import jwt from 'jsonwebtoken';
import { prisma } from '../config/database';
import { logger } from '../utils/logger';

export const generateTokens = async (userId: string) => {
  const accessToken = jwt.sign(
    { userId },
    process.env.JWT_SECRET!,
    { expiresIn: process.env.JWT_EXPIRES_IN }
  );

  const refreshToken = jwt.sign(
    { userId },
    process.env.JWT_REFRESH_SECRET!,
    { expiresIn: process.env.JWT_REFRESH_EXPIRES_IN }
  );

  // Store refresh token in database
  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + 7); // 7 days

  await prisma.refreshToken.create({
    data: {
      token: refreshToken,
      userId,
      expiresAt,
    }
  });

  return { accessToken, refreshToken };
};

export const verifyRefreshToken = async (token: string) => {
  try {
    // Check if token exists in database
    const storedToken = await prisma.refreshToken.findUnique({
      where: { token },
      include: { user: true }
    });

    if (!storedToken || storedToken.expiresAt < new Date()) {
      if (storedToken) {
        // Remove expired token
        await prisma.refreshToken.delete({
          where: { id: storedToken.id }
        });
      }
      return null;
    }

    // Verify JWT
    const decoded = jwt.verify(token, process.env.JWT_REFRESH_SECRET!) as any;
    
    return {
      userId: decoded.userId,
      user: storedToken.user
    };

  } catch (error) {
    logger.error('Refresh token verification error:', error);
    return null;
  }
};

export const cleanupExpiredTokens = async () => {
  try {
    const result = await prisma.refreshToken.deleteMany({
      where: {
        expiresAt: {
          lt: new Date()
        }
      }
    });

    logger.info(`Cleaned up ${result.count} expired refresh tokens`);
  } catch (error) {
    logger.error('Token cleanup error:', error);
  }
};

// Run cleanup every hour
setInterval(cleanupExpiredTokens, 60 * 60 * 1000);
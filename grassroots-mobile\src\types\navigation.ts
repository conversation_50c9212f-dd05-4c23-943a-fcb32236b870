import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';

// Define the parameter lists for each stack
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
};

export type AuthStackParamList = {
  Welcome: undefined;
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Events: undefined;
  Chat: undefined;
  Profile: undefined;
};

export type HomeStackParamList = {
  HomeMain: undefined;
  EventDetail: { event: any };
  Notifications: undefined;
  CreateEvent: undefined;
};

export type EventsStackParamList = {
  EventsMain: undefined;
  EventDetail: { event: any };
  PostEventReview: { eventId: string };
};

export type ChatStackParamList = {
  ChatList: undefined;
  Chat: { chatRoom: any };
};

export type ProfileStackParamList = {
  ProfileMain: undefined;
  Connections: undefined;
  SafetyReport: {
    reportedUserId: string;
    reportedUserName?: string;
    reportedUserPhoto?: string;
    eventId?: string;
    eventTitle?: string;
  };
  WallOfShame: undefined;
};

// Navigation prop types
export type HomeStackNavigationProp = StackNavigationProp<HomeStackParamList>;
export type EventsStackNavigationProp = StackNavigationProp<EventsStackParamList>;
export type ChatStackNavigationProp = StackNavigationProp<ChatStackParamList>;
export type ProfileStackNavigationProp = StackNavigationProp<ProfileStackParamList>;

// Route prop types
export type ChatScreenRouteProp = RouteProp<ChatStackParamList, 'Chat'>;
export type EventDetailScreenRouteProp = RouteProp<HomeStackParamList, 'EventDetail'> | RouteProp<EventsStackParamList, 'EventDetail'>;
export type PostEventReviewScreenRouteProp = RouteProp<EventsStackParamList, 'PostEventReview'>;
export type SafetyReportScreenRouteProp = RouteProp<ProfileStackParamList, 'SafetyReport'>;

// Screen prop types
export interface ChatScreenProps {
  navigation: HomeStackNavigationProp | ChatStackNavigationProp;
  route: ChatScreenRouteProp;
}

export interface EventDetailScreenProps {
  navigation: HomeStackNavigationProp | EventsStackNavigationProp;
  route: EventDetailScreenRouteProp;
}

export interface PostEventReviewScreenProps {
  navigation: EventsStackNavigationProp;
  route: PostEventReviewScreenRouteProp;
}

export interface SafetyReportScreenProps {
  navigation: ProfileStackNavigationProp;
  route: SafetyReportScreenRouteProp;
}

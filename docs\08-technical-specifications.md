# ⚙️ **GRASSROOTS** Technical Specifications

*Detailed technical requirements and specifications for the Grassroots platform*

---

## 🏗️ System Architecture Overview

### High-Level Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile App    │    │   Admin Panel   │    │  Partner Portal │
│  (React Native) │    │   (Next.js)     │    │   (Next.js)     │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴───────────┐
                    │     API Gateway         │
                    │   (Express.js/Node.js)  │
                    └─────────────┬───────────┘
                                 │
          ┌──────────────────────┼──────────────────────┐
          │                      │                      │
    ┌─────┴─────┐         ┌─────┴─────┐         ┌─────┴─────┐
    │PostgreSQL │         │   Redis   │         │   AWS S3  │
    │ Database  │         │   Cache   │         │  Storage  │
    └───────────┘         └───────────┘         └───────────┘
```

### Technology Stack

#### Frontend (Mobile App)
- **Framework:** React Native 0.72+ with Expo SDK 49+
- **Language:** TypeScript 5.0+
- **Navigation:** React Navigation 6.x
- **State Management:** Redux Toolkit + RTK Query
- **UI Components:** NativeBase 3.x or React Native Elements
- **Maps:** React Native Maps with Google Maps
- **Camera:** Expo Camera with image compression
- **Push Notifications:** Expo Notifications + Firebase Cloud Messaging
- **Real-time:** Socket.io client
- **Testing:** Jest + React Native Testing Library

#### Backend (API Server)
- **Runtime:** Node.js 18+ LTS
- **Framework:** Express.js 4.x with TypeScript
- **Database ORM:** Prisma 5.x
- **Authentication:** Firebase Admin SDK
- **Real-time:** Socket.io server
- **File Upload:** Multer with AWS S3 integration
- **Email Service:** SendGrid or AWS SES
- **Caching:** Redis 7.x
- **Testing:** Jest + Supertest
- **Documentation:** Swagger/OpenAPI 3.0

#### Database
- **Primary Database:** PostgreSQL 15+
- **Caching Layer:** Redis 7.x
- **File Storage:** AWS S3 or Google Cloud Storage
- **Search Engine:** PostgreSQL Full-Text Search (initially)

#### Infrastructure
- **Cloud Provider:** AWS or Google Cloud Platform
- **Container Orchestration:** Docker + Kubernetes (production)
- **CI/CD:** GitHub Actions
- **Monitoring:** Sentry + DataDog or New Relic
- **CDN:** CloudFront or CloudFlare

---

## 📱 Mobile App Specifications

### Supported Platforms
- **iOS:** 13.0+ (iPhone 8 and newer)
- **Android:** API Level 23+ (Android 6.0+)
- **Target Devices:** Smartphones only (tablet support in future)

### Performance Requirements
- **App Launch Time:** < 3 seconds on mid-range devices
- **Screen Transitions:** 60 FPS animations
- **Memory Usage:** < 150MB RAM on average
- **Battery Impact:** Minimal background processing
- **Offline Capability:** Core features work without internet

### UI/UX Specifications
- **Design System:** Custom design based on Material Design 3.0
- **Accessibility:** WCAG 2.1 AA compliance
- **Dark Mode:** Full support across all screens
- **Internationalization:** English (primary), Spanish (future)
- **Font Scaling:** Support for system font size preferences

### Security Requirements
- **Data Encryption:** AES-256 for sensitive data
- **Network Security:** TLS 1.3 for all API communications
- **Biometric Auth:** Face ID/Touch ID/Fingerprint support
- **Certificate Pinning:** Prevent man-in-the-middle attacks
- **Code Obfuscation:** Protect against reverse engineering

---

## 🗄️ Database Schema

### Core Tables

#### Users Table
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  university_domain VARCHAR(100) NOT NULL,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  profile_photo_url TEXT,
  bio TEXT,
  voice_note_url TEXT,
  date_of_birth DATE,
  gender VARCHAR(20),
  interests TEXT[], -- Array of interest tags
  verification_status VARCHAR(20) DEFAULT 'pending',
  account_status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  last_active_at TIMESTAMP
);
```

#### Events Table
```sql
CREATE TABLE events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(200) NOT NULL,
  description TEXT NOT NULL,
  event_type VARCHAR(50) NOT NULL, -- 'curated' or 'community'
  category VARCHAR(50) NOT NULL,
  location_id UUID REFERENCES locations(id),
  start_time TIMESTAMP NOT NULL,
  end_time TIMESTAMP NOT NULL,
  capacity INTEGER NOT NULL DEFAULT 24,
  min_group_size INTEGER DEFAULT 4,
  max_group_size INTEGER DEFAULT 6,
  created_by UUID REFERENCES users(id),
  status VARCHAR(20) DEFAULT 'active',
  image_url TEXT,
  tags TEXT[],
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Groups Table
```sql
CREATE TABLE groups (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_id UUID REFERENCES events(id),
  group_number INTEGER NOT NULL,
  status VARCHAR(20) DEFAULT 'forming',
  chat_room_id VARCHAR(100) UNIQUE,
  formed_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);
```

#### Messages Table
```sql
CREATE TABLE messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  chat_room_id VARCHAR(100) NOT NULL,
  sender_id UUID REFERENCES users(id),
  message_type VARCHAR(20) DEFAULT 'text', -- 'text', 'image', 'system'
  content TEXT NOT NULL,
  image_url TEXT,
  reply_to_id UUID REFERENCES messages(id),
  edited_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);
```

#### Connections Table
```sql
CREATE TABLE connections (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user1_id UUID REFERENCES users(id),
  user2_id UUID REFERENCES users(id),
  connection_type VARCHAR(20) NOT NULL, -- 'friend', 'spark'
  status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'accepted', 'declined'
  event_id UUID REFERENCES events(id),
  created_at TIMESTAMP DEFAULT NOW(),
  accepted_at TIMESTAMP,
  UNIQUE(user1_id, user2_id, event_id)
);
```

### Indexes and Performance
```sql
-- User lookup indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_university ON users(university_domain);
CREATE INDEX idx_users_status ON users(account_status);

-- Event discovery indexes
CREATE INDEX idx_events_start_time ON events(start_time);
CREATE INDEX idx_events_location ON events(location_id);
CREATE INDEX idx_events_category ON events(category);
CREATE INDEX idx_events_status ON events(status);

-- Chat performance indexes
CREATE INDEX idx_messages_chat_room ON messages(chat_room_id, created_at);
CREATE INDEX idx_messages_sender ON messages(sender_id);

-- Connection lookup indexes
CREATE INDEX idx_connections_user1 ON connections(user1_id);
CREATE INDEX idx_connections_user2 ON connections(user2_id);
CREATE INDEX idx_connections_event ON connections(event_id);
```

---

## 🔌 API Specifications

### Authentication Endpoints
```typescript
// User Registration
POST /api/auth/register
{
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  universityDomain: string;
}

// User Login
POST /api/auth/login
{
  email: string;
  password: string;
}

// Token Refresh
POST /api/auth/refresh
{
  refreshToken: string;
}
```

### User Management Endpoints
```typescript
// Get User Profile
GET /api/users/profile
Headers: { Authorization: "Bearer <token>" }

// Update User Profile
PUT /api/users/profile
{
  firstName?: string;
  lastName?: string;
  bio?: string;
  interests?: string[];
  profilePhoto?: File;
}

// Upload Voice Note
POST /api/users/voice-note
Content-Type: multipart/form-data
{
  voiceNote: File;
}
```

### Event Management Endpoints
```typescript
// Get Events Feed
GET /api/events?page=1&limit=20&category=all&location=nearby
Headers: { Authorization: "Bearer <token>" }

// Create Event
POST /api/events
{
  title: string;
  description: string;
  category: string;
  locationId: string;
  startTime: string;
  endTime: string;
  capacity: number;
  image?: File;
}

// Opt-in to Event
POST /api/events/:eventId/opt-in
Headers: { Authorization: "Bearer <token>" }
```

### Chat Endpoints
```typescript
// Get Chat History
GET /api/chats/:chatRoomId/messages?page=1&limit=50
Headers: { Authorization: "Bearer <token>" }

// Send Message
POST /api/chats/:chatRoomId/messages
{
  content: string;
  messageType: 'text' | 'image';
  replyToId?: string;
}
```

### Real-time Events (Socket.io)
```typescript
// Client Events
socket.emit('join_chat', { chatRoomId: string });
socket.emit('send_message', { chatRoomId: string, content: string });
socket.emit('typing_start', { chatRoomId: string });
socket.emit('typing_stop', { chatRoomId: string });

// Server Events
socket.on('message_received', { message: Message });
socket.on('user_typing', { userId: string, isTyping: boolean });
socket.on('group_formed', { groupId: string, members: User[] });
socket.on('connection_made', { connectionId: string, user: User });
```

---

## 🔒 Security Specifications

### Authentication & Authorization
- **JWT Tokens:** Access tokens (15 min expiry) + Refresh tokens (30 days)
- **Role-Based Access:** User, Moderator, Admin, Super Admin
- **Rate Limiting:** 100 requests/minute per user, 1000/minute per IP
- **Password Policy:** Min 8 chars, uppercase, lowercase, number, special char

### Data Protection
- **Encryption at Rest:** AES-256 for sensitive data
- **Encryption in Transit:** TLS 1.3 for all communications
- **PII Handling:** Minimal collection, encrypted storage, secure deletion
- **GDPR Compliance:** Data export, deletion, consent management

### Input Validation
- **Server-side Validation:** All inputs validated and sanitized
- **File Upload Security:** Type validation, size limits, virus scanning
- **SQL Injection Prevention:** Parameterized queries only
- **XSS Prevention:** Content Security Policy, input sanitization

---

## 📊 Performance Requirements

### Response Time Targets
- **API Endpoints:** < 200ms average response time
- **Database Queries:** < 100ms for simple queries, < 500ms for complex
- **File Uploads:** < 5 seconds for images, < 10 seconds for voice notes
- **Real-time Messages:** < 100ms delivery time

### Scalability Targets
- **Concurrent Users:** 1,000 simultaneous users per campus
- **Database Connections:** 100 concurrent connections
- **File Storage:** 10TB initial capacity with auto-scaling
- **CDN Performance:** < 50ms asset delivery globally

### Monitoring & Alerting
- **Uptime Target:** 99.9% availability
- **Error Rate:** < 0.1% of requests
- **Performance Monitoring:** Real-time metrics and alerting
- **Log Retention:** 90 days for application logs, 1 year for audit logs

---

## 🧪 Testing Requirements

### Unit Testing
- **Coverage Target:** > 80% code coverage
- **Test Types:** Component tests, utility function tests, API endpoint tests
- **Mocking:** External services, database connections, file operations

### Integration Testing
- **API Testing:** End-to-end API workflow testing
- **Database Testing:** Data integrity and performance testing
- **Real-time Testing:** Socket.io connection and message delivery

### End-to-End Testing
- **User Flows:** Complete user journeys from registration to connections
- **Cross-Platform:** iOS and Android compatibility testing
- **Performance Testing:** Load testing with simulated user traffic

---

*These technical specifications provide the foundation for building a robust, scalable, and secure Grassroots platform.*

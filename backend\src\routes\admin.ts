import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import { requireAdmin } from '../middleware/adminAuth';
import {
  getDashboardStats,
  getUsers,
  getUserDetails,
  updateUserStatus,
  getEventAnalytics,
  getSystemHealth
} from '../controllers/adminController';
import {
  getReports,
  reviewReport,
  getUserModerationHistory
} from '../controllers/moderationController';

const router = Router();

// All admin routes require authentication and admin privileges
router.use(authenticateToken);
router.use(requireAdmin);

// Dashboard
router.get('/dashboard/stats', getDashboardStats);
router.get('/dashboard/health', getSystemHealth);

// User Management
router.get('/users', getUsers);
router.get('/users/:userId', getUserDetails);
router.put('/users/:userId/status', updateUserStatus);
router.get('/users/:userId/moderation-history', getUserModerationHistory);

// Event Analytics
router.get('/events/analytics', getEventAnalytics);
router.get('/events/:eventId/analytics', getEventAnalytics);

// Moderation
router.get('/reports', getReports);
router.put('/reports/:reportId/review', reviewReport);

export default router;
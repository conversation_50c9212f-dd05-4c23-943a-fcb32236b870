import request from 'supertest';
import { app } from '../../src/app';
import { createTestUser, createTestEvent, generateTestToken, cleanupDatabase } from '../utils/testHelpers';

describe('Event Endpoints', () => {
  afterEach(async () => {
    await cleanupDatabase();
  });

  describe('POST /api/events', () => {
    it('should create a new event', async () => {
      const user = await createTestUser();
      const token = generateTestToken(user.id);

      const eventData = {
        title: 'Test Event',
        description: 'Test Description',
        location: 'Test Location',
        startTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        endTime: new Date(Date.now() + 25 * 60 * 60 * 1000).toISOString(),
        capacity: 20
      };

      const response = await request(app)
        .post('/api/events')
        .set('Authorization', `Bearer ${token}`)
        .send(eventData);

      expect(response.status).toBe(201);
      expect(response.body.event.title).toBe(eventData.title);
      expect(response.body.event.createdById).toBe(user.id);
    });

    it('should return 400 for missing required fields', async () => {
      const user = await createTestUser();
      const token = generateTestToken(user.id);

      const response = await request(app)
        .post('/api/events')
        .set('Authorization', `Bearer ${token}`)
        .send({ title: 'Incomplete Event' });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Missing required fields');
    });
  });

  describe('GET /api/events', () => {
    it('should return paginated events', async () => {
      const user = await createTestUser();
      await createTestEvent(user.id, { title: 'Event 1' });
      await createTestEvent(user.id, { title: 'Event 2' });

      const response = await request(app)
        .get('/api/events')
        .query({ page: 1, limit: 10 });

      expect(response.status).toBe(200);
      expect(response.body.events).toHaveLength(2);
      expect(response.body.pagination.total).toBe(2);
    });

    it('should filter events by search term', async () => {
      const user = await createTestUser();
      await createTestEvent(user.id, { title: 'Basketball Game' });
      await createTestEvent(user.id, { title: 'Study Group' });

      const response = await request(app)
        .get('/api/events')
        .query({ search: 'Basketball' });

      expect(response.status).toBe(200);
      expect(response.body.events).toHaveLength(1);
      expect(response.body.events[0].title).toBe('Basketball Game');
    });
  });

  describe('POST /api/events/:id/join', () => {
    it('should allow user to join event', async () => {
      const creator = await createTestUser();
      const participant = await createTestUser();
      const event = await createTestEvent(creator.id);
      const token = generateTestToken(participant.id);

      const response = await request(app)
        .post(`/api/events/${event.id}/join`)
        .set('Authorization', `Bearer ${token}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Successfully joined event');
    });

    it('should return 401 without authentication', async () => {
      const user = await createTestUser();
      const event = await createTestEvent(user.id);

      const response = await request(app)
        .post(`/api/events/${event.id}/join`);

      expect(response.status).toBe(401);
    });
  });
});
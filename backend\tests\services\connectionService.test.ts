import { connectionService } from '../../src/services/connectionService';
import { createTestUser, createTestEvent, addUserToEvent, cleanupDatabase } from '../utils/testHelpers';

describe('ConnectionService', () => {
  afterEach(async () => {
    await cleanupDatabase();
  });

  describe('createConnection', () => {
    it('should create a connection between event participants', async () => {
      const user1 = await createTestUser();
      const user2 = await createTestUser();
      const event = await createTestEvent(user1.id);
      
      await addUserToEvent(user1.id, event.id);
      await addUserToEvent(user2.id, event.id);

      const connection = await connectionService.createConnection(user1.id, {
        eventId: event.id,
        toUserId: user2.id,
        connectionType: 'FRIEND'
      });

      expect(connection.fromUserId).toBe(user1.id);
      expect(connection.toUserId).toBe(user2.id);
      expect(connection.connectionType).toBe('FRIEND');
      expect(connection.status).toBe('PENDING');
    });

    it('should throw error if user did not participate in event', async () => {
      const user1 = await createTestUser();
      const user2 = await createTestUser();
      const event = await createTestEvent(user1.id);

      await expect(connectionService.createConnection(user1.id, {
        eventId: event.id,
        toUserId: user2.id,
        connectionType: 'FRIEND'
      })).rejects.toThrow('User did not participate in this event');
    });
  });

  describe('checkMutualConnection', () => {
    it('should detect mutual connections', async () => {
      const user1 = await createTestUser();
      const user2 = await createTestUser();
      const event = await createTestEvent(user1.id);
      
      await addUserToEvent(user1.id, event.id);
      await addUserToEvent(user2.id, event.id);

      // Create connections from both users
      await connectionService.createConnection(user1.id, {
        eventId: event.id,
        toUserId: user2.id,
        connectionType: 'FRIEND'
      });

      await connectionService.createConnection(user2.id, {
        eventId: event.id,
        toUserId: user1.id,
        connectionType: 'SPARK'
      });

      const isMutual = await connectionService.checkMutualConnection(event.id, user1.id, user2.id);
      expect(isMutual).toBe(true);
    });
  });

  describe('sendDirectMessage', () => {
    it('should send message between mutual connections', async () => {
      const user1 = await createTestUser();
      const user2 = await createTestUser();
      const event = await createTestEvent(user1.id);
      
      await addUserToEvent(user1.id, event.id);
      await addUserToEvent(user2.id, event.id);

      const connection1 = await connectionService.createConnection(user1.id, {
        eventId: event.id,
        toUserId: user2.id,
        connectionType: 'FRIEND'
      });

      const connection2 = await connectionService.createConnection(user2.id, {
        eventId: event.id,
        toUserId: user1.id,
        connectionType: 'FRIEND'
      });

      // Manually set to mutual for testing
      await connectionService.checkMutualConnection(event.id, user1.id, user2.id);

      const message = await connectionService.sendDirectMessage(
        connection1.id,
        user1.id,
        'Hello there!'
      );

      expect(message.content).toBe('Hello there!');
      expect(message.senderId).toBe(user1.id);
    });
  });
});
import request from 'supertest';
import { app } from '../../src/app';
import { createTestUser, createTestEvent, addUserToEvent, generateTestToken, cleanupDatabase } from '../utils/testHelpers';

describe('Connection Endpoints', () => {
  afterEach(async () => {
    await cleanupDatabase();
  });

  describe('POST /api/connections', () => {
    it('should create a connection between event participants', async () => {
      const user1 = await createTestUser();
      const user2 = await createTestUser();
      const event = await createTestEvent(user1.id);
      const token = generateTestToken(user1.id);
      
      await addUserToEvent(user1.id, event.id);
      await addUserToEvent(user2.id, event.id);

      const response = await request(app)
        .post('/api/connections')
        .set('Authorization', `Bearer ${token}`)
        .send({
          eventId: event.id,
          toUserId: user2.id,
          connectionType: 'FRIEND'
        });

      expect(response.status).toBe(201);
      expect(response.body.connection.fromUserId).toBe(user1.id);
      expect(response.body.connection.toUserId).toBe(user2.id);
      expect(response.body.connection.connectionType).toBe('FRIEND');
    });

    it('should return 400 for invalid connection type', async () => {
      const user1 = await createTestUser();
      const user2 = await createTestUser();
      const event = await createTestEvent(user1.id);
      const token = generateTestToken(user1.id);

      const response = await request(app)
        .post('/api/connections')
        .set('Authorization', `Bearer ${token}`)
        .send({
          eventId: event.id,
          toUserId: user2.id,
          connectionType: 'INVALID'
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Invalid connection type');
    });
  });

  describe('GET /api/connections/user', () => {
    it('should return user connections', async () => {
      const user = await createTestUser();
      const token = generateTestToken(user.id);

      const response = await request(app)
        .get('/api/connections/user')
        .set('Authorization', `Bearer ${token}`);

      expect(response.status).toBe(200);
      expect(response.body.connections).toBeDefined();
      expect(Array.isArray(response.body.connections)).toBe(true);
    });
  });
});
-- CreateIndex
CREATE INDEX "idx_users_email" ON "users"("email");
CREATE INDEX "idx_users_university" ON "users"("university");
CREATE INDEX "idx_users_is_active" ON "users"("is_active");
CREATE INDEX "idx_users_created_at" ON "users"("created_at");

-- CreateIndex
CREATE INDEX "idx_events_start_time" ON "events"("start_time");
CREATE INDEX "idx_events_location" ON "events"("location");
CREATE INDEX "idx_events_created_by_id" ON "events"("created_by_id");
CREATE INDEX "idx_events_is_active" ON "events"("is_active");

-- CreateIndex for full-text search
CREATE INDEX "idx_events_title_search" ON "events" USING gin(to_tsvector('english', "title"));
CREATE INDEX "idx_events_description_search" ON "events" USING gin(to_tsvector('english', "description"));

-- CreateIndex
CREATE INDEX "idx_event_participants_user_id" ON "event_participants"("user_id");
CREATE INDEX "idx_event_participants_event_id" ON "event_participants"("event_id");
CREATE INDEX "idx_event_participants_joined_at" ON "event_participants"("joined_at");

-- CreateIndex
CREATE INDEX "idx_groups_event_id" ON "groups"("event_id");
CREATE INDEX "idx_groups_created_at" ON "groups"("created_at");

-- CreateIndex
CREATE INDEX "idx_group_members_user_id" ON "group_members"("user_id");
CREATE INDEX "idx_group_members_group_id" ON "group_members"("group_id");

-- CreateIndex
CREATE INDEX "idx_messages_group_id" ON "messages"("group_id");
CREATE INDEX "idx_messages_sender_id" ON "messages"("sender_id");
CREATE INDEX "idx_messages_created_at" ON "messages"("created_at");

-- CreateIndex
CREATE INDEX "idx_connections_from_user_id" ON "connections"("from_user_id");
CREATE INDEX "idx_connections_to_user_id" ON "connections"("to_user_id");
CREATE INDEX "idx_connections_event_id" ON "connections"("event_id");
CREATE INDEX "idx_connections_status" ON "connections"("status");

-- CreateIndex
CREATE INDEX "idx_reports_reporter_id" ON "reports"("reporter_id");
CREATE INDEX "idx_reports_reported_user_id" ON "reports"("reported_user_id");
CREATE INDEX "idx_reports_status" ON "reports"("status");
CREATE INDEX "idx_reports_priority" ON "reports"("priority");
CREATE INDEX "idx_reports_created_at" ON "reports"("created_at");

-- CreateIndex
CREATE INDEX "idx_refresh_tokens_user_id" ON "refresh_tokens"("user_id");
CREATE INDEX "idx_refresh_tokens_expires_at" ON "refresh_tokens"("expires_at");

-- Composite indexes for common queries
CREATE INDEX "idx_events_active_start_time" ON "events"("is_active", "start_time");
CREATE INDEX "idx_event_participants_event_user" ON "event_participants"("event_id", "user_id");
CREATE INDEX "idx_group_members_group_user" ON "group_members"("group_id", "user_id");
CREATE INDEX "idx_connections_mutual" ON "connections"("from_user_id", "to_user_id", "event_id");
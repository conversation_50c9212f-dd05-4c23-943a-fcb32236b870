export interface Connection {
  id: string;
  eventId: string;
  fromUserId: string;
  toUserId: string;
  connectionType: 'FRIEND' | 'SPARK' | 'PASS';
  status: 'PENDING' | 'MUTUAL' | 'DECLINED';
  createdAt: Date;
  mutualAt?: Date;
}

export interface PostEventReview {
  id: string;
  eventId: string;
  reviewerId: string;
  revieweeId: string;
  rating: 'FRIEND' | 'SPARK' | 'PASS';
  feedback?: string;
  isAnonymous: boolean;
  createdAt: Date;
}

export interface DirectMessage {
  id: string;
  connectionId: string;
  senderId: string;
  content: string;
  messageType: 'TEXT' | 'IMAGE' | 'VOICE';
  isRead: boolean;
  createdAt: Date;
}

export interface CreateConnectionData {
  eventId: string;
  toUserId: string;
  connectionType: 'FRIEND' | 'SPARK' | 'PASS';
  feedback?: string;
  isAnonymous?: boolean;
}
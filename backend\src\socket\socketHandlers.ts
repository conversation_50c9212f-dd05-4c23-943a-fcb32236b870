import { Server } from 'socket.io';
import { AuthenticatedSocket } from './socketAuth';
import { groupService } from '../services/groupService';
import { logger } from '../utils/logger';

export const initializeSocket = (io: Server) => {
  io.on('connection', (socket: AuthenticatedSocket) => {
    logger.info(`User connected: ${socket.userId}`);

    // Join user to their groups
    socket.on('join-groups', async () => {
      try {
        const userGroups = await groupService.getUserGroups(socket.userId!);
        userGroups.forEach(membership => {
          socket.join(`group-${membership.group.id}`);
        });
        logger.info(`User ${socket.userId} joined ${userGroups.length} group rooms`);
      } catch (error) {
        logger.error('Join groups error:', error);
      }
    });

    // Handle joining specific group
    socket.on('join-group', (groupId: string) => {
      socket.join(`group-${groupId}`);
      logger.info(`User ${socket.userId} joined group room: ${groupId}`);
    });

    // Handle leaving group
    socket.on('leave-group', (groupId: string) => {
      socket.leave(`group-${groupId}`);
      logger.info(`User ${socket.userId} left group room: ${groupId}`);
    });

    // Handle sending messages
    socket.on('send-message', async (data: { groupId: string; content: string; messageType?: string }) => {
      try {
        const message = await groupService.sendMessage(
          data.groupId,
          socket.userId!,
          data.content,
          (data.messageType as any) || 'TEXT'
        );

        // Broadcast message to group members
        io.to(`group-${data.groupId}`).emit('new-message', message);
        logger.info(`Message broadcasted to group ${data.groupId}`);
      } catch (error) {
        logger.error('Send message error:', error);
        socket.emit('message-error', { error: 'Failed to send message' });
      }
    });

    // Handle typing indicators
    socket.on('typing-start', (groupId: string) => {
      socket.to(`group-${groupId}`).emit('user-typing', {
        userId: socket.userId,
        groupId
      });
    });

    socket.on('typing-stop', (groupId: string) => {
      socket.to(`group-${groupId}`).emit('user-stopped-typing', {
        userId: socket.userId,
        groupId
      });
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      logger.info(`User disconnected: ${socket.userId}`);
    });
  });
};
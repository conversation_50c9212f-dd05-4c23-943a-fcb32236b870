import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// Types for Phase 4 - Post-Event Connections
export interface EventParticipant {
  id: string;
  firstName: string;
  lastName: string;
  profilePhoto: string;
  bio: string;
  interests: string[];
  university: string;
  mutualConnections: number;
  eventInteractions: {
    chatMessages: number;
    sharedMoments: number;
    timeSpentTogether: number; // in minutes
  };
}

export interface ConnectionReview {
  id: string;
  eventId: string;
  reviewerId: string;
  revieweeId: string;
  rating: 'friend' | 'spark' | 'pass';
  isAnonymous: boolean;
  feedback?: string;
  createdAt: string;
}

export interface MutualConnection {
  id: string;
  eventId: string;
  user1Id: string;
  user2Id: string;
  connectionType: 'friend' | 'spark';
  matchedAt: string;
  chatRoomId?: string;
  status: 'pending' | 'active' | 'archived';
}

export interface PostEventSummary {
  eventId: string;
  eventTitle: string;
  eventDate: string;
  participants: EventParticipant[];
  myReviews: ConnectionReview[];
  receivedReviews: ConnectionReview[];
  mutualConnections: MutualConnection[];
  reviewDeadline: string;
  isReviewComplete: boolean;
}

interface ConnectionsState {
  postEventSummaries: PostEventSummary[];
  currentEventReview: string | null;
  pendingReviews: ConnectionReview[];
  mutualConnections: MutualConnection[];
  isLoading: boolean;
  error: string | null;
}

const initialState: ConnectionsState = {
  postEventSummaries: [],
  currentEventReview: null,
  pendingReviews: [],
  mutualConnections: [],
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchPostEventSummary = createAsyncThunk(
  'connections/fetchPostEventSummary',
  async (eventId: string) => {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock data
    const mockSummary: PostEventSummary = {
      eventId,
      eventTitle: 'Coffee & Connections',
      eventDate: '2025-07-11T10:00:00Z',
      participants: [
        {
          id: 'user2',
          firstName: 'Sarah',
          lastName: 'Chen',
          profilePhoto: 'https://i.pravatar.cc/150?img=2',
          bio: 'Computer Science major who loves hiking and photography',
          interests: ['Technology', 'Photography', 'Hiking', 'Coffee'],
          university: 'University of California',
          mutualConnections: 3,
          eventInteractions: {
            chatMessages: 12,
            sharedMoments: 2,
            timeSpentTogether: 45,
          },
        },
        {
          id: 'user3',
          firstName: 'Mike',
          lastName: 'Johnson',
          profilePhoto: 'https://i.pravatar.cc/150?img=3',
          bio: 'Business student passionate about entrepreneurship',
          interests: ['Business', 'Entrepreneurship', 'Sports', 'Travel'],
          university: 'University of California',
          mutualConnections: 1,
          eventInteractions: {
            chatMessages: 8,
            sharedMoments: 1,
            timeSpentTogether: 30,
          },
        },
      ],
      myReviews: [],
      receivedReviews: [],
      mutualConnections: [],
      reviewDeadline: '2025-07-13T23:59:59Z',
      isReviewComplete: false,
    };
    
    return mockSummary;
  }
);

export const submitConnectionReview = createAsyncThunk(
  'connections/submitConnectionReview',
  async (review: Omit<ConnectionReview, 'id' | 'createdAt'>) => {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const newReview: ConnectionReview = {
      ...review,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
    };
    
    return newReview;
  }
);

export const fetchMutualConnections = createAsyncThunk(
  'connections/fetchMutualConnections',
  async () => {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // Mock data
    const mockConnections: MutualConnection[] = [
      {
        id: 'conn1',
        eventId: '1',
        user1Id: 'user1',
        user2Id: 'user2',
        connectionType: 'friend',
        matchedAt: '2025-07-11T15:30:00Z',
        chatRoomId: 'private-chat-1',
        status: 'active',
      },
    ];
    
    return mockConnections;
  }
);

const connectionsSlice = createSlice({
  name: 'connections',
  initialState,
  reducers: {
    setCurrentEventReview: (state, action: PayloadAction<string | null>) => {
      state.currentEventReview = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    updateConnectionStatus: (state, action: PayloadAction<{ connectionId: string; status: MutualConnection['status'] }>) => {
      const connection = state.mutualConnections.find(c => c.id === action.payload.connectionId);
      if (connection) {
        connection.status = action.payload.status;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Post Event Summary
      .addCase(fetchPostEventSummary.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPostEventSummary.fulfilled, (state, action) => {
        state.isLoading = false;
        const existingIndex = state.postEventSummaries.findIndex(
          summary => summary.eventId === action.payload.eventId
        );
        if (existingIndex >= 0) {
          state.postEventSummaries[existingIndex] = action.payload;
        } else {
          state.postEventSummaries.push(action.payload);
        }
      })
      .addCase(fetchPostEventSummary.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch post-event summary';
      })
      // Submit Connection Review
      .addCase(submitConnectionReview.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(submitConnectionReview.fulfilled, (state, action) => {
        state.isLoading = false;
        state.pendingReviews.push(action.payload);

        // Update the corresponding post-event summary
        const summary = state.postEventSummaries.find(
          s => s.eventId === action.payload.eventId
        );
        if (summary) {
          summary.myReviews.push(action.payload);
          summary.isReviewComplete = summary.participants.length === summary.myReviews.length;

          // If this creates a mutual connection (both rated friend/spark), increment connections
          if (action.payload.rating === 'friend' || action.payload.rating === 'spark') {
            // Note: In a real app, this would be handled by the backend
            // For now, we'll increment on any positive rating
          }
        }
      })
      .addCase(submitConnectionReview.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to submit review';
      })
      // Fetch Mutual Connections
      .addCase(fetchMutualConnections.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchMutualConnections.fulfilled, (state, action) => {
        state.isLoading = false;
        state.mutualConnections = action.payload;
      })
      .addCase(fetchMutualConnections.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch mutual connections';
      });
  },
});

export const {
  setCurrentEventReview,
  clearError,
  updateConnectionStatus,
} = connectionsSlice.actions;

export default connectionsSlice.reducer;

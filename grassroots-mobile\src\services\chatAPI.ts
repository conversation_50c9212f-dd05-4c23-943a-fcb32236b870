import { apiClient } from './api';

export interface ChatParticipant {
  id: string;
  name: string;
  avatar?: string;
}

export interface ChatMessage {
  _id: string;
  text: string;
  messageType: 'TEXT' | 'IMAGE' | 'SYSTEM';
  imageUrl?: string;
  createdAt: string;
  editedAt?: string;
  user: {
    _id: string;
    name: string;
    avatar?: string;
  };
  replyTo?: {
    _id: string;
    text: string;
    user: {
      _id: string;
      name: string;
    };
  };
}

export interface ChatRoom {
  id: string;
  name: string;
  eventId: string;
  eventTitle: string;
  eventStartTime: string;
  eventStatus: string;
  groupId: string;
  groupNumber: number;
  groupStatus: string;
  participants: ChatParticipant[];
  lastMessage?: {
    id: string;
    content: string;
    messageType: string;
    createdAt: string;
    sender: {
      id: string;
      name: string;
    };
  };
  messageCount: number;
  isActive: boolean;
  createdAt: string;
}

export interface MessagesResponse {
  messages: ChatMessage[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface SendMessageData {
  content: string;
  messageType?: 'TEXT' | 'IMAGE';
  replyToId?: string;
}

export const chatAPI = {
  async getChatRooms(): Promise<ChatRoom[]> {
    const response = await apiClient.get<{ chatRooms: ChatRoom[] }>('/chat/rooms');
    return response.data!.chatRooms;
  },

  async getChatMessages(
    roomId: string,
    page: number = 1,
    limit: number = 50
  ): Promise<MessagesResponse> {
    const response = await apiClient.get<MessagesResponse>(
      `/chat/${roomId}/messages?page=${page}&limit=${limit}`
    );
    return response.data!;
  },

  async sendMessage(roomId: string, data: SendMessageData): Promise<ChatMessage> {
    const response = await apiClient.post<{ data: ChatMessage }>(
      `/chat/${roomId}/messages`,
      data
    );
    return response.data!.data;
  },

  async deleteMessage(roomId: string, messageId: string): Promise<void> {
    await apiClient.delete(`/chat/${roomId}/messages/${messageId}`);
  },

  async uploadChatImage(imageUri: string): Promise<string> {
    const formData = new FormData();
    formData.append('image', {
      uri: imageUri,
      type: 'image/jpeg',
      name: 'chat-image.jpg',
    } as any);
    formData.append('type', 'chat');

    const response = await apiClient.postFormData<{ imageUrl: string }>('/upload/image', formData);
    return response.data!.imageUrl;
  },
};

import React from 'react';
import { ViewStyle } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '../contexts/ThemeContext';
import { gradients } from '../utils/theme';

interface GradientBackgroundProps {
  variant?: 'primary' | 'secondary' | 'accent' | 'card' | 'hero' | 'feature' | 'glow' | 'sunset' | 'ocean';
  children?: React.ReactNode;
  style?: ViewStyle;
  start?: { x: number; y: number };
  end?: { x: number; y: number };
}

const GradientBackground: React.FC<GradientBackgroundProps> = ({
  variant = 'primary',
  children,
  style,
  start = { x: 0, y: 0 },
  end = { x: 1, y: 1 },
}) => {
  const { isDark } = useTheme();

  const getGradientColors = () => {
    if (!isDark) {
      // Light theme gradients - modern and subtle
      switch (variant) {
        case 'primary':
          return gradients.light.background;
        case 'secondary':
          return gradients.light.subtle;
        case 'accent':
          return gradients.light.hero;
        case 'card':
          return gradients.light.card;
        case 'hero':
          return gradients.light.hero;
        case 'feature':
          return gradients.light.feature;
        case 'glow':
          return gradients.primary;
        case 'sunset':
          return gradients.sunset;
        case 'ocean':
          return gradients.ocean;
        default:
          return gradients.light.background;
      }
    }

    // Dark theme gradients - beautiful and modern
    switch (variant) {
      case 'primary':
        return gradients.dark.background;
      case 'secondary':
        return gradients.dark.card;
      case 'accent':
        return gradients.dark.glow;
      case 'card':
        return gradients.dark.card;
      case 'hero':
        return gradients.dark.hero;
      case 'feature':
        return gradients.dark.feature;
      case 'glow':
        return gradients.dark.glow;
      case 'sunset':
        return gradients.sunset;
      case 'ocean':
        return gradients.ocean;
      default:
        return gradients.dark.background;
    }
  };

  const colors = getGradientColors();

  return (
    <LinearGradient
      colors={colors as [string, string, ...string[]]}
      start={start}
      end={end}
      style={style}
    >
      {children}
    </LinearGradient>
  );
};

// Predefined gradient variants for common use cases
export const GradientCard: React.FC<GradientBackgroundProps> = (props) => (
  <GradientBackground variant="card" {...props} />
);

export const GradientScreen: React.FC<GradientBackgroundProps> = (props) => (
  <GradientBackground 
    variant="primary" 
    start={{ x: 0, y: 0 }} 
    end={{ x: 1, y: 1 }} 
    {...props} 
  />
);

export const GradientHeader: React.FC<GradientBackgroundProps> = (props) => (
  <GradientBackground 
    variant="secondary" 
    start={{ x: 0, y: 0 }} 
    end={{ x: 1, y: 0.5 }} 
    {...props} 
  />
);

export const GradientButton: React.FC<GradientBackgroundProps> = (props) => (
  <GradientBackground 
    variant="accent" 
    start={{ x: 0, y: 0 }} 
    end={{ x: 1, y: 0 }} 
    {...props} 
  />
);

export default GradientBackground;

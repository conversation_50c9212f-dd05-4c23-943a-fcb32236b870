# Vibe App

Welcome to the Vibe App monorepo. This repository contains the source code for the Vibe mobile application and its backend services.

## Overview

- **`grassroots-mobile/`**: The React Native (Expo) mobile application.
- **`backend/`**: The Node.js, Express, and Prisma backend API.
- **`docs/`**: Project documentation.

## Getting Started

To get the application up and running, please refer to the detailed setup guide:

**[➡️ SETUP_GUIDE.md](SETUP_GUIDE.md)**

This guide provides step-by-step instructions for setting up the backend and the mobile application.

## Contributing

Contributions are welcome! Please read the [contributing guidelines](docs/CONTRIBUTING.md) before getting started.

## License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.

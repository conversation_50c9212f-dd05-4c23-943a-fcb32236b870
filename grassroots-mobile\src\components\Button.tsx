import React, { useRef } from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ActivityIndicator,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import { AnimationUtils } from '../utils/animations';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'ghost' | 'danger';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  icon?: keyof typeof Ionicons.glyphMap;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  style,
  textStyle,
}) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const scaleValue = useRef(new Animated.Value(1)).current;
  const animationHandlers = AnimationUtils.buttonPress(scaleValue);
  const buttonStyle = [
    styles.base,
    styles[variant],
    styles[size],
    fullWidth && styles.fullWidth,
    disabled && styles.disabled,
    style,
  ];

  const textStyleCombined = [
    styles.text,
    styles[`${variant}Text`],
    styles[`${size}Text`],
    disabled && styles.disabledText,
    textStyle,
  ];

  const iconSize = size === 'small' ? 16 : size === 'large' ? 24 : 20;
  const iconColor = variant === 'primary' || variant === 'danger' 
    ? theme.colors.text.inverse 
    : theme.colors.primary[500];

  return (
    <Animated.View style={{ transform: [{ scale: scaleValue }] }}>
      <TouchableOpacity
        style={buttonStyle}
        onPress={onPress}
        disabled={disabled || loading}
        activeOpacity={1}
        onPressIn={animationHandlers.onPressIn}
        onPressOut={animationHandlers.onPressOut}
      >
      {loading ? (
        <ActivityIndicator 
          size="small" 
          color={iconColor}
        />
      ) : (
        <View style={styles.buttonContent}>
          {icon && iconPosition === 'left' && (
            <Ionicons
              name={icon}
              size={iconSize}
              color={iconColor}
              style={styles.iconLeft}
            />
          )}
          <Text style={textStyleCombined}>{title}</Text>
          {icon && iconPosition === 'right' && (
            <Ionicons
              name={icon}
              size={iconSize}
              color={iconColor}
              style={styles.iconRight}
            />
          )}
        </View>
      )}
      </TouchableOpacity>
    </Animated.View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  base: {
    borderRadius: theme.borderRadius.full,
    ...theme.shadows.base,
  },

  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  
  // Variants
  primary: {
    backgroundColor: theme.colors.primary[500],
  },
  
  secondary: {
    backgroundColor: theme.colors.background.primary,
    borderWidth: 2,
    borderColor: theme.colors.primary[500],
  },
  
  ghost: {
    backgroundColor: 'transparent',
  },
  
  danger: {
    backgroundColor: theme.colors.error,
  },
  
  // Sizes
  small: {
    paddingVertical: theme.spacing[2],
    paddingHorizontal: theme.spacing[4],
  },
  
  medium: {
    paddingVertical: theme.spacing[3],
    paddingHorizontal: theme.spacing[6],
  },
  
  large: {
    paddingVertical: theme.spacing[4],
    paddingHorizontal: theme.spacing[8],
  },
  
  // States
  disabled: {
    opacity: 0.5,
  },
  
  fullWidth: {
    width: '100%',
  },
  
  // Text styles
  text: {
    fontWeight: theme.typography.fontWeight.semibold,
    textAlign: 'center',
  },
  
  primaryText: {
    color: theme.colors.text.inverse,
  },
  
  secondaryText: {
    color: theme.colors.primary[500],
  },
  
  ghostText: {
    color: theme.colors.primary[500],
  },
  
  dangerText: {
    color: theme.colors.text.inverse,
  },
  
  disabledText: {
    opacity: 0.7,
  },
  
  // Size-specific text
  smallText: {
    fontSize: theme.typography.fontSize.sm,
  },
  
  mediumText: {
    fontSize: theme.typography.fontSize.base,
  },
  
  largeText: {
    fontSize: theme.typography.fontSize.lg,
  },
  
  // Icon styles
  iconLeft: {
    marginRight: theme.spacing[2],
  },
  
  iconRight: {
    marginLeft: theme.spacing[2],
  },
});

export default Button;

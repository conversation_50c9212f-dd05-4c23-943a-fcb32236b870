import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import { validateRequest, validateQuery } from '../middleware/validation';
import { eventValidation } from '../validation/schemas';
import { eventCreationLimiter, generalLimiter } from '../middleware/rateLimiting';
import { cacheMiddleware, invalidateCache } from '../middleware/cache';
import { asyncHandler } from '../middleware/errorHandler';
import {
  createEvent,
  getEvents,
  getEventById,
  updateEvent,
  deleteEvent,
  joinEvent,
  leaveEvent,
  getEventParticipants
} from '../controllers/eventController';

const router = Router();

// Apply asyncHandler to all routes to catch async errors
router.get('/', generalLimiter, cacheMiddleware(300), asyncHandler(getEvents));
router.post('/', 
  authenticateToken, 
  eventCreationLimiter,
  validateRequest(eventValidation.create),
  invalidateCache(['cache:events:*']),
  asyncHandler(createEvent)
);
router.get('/:id', generalLimiter, cacheMiddleware(600), asyncHandler(getEventById));
router.put('/:id', 
  authenticateToken, 
  validateRequest(eventValidation.update),
  invalidateCache(['cache:events:*', 'cache:event:*']),
  asyncHandler(updateEvent)
);
router.delete('/:id', authenticateToken, invalidateCache(['cache:events:*']), asyncHandler(deleteEvent));
router.post('/:id/join', authenticateToken, invalidateCache(['cache:event:*']), asyncHandler(joinEvent));
router.post('/:id/leave', authenticateToken, invalidateCache(['cache:event:*']), asyncHandler(leaveEvent));
router.get('/:id/participants', authenticateToken, asyncHandler(getEventParticipants));

export default router;


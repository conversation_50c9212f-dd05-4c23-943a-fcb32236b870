import { Request, Response } from 'express';
import { OAuth2Client } from 'google-auth-library';
import jwt from 'jsonwebtoken';
import { prisma } from '../config/database';
import { logger } from '../utils/logger';
import { generateTokens, verifyRefreshToken } from '../services/authService';

const googleClient = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);

export const googleAuth = async (req: Request, res: Response) => {
  try {
    const { idToken, accessToken, email, firstName, lastName, profilePicture, googleId } = req.body;

    logger.info(`Google auth attempt for email: ${email}`);

    // Verify Google ID token
    const ticket = await googleClient.verifyIdToken({
      idToken,
      audience: process.env.GOOGLE_CLIENT_ID,
    });

    const payload = ticket.getPayload();
    if (!payload || payload.email !== email) {
      return res.status(401).json({ error: 'Invalid Google token' });
    }

    // Check if user exists
    let user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        profilePicture: true,
        bio: true,
        university: true,
        graduationYear: true,
        major: true,
        interests: true,
        isVerified: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    if (!user) {
      // Create new user
      user = await prisma.user.create({
        data: {
          email,
          firstName,
          lastName,
          profilePicture,
          googleId,
          university: 'Not specified', // Will be updated in profile setup
          graduationYear: new Date().getFullYear(),
          major: 'Not specified',
          interests: [],
          isVerified: true, // Google OAuth users are pre-verified
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          profilePicture: true,
          bio: true,
          university: true,
          graduationYear: true,
          major: true,
          interests: true,
          isVerified: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
        }
      });

      logger.info(`New user created: ${user.id}`);
    } else {
      // Update existing user's Google ID if not set
      if (!user.googleId) {
        await prisma.user.update({
          where: { id: user.id },
          data: { googleId }
        });
      }

      logger.info(`Existing user logged in: ${user.id}`);
    }

    if (!user.isActive) {
      return res.status(403).json({ error: 'Account is deactivated' });
    }

    // Generate JWT tokens
    const tokens = await generateTokens(user.id);

    res.json({
      user,
      tokens,
      message: 'Authentication successful'
    });

  } catch (error: any) {
    logger.error('Google auth error:', error);
    res.status(500).json({ error: 'Authentication failed' });
  }
};

export const refreshToken = async (req: Request, res: Response) => {
  try {
    const { refreshToken } = req.body;

    const decoded = await verifyRefreshToken(refreshToken);
    if (!decoded) {
      return res.status(401).json({ error: 'Invalid refresh token' });
    }

    // Generate new access token
    const accessToken = jwt.sign(
      { userId: decoded.userId },
      process.env.JWT_SECRET!,
      { expiresIn: process.env.JWT_EXPIRES_IN }
    );

    res.json({ accessToken });

  } catch (error: any) {
    logger.error('Refresh token error:', error);
    res.status(401).json({ error: 'Token refresh failed' });
  }
};

export const logout = async (req: Request, res: Response) => {
  try {
    const { refreshToken } = req.body;

    if (refreshToken) {
      // Remove refresh token from database
      await prisma.refreshToken.deleteMany({
        where: { token: refreshToken }
      });
    }

    res.json({ message: 'Logged out successfully' });

  } catch (error: any) {
    logger.error('Logout error:', error);
    res.status(500).json({ error: 'Logout failed' });
  }
};
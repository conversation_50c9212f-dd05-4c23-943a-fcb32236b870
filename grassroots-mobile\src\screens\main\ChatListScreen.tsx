import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useDispatch, useSelector } from 'react-redux';
import { Ionicons } from '@expo/vector-icons';
import { AppDispatch, RootState } from '../../store';
import { fetchChatRooms, setCurrentChatRoom } from '../../store/slices/chatSlice';
import { Card, ChatItemSkeleton } from '../../components';
import GradientBackground from '../../components/GradientBackground';
import { useTheme } from '../../contexts/ThemeContext';
import { createTypographyStyles } from '../../utils/styles';

interface ChatListScreenProps {
  navigation: any;
}

const ChatListScreen: React.FC<ChatListScreenProps> = ({ navigation }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { chatRooms, isLoading } = useSelector((state: RootState) => state.chat);
  const { theme } = useTheme();
  const styles = createStyles(theme);

  useEffect(() => {
    dispatch(fetchChatRooms());
  }, [dispatch]);

  const handleChatPress = (chatRoom: any) => {
    dispatch(setCurrentChatRoom(chatRoom));
    navigation.navigate('Chat', { chatRoom });
  };

  const formatTime = (date: string | Date) => {
    const messageDate = new Date(date);
    const now = new Date();
    const diffInHours = (now.getTime() - messageDate.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return messageDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return messageDate.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const renderChatRoom = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.chatItem}
      onPress={() => handleChatPress(item)}
      activeOpacity={0.7}
    >
      {/* Left: Avatar */}
      <View style={styles.avatarSection}>
        <Image
          source={{ uri: item.participants[0]?.avatar || 'https://i.pravatar.cc/150?img=1' }}
          style={styles.chatAvatar}
        />
      </View>

      {/* Center: Content */}
      <View style={styles.contentSection}>
        <View style={styles.topRow}>
          <Text style={styles.chatName} numberOfLines={1}>
            {item.name}
          </Text>
          <Text style={styles.chatTime}>
            {item.lastMessage ? formatTime(item.lastMessage.createdAt) : ''}
          </Text>
        </View>

        <View style={styles.middleRow}>
          <Text style={styles.lastMessage} numberOfLines={1}>
            {item.lastMessage ? item.lastMessage.text : 'No messages yet'}
          </Text>
          {item.unreadCount > 0 && (
            <View style={styles.unreadBadge}>
              <Text style={styles.unreadText}>
                {item.unreadCount > 99 ? '99+' : item.unreadCount}
              </Text>
            </View>
          )}
        </View>

        <View style={styles.bottomRow}>
          <Text style={styles.eventTitle} numberOfLines={1}>
            📅 {item.eventTitle}
          </Text>
          <Text style={styles.participantCount}>
            {item.participants.length} members
          </Text>
        </View>
      </View>

      {/* Right: Arrow */}
      <View style={styles.arrowSection}>
        <Ionicons name="chevron-forward" size={20} color={theme.colors.text.tertiary} />
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="chatbubbles-outline" size={80} color="#C7C7CC" />
      <Text style={styles.emptyTitle}>No Group Chats Yet</Text>
      <Text style={styles.emptySubtitle}>
        Join events to connect with other participants and start chatting!
      </Text>
      <TouchableOpacity
        style={styles.exploreButton}
        onPress={() => navigation.navigate('Events')}
      >
        <Text style={styles.exploreButtonText}>Explore Events</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <GradientBackground variant="primary" style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <GradientBackground variant="secondary" style={styles.header}>
          <Text style={styles.headerTitle}>Group Chats</Text>
          <TouchableOpacity style={styles.headerButton}>
            <Ionicons name="add" size={24} color={theme.colors.primary[500]} />
          </TouchableOpacity>
        </GradientBackground>

      {isLoading && chatRooms.length === 0 ? (
        <View style={styles.list}>
          {[...Array(5)].map((_, index) => (
            <ChatItemSkeleton key={index} />
          ))}
        </View>
      ) : chatRooms.length === 0 ? (
        renderEmptyState()
      ) : (
        <FlatList
          data={chatRooms}
          renderItem={renderChatRoom}
          keyExtractor={(item) => item.id}
          style={styles.list}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isLoading}
              onRefresh={() => dispatch(fetchChatRooms())}
              tintColor={theme.colors.primary[500]}
              colors={[theme.colors.primary[500]]}
              progressBackgroundColor={theme.colors.background.primary}
            />
          }
          ItemSeparatorComponent={() => <View style={styles.separator} />}
        />
        )}
      </SafeAreaView>
    </GradientBackground>
  );
};

const createStyles = (theme: any) => {
  const typography = createTypographyStyles(theme);

  return StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: theme.spacing[5],
    paddingVertical: theme.spacing[4],
    ...theme.shadows.sm,
  },
  headerTitle: {
    ...typography.h3,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.bold,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  list: {
    flex: 1,
  },
  chatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing[5],
    paddingVertical: theme.spacing[4],
    backgroundColor: theme.colors.background.primary,
    height: 80,
  },
  avatarSection: {
    width: 50,
    height: 50,
    marginRight: theme.spacing[3],
    justifyContent: 'center',
    alignItems: 'center',
  },
  chatAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: theme.colors.background.secondary,
  },
  contentSection: {
    flex: 1,
    height: 50,
    justifyContent: 'space-between',
    marginRight: theme.spacing[3],
  },
  topRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 18,
  },
  middleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 16,
  },
  bottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 14,
  },
  chatName: {
    ...typography.body1,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.semibold,
    flex: 1,
  },
  chatTime: {
    ...typography.caption,
    color: theme.colors.text.tertiary,
    fontSize: 11,
  },
  lastMessage: {
    ...typography.body2,
    color: theme.colors.text.secondary,
    flex: 1,
    fontSize: 13,
  },
  unreadBadge: {
    backgroundColor: theme.colors.primary[500],
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  unreadText: {
    color: theme.colors.text.inverse,
    fontSize: 10,
    fontWeight: theme.typography.fontWeight.bold,
  },
  eventTitle: {
    ...typography.caption,
    color: theme.colors.primary[500],
    fontWeight: theme.typography.fontWeight.medium,
    fontSize: 11,
    flex: 1,
  },
  participantCount: {
    ...typography.caption,
    color: theme.colors.text.tertiary,
    fontSize: 10,
  },
  arrowSection: {
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  separator: {
    height: 1,
    backgroundColor: theme.colors.border.light,
    marginLeft: 68,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.spacing[10],
  },
  emptyTitle: {
    ...typography.h4,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.bold,
    marginTop: theme.spacing[5],
    marginBottom: theme.spacing[2],
  },
  emptySubtitle: {
    ...typography.body1,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: theme.spacing[8],
  },
  exploreButton: {
    backgroundColor: theme.colors.primary[500],
    paddingVertical: theme.spacing[3],
    paddingHorizontal: theme.spacing[8],
    borderRadius: theme.borderRadius.full,
    ...theme.shadows.base,
  },
  exploreButtonText: {
    ...typography.body1,
    color: theme.colors.text.inverse,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  });
};

export default ChatListScreen;

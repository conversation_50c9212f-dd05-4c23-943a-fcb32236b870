import { Request, Response } from 'express';
import { groupService } from '../services/groupService';
import { AuthRequest } from '../middleware/auth';
import { logger } from '../utils/logger';

export const getEventGroups = async (req: Request, res: Response) => {
  try {
    const { eventId } = req.params;
    const groups = await groupService.getGroupsByEvent(eventId);
    res.json({ groups });
  } catch (error: any) {
    logger.error('Get event groups error:', error);
    res.status(500).json({ error: 'Failed to fetch groups' });
  }
};

export const getGroupById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const group = await groupService.getGroupById(id);
    
    if (!group) {
      return res.status(404).json({ error: 'Group not found' });
    }

    res.json({ group });
  } catch (error: any) {
    logger.error('Get group by ID error:', error);
    res.status(500).json({ error: 'Failed to fetch group' });
  }
};

export const getUserGroups = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const groups = await groupService.getUserGroups(userId);
    res.json({ groups });
  } catch (error: any) {
    logger.error('Get user groups error:', error);
    res.status(500).json({ error: 'Failed to fetch user groups' });
  }
};

export const sendMessage = async (req: AuthRequest, res: Response) => {
  try {
    const { id: groupId } = req.params;
    const { content, messageType = 'TEXT' } = req.body;
    const senderId = req.user!.id;

    if (!content || content.trim().length === 0) {
      return res.status(400).json({ error: 'Message content is required' });
    }

    const message = await groupService.sendMessage(groupId, senderId, content, messageType);
    res.status(201).json({ message });
  } catch (error: any) {
    logger.error('Send message error:', error);
    
    if (error.message === 'User is not a member of this group') {
      return res.status(403).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to send message' });
  }
};

export const getGroupMessages = async (req: Request, res: Response) => {
  try {
    const { id: groupId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 50;

    const messages = await groupService.getGroupMessages(groupId, page, limit);
    res.json({ messages });
  } catch (error: any) {
    logger.error('Get group messages error:', error);
    res.status(500).json({ error: 'Failed to fetch messages' });
  }
};

export const createGroupsForEvent = async (req: Request, res: Response) => {
  try {
    const { eventId } = req.params;
    const groups = await groupService.createGroupsForEvent(eventId);
    res.json({ groups, message: 'Groups created successfully' });
  } catch (error: any) {
    logger.error('Create groups error:', error);
    res.status(500).json({ error: 'Failed to create groups' });
  }
};
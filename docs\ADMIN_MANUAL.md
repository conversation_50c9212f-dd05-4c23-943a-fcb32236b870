# VIBE App - Administrator Manual

**Version:** 1.0  
**Last Updated:** 2025-07-20  
**Target Audience:** VIBE Administrators, Event Coordinators, Moderators

---

## 🔐 Admin Access & Roles

### Admin Role Hierarchy

#### Super Admin
- **Full system access** - all features and data
- **User management** - create/modify/delete accounts
- **Event management** - create/edit/delete all events
- **Moderation oversight** - review all reports and actions
- **System configuration** - modify app settings
- **Analytics access** - comprehensive platform metrics

#### Event Admin
- **Event management** - create/edit curated events
- **Event analytics** - participation and engagement metrics
- **Location management** - add/modify event venues
- **User support** - help with event-related issues
- **Limited moderation** - event-specific reports only

#### Moderator
- **Safety reports** - review and resolve user reports
- **Content moderation** - monitor chats and profiles
- **User warnings** - issue warnings for violations
- **Account actions** - suspend/ban problematic users
- **Wall of shame** - manage public violation records

### Accessing Admin Panel

#### Web Dashboard
1. **Navigate to** admin.vibeapp.com
2. **Login with admin credentials**
3. **Two-factor authentication** required
4. **Dashboard loads** with role-appropriate features

#### Mobile Admin Access
1. **Open VIBE app** with admin account
2. **Admin menu** appears in profile section
3. **Limited functionality** compared to web dashboard
4. **Emergency moderation** capabilities available

---

## 🎯 Event Management

### Creating Curated Events

#### Event Planning Process
1. **Research campus calendar** for conflicts
2. **Identify target audience** and interests
3. **Secure venue** and necessary permits
4. **Plan group dynamics** and activities
5. **Set appropriate capacity** and group sizes

#### Event Creation Steps
1. **Access Event Dashboard**
2. **Click "Create New Event"**
3. **Fill Event Details:**
   - Title (engaging, descriptive)
   - Description (clear expectations)
   - Category (social, academic, sports, etc.)
   - Date and time (consider student schedules)
   - Duration (typically 2-3 hours)
4. **Set Capacity & Groups:**
   - Total capacity (usually 24-30 students)
   - Min/max group size (4-6 recommended)
   - Group formation algorithm preferences
5. **Location Setup:**
   - Venue name and address
   - GPS coordinates (auto-populated)
   - Accessibility information
   - Parking details
6. **Upload Event Image:**
   - High-quality, relevant photo
   - Represents event atmosphere
   - Follows brand guidelines
7. **Add Tags & Keywords:**
   - Searchable terms
   - Interest categories
   - Difficulty level if applicable

#### Event Types & Best Practices

##### Coffee & Conversation Events
- **Capacity:** 24 students (4 groups of 6)
- **Duration:** 2 hours
- **Location:** Campus café or coffee shop
- **Group Activity:** Structured conversation prompts
- **Success Metrics:** 80%+ satisfaction, 60%+ connections

##### Study Group Mixers
- **Capacity:** 30 students (6 groups of 5)
- **Duration:** 3 hours
- **Location:** Library or study space
- **Group Activity:** Subject-based collaboration
- **Success Metrics:** Academic benefit + social connections

##### Outdoor Adventures
- **Capacity:** 18 students (3 groups of 6)
- **Duration:** 4 hours
- **Location:** Campus recreation areas
- **Group Activity:** Team challenges or exploration
- **Success Metrics:** High engagement, memorable experiences

##### Creative Workshops
- **Capacity:** 20 students (4 groups of 5)
- **Duration:** 2.5 hours
- **Location:** Art studios or maker spaces
- **Group Activity:** Collaborative creative projects
- **Success Metrics:** Skill learning + relationship building

### Event Monitoring & Management

#### Pre-Event (24-48 hours before)
- **Check registration numbers** - minimum viable attendance
- **Review participant profiles** - flag any concerns
- **Confirm venue availability** and setup requirements
- **Send reminder notifications** to registered users
- **Form groups** using algorithm or manual assignment
- **Activate group chats** for pre-event coordination

#### During Event
- **Monitor group chats** for issues or concerns
- **Be available** for emergency contact
- **Document event** with photos/videos (with permission)
- **Address any problems** that arise
- **Ensure safety protocols** are followed

#### Post-Event (24-48 hours after)
- **Send review prompts** to all participants
- **Monitor connection reviews** and matches
- **Collect feedback** on event experience
- **Analyze participation metrics**
- **Plan follow-up events** based on success

### Event Analytics Dashboard

#### Key Metrics
- **Registration Rate:** Sign-ups vs. event views
- **Attendance Rate:** Actual vs. registered attendees
- **Engagement Score:** Chat activity and participation
- **Connection Rate:** Successful matches per participant
- **Satisfaction Score:** Post-event ratings
- **Retention Rate:** Participants joining future events

#### Performance Indicators
- **Green (Excellent):** 90%+ satisfaction, 70%+ connections
- **Yellow (Good):** 80-89% satisfaction, 50-69% connections
- **Red (Needs Improvement):** <80% satisfaction, <50% connections

---

## 👥 User Management

### User Account Overview

#### User Profile Information
- **Basic Details:** Name, email, university, verification status
- **Profile Content:** Photos, bio, voice notes, interests
- **Activity History:** Events joined, connections made, messages sent
- **Safety Record:** Reports filed, violations received
- **Account Status:** Active, suspended, banned, deleted

#### Account Status Management

##### Active Users
- **Full platform access** to all features
- **Can join events** and participate in chats
- **Able to make connections** and use all social features
- **Regular monitoring** for policy compliance

##### Suspended Users
- **Temporary restriction** (7-30 days typical)
- **Cannot join new events** but can complete current ones
- **Limited chat access** - existing groups only
- **Profile hidden** from discovery
- **Must acknowledge violation** to reactivate

##### Banned Users
- **Permanent platform removal**
- **All access revoked** immediately
- **Profile completely hidden**
- **Cannot create new accounts** (email blocked)
- **Connections notified** of account status

### User Verification Process

#### University Email Verification
1. **Automatic domain check** against approved university list
2. **Email confirmation** sent to provided address
3. **Link verification** within 24 hours required
4. **Manual review** for non-standard domains
5. **Verification badge** added to profile

#### Profile Authenticity Review
- **Photo verification** - real person, appropriate content
- **Bio content review** - appropriate language, accurate information
- **Voice note screening** - clear audio, appropriate content
- **Interest validation** - reasonable selections, not spam

### Bulk User Operations

#### Mass Communications
- **System announcements** to all users
- **Targeted messaging** by university, interests, or activity
- **Event promotions** to relevant user segments
- **Safety alerts** for urgent issues

#### Data Export & Analysis
- **User demographics** and engagement patterns
- **Event participation** trends and preferences
- **Connection success** rates and patterns
- **Safety incident** reporting and trends

---

## 🛡️ Safety & Moderation

### Report Management System

#### Report Categories & Severity
- **Critical (24hr response):** Safety concerns, harassment
- **High (48hr response):** Inappropriate content, fake profiles
- **Medium (72hr response):** Spam, minor violations
- **Low (1 week response):** General complaints, suggestions

#### Report Review Process
1. **Initial Triage:** Categorize and assign priority
2. **Evidence Collection:** Screenshots, chat logs, witness accounts
3. **Investigation:** Review user history and context
4. **Decision Making:** Determine appropriate action
5. **Action Implementation:** Warning, suspension, or ban
6. **Documentation:** Record decision and reasoning
7. **Follow-up:** Monitor for compliance and appeals

#### Available Actions

##### Warning System
- **First Warning:** Educational message about policy
- **Second Warning:** Temporary feature restrictions
- **Third Warning:** Account suspension consideration
- **Warning History:** Tracked for pattern analysis

##### Suspension Options
- **Chat Restriction:** Cannot send messages (1-7 days)
- **Event Restriction:** Cannot join new events (7-14 days)
- **Profile Hidden:** Not visible in discovery (14-30 days)
- **Full Suspension:** All features disabled (30+ days)

##### Permanent Actions
- **Account Termination:** Complete platform removal
- **IP Address Ban:** Prevent new account creation
- **Device Ban:** Block specific mobile devices
- **University Alert:** Notify campus authorities if needed

### Content Moderation

#### Automated Monitoring
- **Keyword Detection:** Inappropriate language flagging
- **Image Analysis:** Inappropriate photo detection
- **Spam Detection:** Repetitive or promotional content
- **Behavior Patterns:** Unusual activity identification

#### Manual Review Queue
- **Flagged Content:** Requires human review
- **User Reports:** Community-flagged violations
- **New User Content:** First-time poster review
- **Appeal Requests:** Disputed moderation decisions

### Wall of Shame Management

#### Inclusion Criteria
- **Confirmed Violations:** Only after investigation
- **Severity Threshold:** Multiple violations or serious single incident
- **Community Impact:** Behavior affecting multiple users
- **Pattern Recognition:** Repeat offenders highlighted

#### Display Information
- **Violation Type:** Category of confirmed violation
- **Violation Count:** Number of confirmed incidents
- **Date Range:** When violations occurred
- **Anonymized Details:** General description without personal info

---

## 📊 Analytics & Reporting

### Platform Metrics Dashboard

#### User Engagement
- **Daily Active Users (DAU):** Users opening app daily
- **Weekly Active Users (WAU):** Users engaging weekly
- **Monthly Active Users (MAU):** Users active monthly
- **Session Duration:** Average time spent in app
- **Feature Usage:** Most/least used app features

#### Event Performance
- **Event Creation Rate:** New events per week/month
- **Registration Conversion:** Views to sign-ups ratio
- **Attendance Rate:** Registered vs. actual attendees
- **Event Satisfaction:** Average post-event ratings
- **Repeat Participation:** Users joining multiple events

#### Connection Success
- **Match Rate:** Successful mutual connections
- **Connection Type Distribution:** Friend vs. spark ratios
- **Long-term Engagement:** Connections maintaining contact
- **Cross-Event Connections:** Users connecting across multiple events

#### Safety Metrics
- **Report Volume:** Number of reports per time period
- **Resolution Time:** Average time to resolve reports
- **Action Distribution:** Types of moderation actions taken
- **Repeat Offenders:** Users with multiple violations

### Custom Reports

#### University-Specific Analytics
- **Campus Engagement:** Activity levels by university
- **Event Preferences:** Popular categories by campus
- **Connection Patterns:** Social dynamics by university
- **Safety Incidents:** Campus-specific safety trends

#### Demographic Insights
- **Age Distribution:** User age ranges and preferences
- **Academic Year:** Freshman vs. senior engagement
- **Interest Correlations:** Popular interest combinations
- **Geographic Patterns:** Location-based usage trends

### Data Export & Integration

#### Export Formats
- **CSV Files:** For spreadsheet analysis
- **JSON Data:** For technical integration
- **PDF Reports:** For presentation purposes
- **API Access:** Real-time data integration

#### Privacy Compliance
- **Data Anonymization:** Personal identifiers removed
- **Aggregated Metrics:** Individual user privacy protected
- **GDPR Compliance:** European user data protection
- **FERPA Compliance:** Student privacy regulations

---

## ⚙️ System Configuration

### Platform Settings

#### Global Configuration
- **University Domain List:** Approved email domains
- **Event Capacity Limits:** Maximum attendees per event
- **Group Size Parameters:** Min/max group member counts
- **Chat Message Limits:** Rate limiting and history retention
- **File Upload Limits:** Size restrictions for photos/audio

#### Feature Toggles
- **New User Registration:** Enable/disable sign-ups
- **Event Creation:** Allow community events
- **Chat Features:** Enable/disable specific chat features
- **Connection Reviews:** Modify review time windows
- **Safety Features:** Adjust reporting and moderation tools

### Notification Management

#### System Notifications
- **Maintenance Alerts:** Scheduled downtime notifications
- **Feature Updates:** New feature announcements
- **Policy Changes:** Terms of service updates
- **Emergency Alerts:** Critical safety information

#### User Notification Settings
- **Default Preferences:** New user notification settings
- **Opt-out Options:** User control over notification types
- **Frequency Limits:** Prevent notification spam
- **Delivery Methods:** Push, email, in-app preferences

### Integration Management

#### Third-Party Services
- **Email Service:** SendGrid configuration and monitoring
- **Cloud Storage:** Google Cloud Storage management
- **Analytics:** Google Analytics integration
- **Push Notifications:** Firebase Cloud Messaging setup

#### API Management
- **Rate Limiting:** Prevent API abuse
- **Authentication:** Secure API access
- **Monitoring:** API usage and performance tracking
- **Documentation:** Keep API docs updated

---

## 🚨 Emergency Procedures

### Critical Incident Response

#### Safety Emergencies
1. **Immediate Assessment:** Determine severity and scope
2. **User Safety:** Ensure immediate user protection
3. **Platform Response:** Implement emergency restrictions if needed
4. **Authority Contact:** Notify campus security or police
5. **Documentation:** Record all actions and communications
6. **Follow-up:** Monitor situation and provide support

#### Technical Emergencies
1. **System Monitoring:** Identify scope of technical issues
2. **User Communication:** Notify users of service disruptions
3. **Emergency Fixes:** Implement immediate solutions
4. **Escalation:** Contact technical support team
5. **Recovery:** Restore full service and verify functionality
6. **Post-Incident:** Analyze cause and prevent recurrence

### Contact Information

#### Emergency Contacts
- **Campus Security:** [University-specific numbers]
- **Technical Support:** <EMAIL>
- **Legal Team:** <EMAIL>
- **Executive Team:** <EMAIL>

#### Escalation Procedures
- **Level 1:** Moderator response (immediate)
- **Level 2:** Admin team involvement (within 1 hour)
- **Level 3:** Executive team notification (within 4 hours)
- **Level 4:** External authority involvement (as needed)

---

**Admin Support:** <EMAIL>  
**Technical Issues:** <EMAIL>  
**Emergency Line:** +1-XXX-XXX-XXXX (24/7)

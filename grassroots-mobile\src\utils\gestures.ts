import { PanGestureHandler, State } from 'react-native-gesture-handler';
import { Animated, Dimensions } from 'react-native';
import { HapticUtils } from './haptics';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Gesture utility functions for enhanced interactions
export class GestureUtils {
  // Swipe gesture configuration
  static swipeConfig = {
    minVelocity: 500,
    minDistance: 50,
    maxOffPath: 100,
  };

  // Pull to refresh configuration
  static pullToRefreshConfig = {
    threshold: 80,
    maxPull: 120,
    snapBackDuration: 300,
  };

  // Card swipe configuration (for Tinder-like interactions)
  static cardSwipeConfig = {
    swipeThreshold: screenWidth * 0.3,
    rotationFactor: 0.1,
    scaleOnSwipe: 0.95,
  };

  // Detect swipe direction
  static detectSwipeDirection(
    gestureState: any
  ): 'left' | 'right' | 'up' | 'down' | null {
    const { dx, dy, vx, vy } = gestureState;
    
    // Check if gesture meets minimum requirements
    if (
      Math.abs(dx) < this.swipeConfig.minDistance &&
      Math.abs(dy) < this.swipeConfig.minDistance
    ) {
      return null;
    }

    if (
      Math.abs(vx) < this.swipeConfig.minVelocity &&
      Math.abs(vy) < this.swipeConfig.minVelocity
    ) {
      return null;
    }

    // Determine primary direction
    if (Math.abs(dx) > Math.abs(dy)) {
      return dx > 0 ? 'right' : 'left';
    } else {
      return dy > 0 ? 'down' : 'up';
    }
  }

  // Create pull to refresh gesture handler
  static createPullToRefresh(
    translateY: Animated.Value,
    onRefresh: () => void,
    isRefreshing: boolean
  ) {
    return {
      onGestureEvent: Animated.event(
        [{ nativeEvent: { translationY: translateY } }],
        { useNativeDriver: false }
      ),
      onHandlerStateChange: (event: any) => {
        if (event.nativeEvent.state === State.END) {
          const { translationY } = event.nativeEvent;
          
          if (
            translationY > this.pullToRefreshConfig.threshold &&
            !isRefreshing
          ) {
            HapticUtils.pullToRefresh();
            onRefresh();
          }
          
          // Snap back animation
          Animated.timing(translateY, {
            toValue: 0,
            duration: this.pullToRefreshConfig.snapBackDuration,
            useNativeDriver: false,
          }).start();
        }
      },
    };
  }

  // Create card swipe gesture handler
  static createCardSwipe(
    translateX: Animated.Value,
    translateY: Animated.Value,
    rotate: Animated.Value,
    scale: Animated.Value,
    onSwipeLeft?: () => void,
    onSwipeRight?: () => void
  ) {
    return {
      onGestureEvent: Animated.event(
        [
          {
            nativeEvent: {
              translationX: translateX,
              translationY: translateY,
            },
          },
        ],
        { useNativeDriver: false }
      ),
      onHandlerStateChange: (event: any) => {
        const { state, translationX, translationY } = event.nativeEvent;
        
        if (state === State.ACTIVE) {
          // Update rotation based on horizontal movement
          const rotation = translationX * this.cardSwipeConfig.rotationFactor;
          rotate.setValue(rotation);
          
          // Update scale based on movement
          const distance = Math.sqrt(translationX ** 2 + translationY ** 2);
          const scaleValue = Math.max(
            this.cardSwipeConfig.scaleOnSwipe,
            1 - distance / (screenWidth * 2)
          );
          scale.setValue(scaleValue);
        }
        
        if (state === State.END) {
          const swipeThreshold = this.cardSwipeConfig.swipeThreshold;
          
          if (translationX > swipeThreshold) {
            // Swipe right
            HapticUtils.swipeAction();
            this.animateCardOut(translateX, translateY, rotate, scale, 'right');
            onSwipeRight?.();
          } else if (translationX < -swipeThreshold) {
            // Swipe left
            HapticUtils.swipeAction();
            this.animateCardOut(translateX, translateY, rotate, scale, 'left');
            onSwipeLeft?.();
          } else {
            // Snap back
            this.animateCardBack(translateX, translateY, rotate, scale);
          }
        }
      },
    };
  }

  // Animate card out of screen
  static animateCardOut(
    translateX: Animated.Value,
    translateY: Animated.Value,
    rotate: Animated.Value,
    scale: Animated.Value,
    direction: 'left' | 'right'
  ) {
    const toValue = direction === 'right' ? screenWidth : -screenWidth;
    
    Animated.parallel([
      Animated.timing(translateX, {
        toValue,
        duration: 300,
        useNativeDriver: false,
      }),
      Animated.timing(scale, {
        toValue: 0.8,
        duration: 300,
        useNativeDriver: false,
      }),
    ]).start();
  }

  // Animate card back to center
  static animateCardBack(
    translateX: Animated.Value,
    translateY: Animated.Value,
    rotate: Animated.Value,
    scale: Animated.Value
  ) {
    Animated.parallel([
      Animated.spring(translateX, {
        toValue: 0,
        useNativeDriver: false,
      }),
      Animated.spring(translateY, {
        toValue: 0,
        useNativeDriver: false,
      }),
      Animated.spring(rotate, {
        toValue: 0,
        useNativeDriver: false,
      }),
      Animated.spring(scale, {
        toValue: 1,
        useNativeDriver: false,
      }),
    ]).start();
  }

  // Create long press gesture
  static createLongPress(
    onLongPress: () => void,
    duration: number = 500
  ) {
    return {
      onLongPress: () => {
        HapticUtils.medium();
        onLongPress();
      },
      delayLongPress: duration,
    };
  }

  // Create double tap gesture
  static createDoubleTap(
    onDoubleTap: () => void,
    delay: number = 300
  ) {
    let lastTap = 0;
    
    return {
      onPress: () => {
        const now = Date.now();
        if (now - lastTap < delay) {
          HapticUtils.medium();
          onDoubleTap();
        }
        lastTap = now;
      },
    };
  }

  // Create pinch to zoom gesture
  static createPinchZoom(
    scale: Animated.Value,
    minScale: number = 0.5,
    maxScale: number = 3
  ) {
    return {
      onGestureEvent: Animated.event(
        [{ nativeEvent: { scale } }],
        { useNativeDriver: false }
      ),
      onHandlerStateChange: (event: any) => {
        if (event.nativeEvent.state === State.END) {
          const currentScale = event.nativeEvent.scale;
          
          if (currentScale < minScale) {
            Animated.spring(scale, {
              toValue: minScale,
              useNativeDriver: false,
            }).start();
          } else if (currentScale > maxScale) {
            Animated.spring(scale, {
              toValue: maxScale,
              useNativeDriver: false,
            }).start();
          }
        }
      },
    };
  }

  // Create slide to action gesture (like iOS slide to delete)
  static createSlideToAction(
    translateX: Animated.Value,
    onAction: () => void,
    actionThreshold: number = 80
  ) {
    return {
      onGestureEvent: Animated.event(
        [{ nativeEvent: { translationX: translateX } }],
        { useNativeDriver: false }
      ),
      onHandlerStateChange: (event: any) => {
        const { state, translationX } = event.nativeEvent;
        
        if (state === State.END) {
          if (Math.abs(translationX) > actionThreshold) {
            HapticUtils.swipeAction();
            onAction();
          } else {
            // Snap back
            Animated.spring(translateX, {
              toValue: 0,
              useNativeDriver: false,
            }).start();
          }
        }
      },
    };
  }
}

// Hook for gesture handling
export const useGestures = () => {
  return {
    createPullToRefresh: GestureUtils.createPullToRefresh,
    createCardSwipe: GestureUtils.createCardSwipe,
    createLongPress: GestureUtils.createLongPress,
    createDoubleTap: GestureUtils.createDoubleTap,
    createPinchZoom: GestureUtils.createPinchZoom,
    createSlideToAction: GestureUtils.createSlideToAction,
    detectSwipeDirection: GestureUtils.detectSwipeDirection,
  };
};

export default GestureUtils;

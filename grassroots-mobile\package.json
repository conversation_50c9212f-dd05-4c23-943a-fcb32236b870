{"name": "grassroots-mobile", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-native-google-signin/google-signin": "^15.0.0", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "@reduxjs/toolkit": "^2.8.2", "expo": "53.0.20", "expo-auth-session": "~5.5.2", "expo-av": "^15.1.7", "expo-camera": "~16.1.11", "expo-crypto": "^14.1.5", "expo-haptics": "^14.1.4", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-location": "^18.1.6", "expo-status-bar": "~2.2.3", "expo-web-browser": "^14.2.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-elements": "^3.4.3", "react-native-maps": "1.20.1", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "react-redux": "^9.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}
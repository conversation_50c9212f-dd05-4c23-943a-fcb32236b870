import React, { useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  Animated,
  Dimensions,
  ViewStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import Typography from './Typography';

const { width, height } = Dimensions.get('window');

interface SuccessAnimationProps {
  visible: boolean;
  type?: 'event-join' | 'connection-made' | 'profile-update' | 'message-sent';
  title?: string;
  subtitle?: string;
  onComplete?: () => void;
  style?: ViewStyle;
}

const SuccessAnimation: React.FC<SuccessAnimationProps> = ({
  visible,
  type = 'event-join',
  title,
  subtitle,
  onComplete,
  style,
}) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  
  // Animation values
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const bounceAnim = useRef(new Animated.Value(0)).current;
  const confettiAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (visible) {
      // Start the celebration animation sequence
      Animated.sequence([
        // Initial scale and fade in
        Animated.parallel([
          Animated.spring(scaleAnim, {
            toValue: 1,
            tension: 100,
            friction: 8,
            useNativeDriver: true,
          }),
          Animated.timing(opacityAnim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }),
        ]),
        // Bounce effect
        Animated.spring(bounceAnim, {
          toValue: 1,
          tension: 150,
          friction: 6,
          useNativeDriver: true,
        }),
        // Confetti animation
        Animated.timing(confettiAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        // Hold for a moment
        Animated.delay(1000),
        // Fade out
        Animated.parallel([
          Animated.timing(opacityAnim, {
            toValue: 0,
            duration: 400,
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 0.8,
            duration: 400,
            useNativeDriver: true,
          }),
        ]),
      ]).start(() => {
        // Reset animations
        scaleAnim.setValue(0);
        opacityAnim.setValue(0);
        bounceAnim.setValue(0);
        confettiAnim.setValue(0);
        onComplete?.();
      });
    }
  }, [visible]);

  const getSuccessConfig = () => {
    switch (type) {
      case 'event-join':
        return {
          icon: 'checkmark-circle' as keyof typeof Ionicons.glyphMap,
          color: theme.colors.success,
          defaultTitle: 'Event Joined!',
          defaultSubtitle: 'Get ready to vibe with new people',
        };
      case 'connection-made':
        return {
          icon: 'heart' as keyof typeof Ionicons.glyphMap,
          color: theme.colors.accent.pink,
          defaultTitle: 'Connection Made!',
          defaultSubtitle: 'A new friendship begins',
        };
      case 'profile-update':
        return {
          icon: 'person-circle' as keyof typeof Ionicons.glyphMap,
          color: theme.colors.primary[500],
          defaultTitle: 'Profile Updated!',
          defaultSubtitle: 'Looking great!',
        };
      case 'message-sent':
        return {
          icon: 'paper-plane' as keyof typeof Ionicons.glyphMap,
          color: theme.colors.info,
          defaultTitle: 'Message Sent!',
          defaultSubtitle: 'Your message is on its way',
        };
      default:
        return {
          icon: 'checkmark-circle' as keyof typeof Ionicons.glyphMap,
          color: theme.colors.success,
          defaultTitle: 'Success!',
          defaultSubtitle: 'Action completed successfully',
        };
    }
  };

  const config = getSuccessConfig();

  if (!visible) return null;

  return (
    <View style={[styles.overlay, style]}>
      <Animated.View
        style={[
          styles.container,
          {
            opacity: opacityAnim,
            transform: [
              { scale: scaleAnim },
              {
                translateY: bounceAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, -10],
                }),
              },
            ],
          },
        ]}
      >
        {/* Main success icon */}
        <Animated.View
          style={[
            styles.iconContainer,
            {
              backgroundColor: config.color,
              transform: [
                {
                  scale: bounceAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [1, 1.2],
                  }),
                },
              ],
            },
          ]}
        >
          <Ionicons
            name={config.icon}
            size={60}
            color={theme.colors.text.inverse}
          />
        </Animated.View>

        {/* Success text */}
        <View style={styles.textContainer}>
          <Typography
            variant="h3"
            color="primary"
            align="center"
            style={styles.title}
          >
            {title || config.defaultTitle}
          </Typography>
          <Typography
            variant="body1"
            color="secondary"
            align="center"
            style={styles.subtitle}
          >
            {subtitle || config.defaultSubtitle}
          </Typography>
        </View>

        {/* Confetti particles */}
        {[...Array(8)].map((_, index) => (
          <Animated.View
            key={index}
            style={[
              styles.confetti,
              {
                left: `${20 + (index * 10)}%`,
                backgroundColor: index % 2 === 0 ? theme.colors.primary[400] : theme.colors.accent.pink,
                transform: [
                  {
                    translateY: confettiAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, -100 - (index * 20)],
                    }),
                  },
                  {
                    rotate: confettiAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0deg', `${360 * (index % 2 === 0 ? 1 : -1)}deg`],
                    }),
                  },
                ],
                opacity: confettiAnim.interpolate({
                  inputRange: [0, 0.5, 1],
                  outputRange: [0, 1, 0],
                }),
              },
            ]}
          />
        ))}
      </Animated.View>
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  container: {
    alignItems: 'center',
    padding: theme.spacing[8],
    backgroundColor: theme.colors.background.primary,
    borderRadius: theme.borderRadius['2xl'],
    ...theme.shadows.lg,
    maxWidth: width * 0.8,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.spacing[6],
    ...theme.shadows.md,
  },
  textContainer: {
    alignItems: 'center',
    gap: theme.spacing[2],
  },
  title: {
    marginBottom: theme.spacing[1],
  },
  subtitle: {
    textAlign: 'center',
  },
  confetti: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
  },
});

export default SuccessAnimation;

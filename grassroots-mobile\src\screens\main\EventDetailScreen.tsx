import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Alert,
  Image,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '../../store';
import { optInToEvent } from '../../store/slices/eventsSlice';
import { incrementEventsAttended } from '../../store/slices/userSlice';
import { Button, SuccessAnimation } from '../../components';
import { useTheme } from '../../contexts/ThemeContext';
import { createTypographyStyles } from '../../utils/styles';

const { height } = Dimensions.get('window');

interface EventDetailScreenProps {
  navigation: any;
  route: {
    params: {
      event: {
        id: string;
        title: string;
        description: string;
        startTime: string;
        endTime: string;
        location: {
          name: string;
          address: string;
        };
        imageUrl?: string;
        currentAttendees: number;
        capacity: number;
        category: string;
        eventType: string;
        tags?: string[];
        isOptedIn: boolean;
      };
    };
  };
}

const EventDetailScreen: React.FC<EventDetailScreenProps> = ({ navigation, route }) => {
  const { event } = route.params;
  const dispatch = useDispatch<AppDispatch>();
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const typography = createTypographyStyles(theme);

  const [isJoined, setIsJoined] = useState(event.isOptedIn);
  const [showSuccess, setShowSuccess] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleJoinEvent = async () => {
    if (isJoined) return;

    setIsLoading(true);
    try {
      dispatch(optInToEvent(event.id));
      dispatch(incrementEventsAttended());
      setIsJoined(true);
      setShowSuccess(true);
    } catch (error) {
      Alert.alert('Error', 'Failed to join event. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleShare = async () => {
    try {
      // In a real app, this would use Expo Sharing or React Native Share
      const shareMessage = `Check out this event on VIBE!\n\n${event.title}\n${formatDate(event.startTime)} at ${formatTime(event.startTime)}\n📍 ${event.location.name}\n\nJoin me and meet new people!`;

      Alert.alert(
        'Share Event',
        shareMessage,
        [
          { text: 'Copy Link', onPress: () => Alert.alert('Copied!', 'Event link copied to clipboard') },
          { text: 'Cancel', style: 'cancel' },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Unable to share event at this time');
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />

      {/* Hero Image Section */}
      <View style={styles.heroSection}>
        <Image
          source={{ uri: event.imageUrl || 'https://images.unsplash.com/photo-1511795409834-ef04bbd61622?w=400' }}
          style={styles.heroImage}
          resizeMode="cover"
        />
        <LinearGradient
          colors={['rgba(0,0,0,0.3)', 'transparent', 'rgba(0,0,0,0.8)']}
          style={styles.heroGradient}
        />

        {/* Header Controls */}
        <SafeAreaView style={styles.headerControls}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={handleShare}
          >
            <Ionicons name="share-outline" size={24} color="white" />
          </TouchableOpacity>
        </SafeAreaView>

        {/* Event Badge */}
        <View style={styles.eventBadgeContainer}>
          <View style={styles.eventBadge}>
            <Text style={styles.eventBadgeText}>
              {event.eventType === 'curated' ? '✨ VIBE Curated' : '👥 Community Event'}
            </Text>
          </View>
        </View>
      </View>

      {/* Content Section */}
      <View style={styles.contentSection}>
        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          bounces={false}
        >
          {/* Event Title */}
          <View style={styles.titleSection}>
            <Text style={styles.eventTitle}>{event.title}</Text>
            <Text style={styles.eventCategory}>{event.category.toUpperCase()}</Text>
          </View>

          {/* Quick Info Cards */}
          <View style={styles.quickInfoSection}>
            <View style={styles.infoRow}>
              <View style={styles.infoCard}>
                <View style={styles.infoIconContainer}>
                  <Ionicons name="calendar" size={20} color={theme.colors.primary[500]} />
                </View>
                <View style={styles.infoContent}>
                  <Text style={styles.infoLabel}>Date</Text>
                  <Text style={styles.infoValue}>{formatDate(event.startTime)}</Text>
                </View>
              </View>

              <View style={styles.infoCard}>
                <View style={styles.infoIconContainer}>
                  <Ionicons name="time" size={20} color={theme.colors.primary[500]} />
                </View>
                <View style={styles.infoContent}>
                  <Text style={styles.infoLabel}>Time</Text>
                  <Text style={styles.infoValue}>{formatTime(event.startTime)}</Text>
                </View>
              </View>
            </View>

            <View style={styles.infoRow}>
              <View style={styles.infoCard}>
                <View style={styles.infoIconContainer}>
                  <Ionicons name="location" size={20} color={theme.colors.primary[500]} />
                </View>
                <View style={styles.infoContent}>
                  <Text style={styles.infoLabel}>Location</Text>
                  <Text style={styles.infoValue}>{event.location.name}</Text>
                </View>
              </View>

              <View style={styles.infoCard}>
                <View style={styles.infoIconContainer}>
                  <Ionicons name="people" size={20} color={theme.colors.primary[500]} />
                </View>
                <View style={styles.infoContent}>
                  <Text style={styles.infoLabel}>Attendees</Text>
                  <Text style={styles.infoValue}>{event.currentAttendees}/{event.capacity}</Text>
                </View>
              </View>
            </View>
          </View>

          {/* Description */}
          <View style={styles.descriptionSection}>
            <Text style={styles.sectionTitle}>About This Event</Text>
            <Text style={styles.description}>{event.description}</Text>
          </View>

          {/* Location Details */}
          <View style={styles.locationSection}>
            <Text style={styles.sectionTitle}>Location</Text>
            <View style={styles.locationCard}>
              <Ionicons name="location-outline" size={24} color={theme.colors.primary[500]} />
              <View style={styles.locationInfo}>
                <Text style={styles.locationName}>{event.location.name}</Text>
                <Text style={styles.locationAddress}>{event.location.address}</Text>
              </View>
            </View>
          </View>

          {/* Tags */}
          {event.tags && event.tags.length > 0 && (
            <View style={styles.tagsSection}>
              <Text style={styles.sectionTitle}>Tags</Text>
              <View style={styles.tagsContainer}>
                {event.tags.map((tag, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>#{tag}</Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* Bottom Spacing */}
          <View style={styles.bottomSpacing} />
        </ScrollView>

        {/* Fixed Bottom Action */}
        <View style={styles.bottomAction}>
          <View style={styles.attendeeInfo}>
            <Text style={styles.attendeeCount}>
              {event.currentAttendees} of {event.capacity} joined
            </Text>
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  { width: `${(event.currentAttendees / event.capacity) * 100}%` }
                ]}
              />
            </View>
          </View>

          <Button
            title={isJoined ? "✓ You're Going!" : "Join Event"}
            onPress={handleJoinEvent}
            variant={isJoined ? "secondary" : "primary"}
            size="large"
            disabled={isJoined || isLoading}
            loading={isLoading}
            style={styles.joinButton}
          />
        </View>
      </View>

      {/* Success Animation */}
      <SuccessAnimation
        visible={showSuccess}
        type="event-join"
        title="Event Joined!"
        subtitle="Get ready to vibe with new people"
        onComplete={() => setShowSuccess(false)}
      />
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  heroSection: {
    height: height * 0.4,
    position: 'relative',
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  heroGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  headerControls: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: theme.spacing[4],
    paddingTop: theme.spacing[2],
  },
  headerButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  eventBadgeContainer: {
    position: 'absolute',
    bottom: theme.spacing[4],
    left: theme.spacing[4],
  },
  eventBadge: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    paddingHorizontal: theme.spacing[3],
    paddingVertical: theme.spacing[2],
    borderRadius: theme.borderRadius.full,
  },
  eventBadgeText: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.text.primary,
  },
  contentSection: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
    borderTopLeftRadius: theme.borderRadius.xl,
    borderTopRightRadius: theme.borderRadius.xl,
    marginTop: -theme.spacing[6],
    paddingTop: theme.spacing[6],
  },
  scrollView: {
    flex: 1,
  },
  titleSection: {
    paddingHorizontal: theme.spacing[5],
    paddingBottom: theme.spacing[5],
  },
  eventTitle: {
    fontSize: theme.typography.fontSize['2xl'],
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.text.primary,
    marginBottom: theme.spacing[2],
    lineHeight: 32,
  },
  eventCategory: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.primary[500],
    letterSpacing: 1,
  },
  quickInfoSection: {
    paddingHorizontal: theme.spacing[5],
    marginBottom: theme.spacing[6],
  },
  infoRow: {
    flexDirection: 'row',
    gap: theme.spacing[3],
    marginBottom: theme.spacing[3],
  },
  infoCard: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.background.card,
    padding: theme.spacing[4],
    borderRadius: theme.borderRadius.lg,
    ...theme.shadows.sm,
  },
  infoIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary[50],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing[3],
  },
  infoContent: {
    flex: 1,
  },
  infoLabel: {
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.medium,
    color: theme.colors.text.tertiary,
    marginBottom: theme.spacing[1],
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  infoValue: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.text.primary,
  },
  descriptionSection: {
    paddingHorizontal: theme.spacing[5],
    marginBottom: theme.spacing[6],
  },
  sectionTitle: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.text.primary,
    marginBottom: theme.spacing[3],
  },
  description: {
    fontSize: theme.typography.fontSize.base,
    lineHeight: 24,
    color: theme.colors.text.secondary,
  },
  locationSection: {
    paddingHorizontal: theme.spacing[5],
    marginBottom: theme.spacing[6],
  },
  locationCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.background.card,
    padding: theme.spacing[4],
    borderRadius: theme.borderRadius.lg,
    ...theme.shadows.sm,
  },
  locationInfo: {
    marginLeft: theme.spacing[3],
    flex: 1,
  },
  locationName: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.text.primary,
    marginBottom: theme.spacing[1],
  },
  locationAddress: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.text.secondary,
  },
  tagsSection: {
    paddingHorizontal: theme.spacing[5],
    marginBottom: theme.spacing[6],
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing[2],
  },
  tag: {
    backgroundColor: theme.colors.background.tertiary,
    paddingHorizontal: theme.spacing[3],
    paddingVertical: theme.spacing[2],
    borderRadius: theme.borderRadius.md,
  },
  tagText: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.primary[600],
    fontWeight: theme.typography.fontWeight.medium,
  },
  bottomSpacing: {
    height: 120,
  },
  bottomAction: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: theme.colors.background.primary,
    paddingHorizontal: theme.spacing[5],
    paddingVertical: theme.spacing[4],
    borderTopWidth: 1,
    borderTopColor: theme.colors.border.light,
    ...theme.shadows.lg,
  },
  attendeeInfo: {
    marginBottom: theme.spacing[3],
  },
  attendeeCount: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing[2],
    textAlign: 'center',
  },
  progressBar: {
    height: 4,
    backgroundColor: theme.colors.background.tertiary,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: theme.colors.primary[500],
    borderRadius: 2,
  },
  joinButton: {
    width: '100%',
  },
});

export default EventDetailScreen;
import { moderationService } from '../../src/services/moderationService';
import { createTestUser, cleanupDatabase } from '../utils/testHelpers';

describe('ModerationService', () => {
  afterEach(async () => {
    await cleanupDatabase();
  });

  describe('submitReport', () => {
    it('should create a new report', async () => {
      const reporter = await createTestUser();
      const reported = await createTestUser();

      const report = await moderationService.submitReport(reporter.id, {
        reportedUserId: reported.id,
        reason: 'HARASSMENT',
        description: 'User was being inappropriate in chat'
      });

      expect(report.reporterId).toBe(reporter.id);
      expect(report.reportedUserId).toBe(reported.id);
      expect(report.reason).toBe('HARASSMENT');
      expect(report.status).toBe('PENDING');
    });

    it('should set priority based on reason', async () => {
      const reporter = await createTestUser();
      const reported = await createTestUser();

      const safetyReport = await moderationService.submitReport(reporter.id, {
        reportedUserId: reported.id,
        reason: 'SAFETY_CONCERN',
        description: 'User made threatening comments'
      });

      expect(safetyReport.priority).toBe('HIGH');

      const spamReport = await moderationService.submitReport(reporter.id, {
        reportedUserId: reported.id,
        reason: 'SPAM',
        description: 'User is sending spam messages'
      });

      expect(spamReport.priority).toBe('MEDIUM');
    });
  });

  describe('reviewReport', () => {
    it('should resolve report and take action', async () => {
      const reporter = await createTestUser();
      const reported = await createTestUser();
      const admin = await createTestUser({ email: '<EMAIL>' });

      const report = await moderationService.submitReport(reporter.id, {
        reportedUserId: reported.id,
        reason: 'HARASSMENT',
        description: 'User was being inappropriate'
      });

      const result = await moderationService.reviewReport(report.id, admin.id, {
        resolution: 'RESOLVED',
        actionType: 'WARNING',
        notes: 'User warned for inappropriate behavior'
      });

      expect(result.status).toBe('RESOLVED');
      expect(result.reviewedById).toBe(admin.id);
    });
  });

  describe('checkContentFilter', () => {
    it('should detect banned words', () => {
      const result = moderationService.checkContentFilter('This is a damn test message');
      
      expect(result.hasBannedWords).toBe(true);
      expect(result.bannedWords).toContain('damn');
    });

    it('should detect suspicious patterns', () => {
      const result = moderationService.checkContentFilter('Contact <NAME_EMAIL> for business');
      
      expect(result.hasSuspiciousPatterns).toBe(true);
      expect(result.suspiciousPatterns).toContain('email');
    });

    it('should pass clean content', () => {
      const result = moderationService.checkContentFilter('This is a perfectly normal message');
      
      expect(result.hasBannedWords).toBe(false);
      expect(result.hasSuspiciousPatterns).toBe(false);
      expect(result.isClean).toBe(true);
    });
  });
});
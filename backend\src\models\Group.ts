export interface Group {
  id: string;
  eventId: string;
  name: string;
  description?: string;
  maxMembers: number;
  currentMembers: number;
  createdAt: Date;
}

export interface GroupMember {
  id: string;
  groupId: string;
  userId: string;
  joinedAt: Date;
  role: 'MEMBER' | 'ADMIN';
}

export interface Message {
  id: string;
  groupId: string;
  senderId: string;
  content?: string;
  messageType: 'TEXT' | 'IMAGE' | 'VOICE' | 'SYSTEM';
  fileUrl?: string;
  fileName?: string;
  fileSize?: number;
  isEdited: boolean;
  editedAt?: Date;
  createdAt: Date;
}

export interface CreateGroupData {
  eventId: string;
  name: string;
  description?: string;
  maxMembers?: number;
}
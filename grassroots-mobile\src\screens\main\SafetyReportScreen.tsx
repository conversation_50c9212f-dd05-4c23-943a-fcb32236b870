import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useDispatch, useSelector } from 'react-redux';
import { Ionicons } from '@expo/vector-icons';
import { AppDispatch, RootState } from '../../store';
import { submitSafetyReport, resetReportSubmissionStatus } from '../../store/slices/safetySlice';
import { Button, Input, Card } from '../../components';
import { useTheme } from '../../contexts/ThemeContext';
import { createTypographyStyles } from '../../utils/styles';
import { SafetyReportScreenProps } from '../../types/navigation';

const getReportTypes = (theme: any) => [
  {
    id: 'harassment',
    title: 'Harassment',
    description: 'Unwanted contact, bullying, or threatening behavior',
    icon: 'warning',
    color: theme.colors.accent.pink,
  },
  {
    id: 'inappropriate_behavior',
    title: 'Inappropriate Behavior',
    description: 'Disrespectful, offensive, or disruptive conduct',
    icon: 'ban',
    color: theme.colors.secondary[500],
  },
  {
    id: 'fake_profile',
    title: 'Fake Profile',
    description: 'Suspicious or misleading profile information',
    icon: 'person-remove',
    color: theme.colors.accent.purple,
  },
  {
    id: 'safety_concern',
    title: 'Safety Concern',
    description: 'Behavior that makes you feel unsafe',
    icon: 'shield',
    color: theme.colors.accent.blue,
  },
  {
    id: 'other',
    title: 'Other',
    description: 'Something else that violates community guidelines',
    icon: 'ellipsis-horizontal',
    color: theme.colors.neutral[500],
  },
];

const SafetyReportScreen: React.FC<SafetyReportScreenProps> = ({ navigation, route }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { reportSubmissionStatus, error } = useSelector((state: RootState) => state.safety);
  const { user } = useSelector((state: RootState) => state.auth);
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const REPORT_TYPES = getReportTypes(theme);

  const { reportedUserId, reportedUserName, reportedUserPhoto, eventId, eventTitle } = route.params;

  const [selectedReportType, setSelectedReportType] = useState<string | null>(null);
  const [description, setDescription] = useState('');

  const handleSubmitReport = async () => {
    if (!selectedReportType || !description.trim()) {
      Alert.alert('Error', 'Please select a report type and provide a description.');
      return;
    }

    if (!user) {
      Alert.alert('Error', 'You must be logged in to submit a report.');
      return;
    }

    try {
      await dispatch(submitSafetyReport({
        reporterId: user.id,
        reportedUserId,
        reportedUserName: reportedUserName || 'Unknown User',
        reportedUserPhoto: reportedUserPhoto || '',
        eventId,
        eventTitle,
        reportType: selectedReportType as any,
        description: description.trim(),
      })).unwrap();

      Alert.alert(
        'Report Submitted',
        'Thank you for helping keep our community safe. We will review your report and take appropriate action.',
        [
          {
            text: 'OK',
            onPress: () => {
              dispatch(resetReportSubmissionStatus());
              navigation.goBack();
            }
          }
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to submit report. Please try again.');
    }
  };

  const renderReportTypeOption = (reportType: typeof REPORT_TYPES[0]) => (
    <TouchableOpacity
      key={reportType.id}
      style={[
        styles.reportTypeOption,
        selectedReportType === reportType.id && {
          borderColor: reportType.color,
          backgroundColor: `${reportType.color}10`,
        }
      ]}
      onPress={() => setSelectedReportType(reportType.id)}
    >
      <View style={styles.reportTypeHeader}>
        <View style={[styles.reportTypeIcon, { backgroundColor: `${reportType.color}20` }]}>
          <Ionicons name={reportType.icon as any} size={24} color={reportType.color} />
        </View>
        <View style={styles.reportTypeContent}>
          <Text style={styles.reportTypeTitle}>{reportType.title}</Text>
          <Text style={styles.reportTypeDescription}>{reportType.description}</Text>
        </View>
        <View style={styles.radioButton}>
          {selectedReportType === reportType.id && (
            <View style={[styles.radioButtonInner, { backgroundColor: reportType.color }]} />
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Report User</Text>
        <View style={{ width: 24 }} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Reported User Info */}
        <Card variant="default" padding="large" style={styles.userCard}>
          <View style={styles.userInfo}>
            <Image source={{ uri: reportedUserPhoto }} style={styles.userPhoto} />
            <View style={styles.userDetails}>
              <Text style={styles.userName}>{reportedUserName}</Text>
              {eventTitle && (
                <Text style={styles.eventContext}>From: {eventTitle}</Text>
              )}
            </View>
          </View>
        </Card>

        {/* Safety Message */}
        <Card variant="default" padding="large" style={styles.safetyCard}>
          <View style={styles.safetyHeader}>
            <Ionicons name="shield-checkmark" size={24} color={theme.colors.primary[500]} />
            <Text style={styles.safetyTitle}>Your Safety Matters</Text>
          </View>
          <Text style={styles.safetyMessage}>
            We take all reports seriously and will investigate promptly. Your report is confidential and helps keep our community safe.
          </Text>
        </Card>

        {/* Report Type Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>What happened?</Text>
          <Text style={styles.sectionSubtitle}>Select the type of issue you want to report</Text>
          
          <View style={styles.reportTypeOptions}>
            {REPORT_TYPES.map(renderReportTypeOption)}
          </View>
        </View>

        {/* Description */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Tell us more</Text>
          <Text style={styles.sectionSubtitle}>
            Provide details about what happened. The more information you provide, the better we can help.
          </Text>
          
          <Input
            placeholder="Describe what happened in detail..."
            value={description}
            onChangeText={setDescription}
            multiline
            numberOfLines={6}
            maxLength={1000}
            style={styles.descriptionInput}
          />
          <Text style={styles.characterCount}>
            {description.length}/1000 characters
          </Text>
        </View>

        {/* Guidelines */}
        <Card variant="flat" padding="large" style={styles.guidelinesCard}>
          <Text style={styles.guidelinesTitle}>Community Guidelines</Text>
          <Text style={styles.guidelinesText}>
            • Be respectful and kind to all community members{'\n'}
            • No harassment, bullying, or threatening behavior{'\n'}
            • Use authentic profile information{'\n'}
            • Report any behavior that makes you feel unsafe{'\n'}
            • False reports may result in account restrictions
          </Text>
        </Card>

        {/* Submit Button */}
        <View style={styles.submitContainer}>
          <Button
            title="Submit Report"
            onPress={handleSubmitReport}
            variant="primary"
            size="large"
            disabled={!selectedReportType || !description.trim() || reportSubmissionStatus === 'submitting'}
            loading={reportSubmissionStatus === 'submitting'}
            icon="send"
            iconPosition="right"
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme: any) => {
  const typography = createTypographyStyles(theme);

  return StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.secondary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: theme.spacing[5],
    paddingVertical: theme.spacing[4],
    backgroundColor: theme.colors.background.primary,
    ...theme.shadows.sm,
  },
  headerTitle: {
    ...typography.h5,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.bold,
  },
  content: {
    flex: 1,
    paddingHorizontal: theme.spacing[5],
    paddingTop: theme.spacing[4],
  },
  userCard: {
    marginBottom: theme.spacing[4],
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  userPhoto: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: theme.spacing[4],
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    ...typography.h6,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.bold,
  },
  eventContext: {
    ...typography.body2,
    color: theme.colors.text.secondary,
    marginTop: theme.spacing[1],
  },
  safetyCard: {
    marginBottom: theme.spacing[6],
    backgroundColor: theme.colors.primary[50],
    borderColor: theme.colors.primary[200],
    borderWidth: 1,
  },
  safetyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing[3],
  },
  safetyTitle: {
    ...typography.h6,
    color: theme.colors.primary[700],
    fontWeight: theme.typography.fontWeight.bold,
    marginLeft: theme.spacing[2],
  },
  safetyMessage: {
    ...typography.body2,
    color: theme.colors.primary[600],
    lineHeight: 22,
  },
  section: {
    marginBottom: theme.spacing[6],
  },
  sectionTitle: {
    ...typography.h6,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.bold,
    marginBottom: theme.spacing[2],
  },
  sectionSubtitle: {
    ...typography.body2,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing[4],
    lineHeight: 20,
  },
  reportTypeOptions: {
    gap: theme.spacing[3],
  },
  reportTypeOption: {
    borderRadius: theme.borderRadius.lg,
    borderWidth: 2,
    borderColor: theme.colors.border.light,
    backgroundColor: theme.colors.background.primary,
    padding: theme.spacing[4],
  },
  reportTypeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  reportTypeIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing[3],
  },
  reportTypeContent: {
    flex: 1,
  },
  reportTypeTitle: {
    ...typography.body1,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  reportTypeDescription: {
    ...typography.body2,
    color: theme.colors.text.secondary,
    marginTop: theme.spacing[1],
    lineHeight: 18,
  },
  radioButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: theme.colors.border.light,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  descriptionInput: {
    minHeight: 120,
    textAlignVertical: 'top',
  },
  characterCount: {
    ...typography.caption,
    color: theme.colors.text.tertiary,
    textAlign: 'right',
    marginTop: theme.spacing[2],
  },
  guidelinesCard: {
    marginBottom: theme.spacing[6],
    backgroundColor: theme.colors.background.tertiary,
  },
  guidelinesTitle: {
    ...typography.body1,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.semibold,
    marginBottom: theme.spacing[3],
  },
  guidelinesText: {
    ...typography.body2,
    color: theme.colors.text.secondary,
    lineHeight: 22,
  },
  submitContainer: {
    paddingBottom: theme.spacing[6],
  },
  });
};

export default SafetyReportScreen;

{"name": "grassroots-backend", "version": "1.0.0", "description": "Backend API for Grassroots social app", "main": "dist/app.js", "scripts": {"dev": "nodemon src/app.ts", "build": "tsc", "start": "node dist/app.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:watch": "jest --watch --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:coverage": "jest --coverage --detect<PERSON><PERSON><PERSON><PERSON><PERSON>", "test:integration": "jest --testPathPattern=integration --detectO<PERSON>Handles", "test:unit": "jest --testPathPattern=services --detectOpenHandles", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "joi": "^17.9.0", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "socket.io": "^4.7.4", "google-auth-library": "^9.4.1", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "@prisma/client": "^5.7.1", "redis": "^4.6.11", "ioredis": "^5.3.0", "compression": "^1.7.4", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^4.6.2"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/joi": "^17.2.3", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "@types/jest": "^29.5.0", "@types/supertest": "^2.0.12", "jest": "^29.5.0", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "socket.io-client": "^4.6.1", "prisma": "^5.7.1"}}
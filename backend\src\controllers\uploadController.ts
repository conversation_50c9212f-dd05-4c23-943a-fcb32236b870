import { Request, Response } from 'express';
import { fileService } from '../services/fileService';
import { logger } from '../utils/logger';

export const uploadEventImage = async (req: Request, res: Response) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const processedPath = await fileService.processEventImage(req.file.path);
    const imageUrl = `/uploads/${path.basename(processedPath)}`;

    res.json({ 
      message: 'Image uploaded successfully',
      imageUrl 
    });
  } catch (error: any) {
    logger.error('Upload event image error:', error);
    res.status(500).json({ error: 'Failed to upload image' });
  }
};

export const uploadProfileImage = async (req: Request, res: Response) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const processedPath = await fileService.processProfileImage(req.file.path);
    const imageUrl = `/uploads/${path.basename(processedPath)}`;

    res.json({ 
      message: 'Profile image uploaded successfully',
      imageUrl 
    });
  } catch (error: any) {
    logger.error('Upload profile image error:', error);
    res.status(500).json({ error: 'Failed to upload profile image' });
  }
};
import Joi from 'joi';

export const eventValidation = {
  create: Joi.object({
    title: Joi.string().min(3).max(100).trim().required(),
    description: Joi.string().min(10).max(1000).trim().required(),
    location: Joi.string().min(3).max(200).trim().required(),
    startTime: Joi.date().greater('now').required(),
    endTime: Joi.date().greater(Joi.ref('startTime')).required(),
    capacity: Joi.number().integer().min(2).max(1000).required(),
    latitude: Joi.number().min(-90).max(90).optional(),
    longitude: Joi.number().min(-180).max(180).optional(),
    tags: Joi.array().items(Joi.string().max(30)).max(5).optional()
  }),

  update: Joi.object({
    title: Joi.string().min(3).max(100).trim().optional(),
    description: Joi.string().min(10).max(1000).trim().optional(),
    location: Joi.string().min(3).max(200).trim().optional(),
    startTime: Joi.date().greater('now').optional(),
    endTime: Joi.date().optional(),
    capacity: Joi.number().integer().min(2).max(1000).optional()
  }).min(1), // At least one field must be provided

  query: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10),
    search: Joi.string().max(100).optional(),
    location: Joi.string().max(100).optional(),
    startDate: Joi.date().optional(),
    endDate: Joi.date().greater(Joi.ref('startDate')).optional()
  })
};

export const userValidation = {
  update: Joi.object({
    firstName: Joi.string().min(2).max(50).trim().pattern(/^[a-zA-Z\s]+$/).optional(),
    lastName: Joi.string().min(2).max(50).trim().pattern(/^[a-zA-Z\s]+$/).optional(),
    bio: Joi.string().max(500).trim().optional(),
    interests: Joi.array().items(Joi.string().max(50).trim()).max(10).unique().optional(),
    university: Joi.string().min(2).max(100).trim().optional(),
    graduationYear: Joi.number().integer().min(new Date().getFullYear()).max(new Date().getFullYear() + 10).optional(),
    major: Joi.string().min(2).max(100).trim().optional()
  }).min(1)
};

export const connectionValidation = {
  create: Joi.object({
    eventId: Joi.string().uuid().required(),
    toUserId: Joi.string().uuid().required(),
    connectionType: Joi.string().valid('FRIEND', 'SPARK', 'PASS').required(),
    feedback: Joi.string().max(500).trim().optional(),
    isAnonymous: Joi.boolean().default(false)
  })
};

export const reportValidation = {
  create: Joi.object({
    reportedUserId: Joi.string().uuid().required(),
    eventId: Joi.string().uuid().optional(),
    messageId: Joi.string().uuid().optional(),
    groupId: Joi.string().uuid().optional(),
    reason: Joi.string().valid(
      'HARASSMENT', 
      'INAPPROPRIATE_CONTENT', 
      'SPAM', 
      'FAKE_PROFILE', 
      'SAFETY_CONCERN', 
      'OTHER'
    ).required(),
    description: Joi.string().min(10).max(1000).trim().required()
  })
};

export const messageValidation = {
  send: Joi.object({
    content: Joi.string().min(1).max(1000).trim().required(),
    messageType: Joi.string().valid('TEXT', 'IMAGE', 'VOICE').default('TEXT')
  })
};

export const adminValidation = {
  userStatusUpdate: Joi.object({
    isActive: Joi.boolean().required(),
    reason: Joi.string().max(500).trim().optional()
  }),

  reportReview: Joi.object({
    resolution: Joi.string().valid('RESOLVED', 'DISMISSED', 'ESCALATED').required(),
    actionType: Joi.string().valid('WARNING', 'TEMPORARY_BAN', 'PERMANENT_BAN', 'CONTENT_REMOVAL').optional(),
    duration: Joi.number().integer().min(1).max(8760).optional() // Max 1 year in hours
  })
};

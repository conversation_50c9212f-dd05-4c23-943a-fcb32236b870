import { prisma } from '../config/database';
import { logger } from '../utils/logger';

export const adminService = {
  async getDashboardStats() {
    const [
      totalUsers,
      activeUsers,
      totalEvents,
      upcomingEvents,
      totalGroups,
      totalMessages,
      pendingReports,
      totalConnections
    ] = await Promise.all([
      prisma.user.count(),
      prisma.user.count({ where: { isActive: true } }),
      prisma.event.count(),
      prisma.event.count({ where: { startTime: { gte: new Date() } } }),
      prisma.group.count(),
      prisma.message.count(),
      prisma.report.count({ where: { status: 'PENDING' } }),
      prisma.connection.count({ where: { status: 'MUTUAL' } })
    ]);

    return {
      users: { total: totalUsers, active: activeUsers },
      events: { total: totalEvents, upcoming: upcomingEvents },
      groups: { total: totalGroups },
      messages: { total: totalMessages },
      reports: { pending: pendingReports },
      connections: { total: totalConnections }
    };
  },

  async getUsers(page: number = 1, limit: number = 20, search?: string, status?: string) {
    const skip = (page - 1) * limit;
    const where: any = {};

    if (search) {
      where.OR = [
        { firstName: { contains: search, mode: 'insensitive' } },
        { lastName: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (status === 'active') where.isActive = true;
    if (status === 'inactive') where.isActive = false;

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          university: true,
          isActive: true,
          isVerified: true,
          createdAt: true,
          _count: {
            select: {
              createdEvents: true,
              eventParticipants: true,
              groupMemberships: true,
              messages: true
            }
          }
        }
      }),
      prisma.user.count({ where })
    ]);

    return {
      users,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    };
  },

  async getUserDetails(userId: string) {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        createdEvents: {
          select: { id: true, title: true, startTime: true, currentParticipants: true }
        },
        eventParticipants: {
          include: {
            event: {
              select: { id: true, title: true, startTime: true }
            }
          }
        },
        groupMemberships: {
          include: {
            group: {
              select: { id: true, name: true, eventId: true }
            }
          }
        },
        sentReports: {
          select: { id: true, reason: true, status: true, createdAt: true }
        },
        receivedReports: {
          select: { id: true, reason: true, status: true, createdAt: true }
        }
      }
    });

    if (!user) {
      throw new Error('User not found');
    }

    return user;
  },

  async updateUserStatus(userId: string, isActive: boolean, reason?: string) {
    const user = await prisma.user.update({
      where: { id: userId },
      data: { isActive }
    });

    if (!isActive && reason) {
      await prisma.moderationAction.create({
        data: {
          userId,
          actionType: 'TEMPORARY_BAN',
          reason,
          createdBy: 'SYSTEM'
        }
      });
    }

    logger.info(`User ${userId} status updated to ${isActive ? 'active' : 'inactive'}`);
    return user;
  },

  async getEventAnalytics(eventId?: string) {
    if (eventId) {
      // Single event analytics
      const event = await prisma.event.findUnique({
        where: { id: eventId },
        include: {
          participants: true,
          groups: {
            include: {
              messages: true,
              members: true
            }
          },
          _count: {
            select: {
              participants: true,
              groups: true
            }
          }
        }
      });

      if (!event) {
        throw new Error('Event not found');
      }

      const totalMessages = event.groups.reduce((sum, group) => sum + group.messages.length, 0);
      const avgGroupSize = event.groups.length > 0 
        ? event.groups.reduce((sum, group) => sum + group.members.length, 0) / event.groups.length 
        : 0;

      return {
        event,
        analytics: {
          participationRate: (event._count.participants / event.capacity) * 100,
          totalMessages,
          averageGroupSize: Math.round(avgGroupSize * 100) / 100,
          groupsFormed: event._count.groups
        }
      };
    } else {
      // Overall analytics
      const events = await prisma.event.findMany({
        include: {
          _count: {
            select: { participants: true, groups: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 10
      });

      return { events };
    }
  },

  async getSystemHealth() {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    const [
      recentUsers,
      recentEvents,
      recentMessages,
      recentReports,
      errorLogs
    ] = await Promise.all([
      prisma.user.count({ where: { createdAt: { gte: oneDayAgo } } }),
      prisma.event.count({ where: { createdAt: { gte: oneDayAgo } } }),
      prisma.message.count({ where: { createdAt: { gte: oneHourAgo } } }),
      prisma.report.count({ where: { createdAt: { gte: oneDayAgo } } }),
      // This would typically come from a logging service
      0
    ]);

    return {
      activity: {
        newUsers24h: recentUsers,
        newEvents24h: recentEvents,
        messages1h: recentMessages,
        reports24h: recentReports
      },
      system: {
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        errorCount: errorLogs
      }
    };
  }
};
import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useDispatch, useSelector } from 'react-redux';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { AppDispatch, RootState } from '../../store';
import {
  fetchPostEventSummary,
  submitConnectionReview,
  EventParticipant,
  ConnectionReview,
} from '../../store/slices/connectionsSlice';
import { Button, Card, Input } from '../../components';
import { useTheme } from '../../contexts/ThemeContext';
import { createTypographyStyles } from '../../utils/styles';
import { PostEventReviewScreenProps } from '../../types/navigation';

const { width } = Dimensions.get('window');

const PostEventReviewScreen: React.FC<PostEventReviewScreenProps> = ({ navigation, route }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const { postEventSummaries, isLoading } = useSelector((state: RootState) => state.connections);
  const { user } = useSelector((state: RootState) => state.auth);
  
  const { eventId } = route.params;
  const eventSummary = postEventSummaries.find(s => s.eventId === eventId);
  
  const [currentParticipantIndex, setCurrentParticipantIndex] = useState(0);
  const [selectedRating, setSelectedRating] = useState<'friend' | 'spark' | 'pass' | null>(null);
  const [feedback, setFeedback] = useState('');
  const [isAnonymous, setIsAnonymous] = useState(true);

  useEffect(() => {
    if (!eventSummary) {
      dispatch(fetchPostEventSummary(eventId));
    }
  }, [dispatch, eventId, eventSummary]);

  const currentParticipant = eventSummary?.participants[currentParticipantIndex];
  const isLastParticipant = currentParticipantIndex === (eventSummary?.participants.length || 0) - 1;

  const handleRatingSelect = (rating: 'friend' | 'spark' | 'pass') => {
    setSelectedRating(rating);
  };

  const handleSubmitReview = async () => {
    if (!selectedRating || !currentParticipant || !user) {
      Alert.alert('Error', 'Please select a rating before continuing.');
      return;
    }

    const review: Omit<ConnectionReview, 'id' | 'createdAt'> = {
      eventId,
      reviewerId: user.id,
      revieweeId: currentParticipant.id,
      rating: selectedRating,
      isAnonymous,
      feedback: feedback.trim() || undefined,
    };

    try {
      await dispatch(submitConnectionReview(review)).unwrap();
      
      // Move to next participant or complete
      if (isLastParticipant) {
        Alert.alert(
          'Reviews Complete! 🎉',
          'Thank you for your feedback. We\'ll notify you of any mutual connections!',
          [{ text: 'OK', onPress: () => navigation.goBack() }]
        );
      } else {
        setCurrentParticipantIndex(prev => prev + 1);
        setSelectedRating(null);
        setFeedback('');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to submit review. Please try again.');
    }
  };

  const handleSkip = () => {
    if (isLastParticipant) {
      navigation.goBack();
    } else {
      setCurrentParticipantIndex(prev => prev + 1);
      setSelectedRating(null);
      setFeedback('');
    }
  };

  const getRatingColor = (rating: 'friend' | 'spark' | 'pass') => {
    switch (rating) {
      case 'friend': return theme.colors.accent.blue;
      case 'spark': return theme.colors.accent.pink;
      case 'pass': return theme.colors.neutral[400];
    }
  };

  const getRatingIcon = (rating: 'friend' | 'spark' | 'pass') => {
    switch (rating) {
      case 'friend': return 'people';
      case 'spark': return 'heart';
      case 'pass': return 'close';
    }
  };

  if (isLoading || !eventSummary || !currentParticipant) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading event summary...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text.primary} />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Post-Event Review</Text>
          <Text style={styles.headerSubtitle}>
            {currentParticipantIndex + 1} of {eventSummary.participants.length}
          </Text>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Event Info */}
        <Card variant="default" padding="large" style={styles.eventCard}>
          <Text style={styles.eventTitle}>{eventSummary.eventTitle}</Text>
          <Text style={styles.eventDate}>
            {new Date(eventSummary.eventDate).toLocaleDateString()}
          </Text>
        </Card>

        {/* Participant Card */}
        <Card variant="elevated" padding="large" style={styles.participantCard}>
          <View style={styles.participantHeader}>
            <Image
              source={{ uri: currentParticipant.profilePhoto }}
              style={styles.participantPhoto}
            />
            <View style={styles.participantInfo}>
              <Text style={styles.participantName}>
                {currentParticipant.firstName} {currentParticipant.lastName}
              </Text>
              <Text style={styles.participantUniversity}>{currentParticipant.university}</Text>
              <Text style={styles.mutualConnections}>
                {currentParticipant.mutualConnections} mutual connections
              </Text>
            </View>
          </View>

          <Text style={styles.participantBio}>{currentParticipant.bio}</Text>

          {/* Interests */}
          <View style={styles.interestsContainer}>
            <Text style={styles.interestsTitle}>Interests</Text>
            <View style={styles.interestsGrid}>
              {currentParticipant.interests.slice(0, 6).map((interest, index) => (
                <View key={index} style={styles.interestChip}>
                  <Text style={styles.interestText}>{interest}</Text>
                </View>
              ))}
            </View>
          </View>

          {/* Event Interactions */}
          <View style={styles.interactionsContainer}>
            <Text style={styles.interactionsTitle}>Your Interactions</Text>
            <View style={styles.interactionsGrid}>
              <View style={styles.interactionItem}>
                <Ionicons name="chatbubble" size={20} color={theme.colors.primary[500]} />
                <Text style={styles.interactionText}>
                  {currentParticipant.eventInteractions.chatMessages} messages
                </Text>
              </View>
              <View style={styles.interactionItem}>
                <Ionicons name="time" size={20} color={theme.colors.accent.blue} />
                <Text style={styles.interactionText}>
                  {currentParticipant.eventInteractions.timeSpentTogether}min together
                </Text>
              </View>
            </View>
          </View>
        </Card>

        {/* Rating Selection */}
        <Card variant="default" padding="large" style={styles.ratingCard}>
          <Text style={styles.ratingTitle}>How would you rate this connection?</Text>
          <Text style={styles.ratingSubtitle}>Your response is anonymous by default</Text>

          <View style={styles.ratingOptions}>
            {(['friend', 'spark', 'pass'] as const).map((rating) => (
              <TouchableOpacity
                key={rating}
                style={[
                  styles.ratingOption,
                  selectedRating === rating && {
                    backgroundColor: getRatingColor(rating),
                    borderColor: getRatingColor(rating),
                  }
                ]}
                onPress={() => handleRatingSelect(rating)}
              >
                <Ionicons
                  name={getRatingIcon(rating)}
                  size={32}
                  color={selectedRating === rating ? theme.colors.text.inverse : getRatingColor(rating)}
                />
                <Text
                  style={[
                    styles.ratingOptionText,
                    selectedRating === rating && styles.ratingOptionTextSelected
                  ]}
                >
                  {rating.charAt(0).toUpperCase() + rating.slice(1)}
                </Text>
                <Text
                  style={[
                    styles.ratingOptionDescription,
                    selectedRating === rating && styles.ratingOptionDescriptionSelected
                  ]}
                >
                  {rating === 'friend' && 'Great to hang out with'}
                  {rating === 'spark' && 'Romantic potential'}
                  {rating === 'pass' && 'Not a match'}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* Optional Feedback */}
          {selectedRating && selectedRating !== 'pass' && (
            <View style={styles.feedbackContainer}>
              <Input
                placeholder="Optional: Add a note (visible only if you both match)"
                value={feedback}
                onChangeText={setFeedback}
                multiline
                numberOfLines={3}
                maxLength={200}
              />
            </View>
          )}

          {/* Anonymous Toggle */}
          <TouchableOpacity
            style={styles.anonymousToggle}
            onPress={() => setIsAnonymous(!isAnonymous)}
          >
            <Ionicons
              name={isAnonymous ? 'checkbox' : 'square-outline'}
              size={24}
              color={theme.colors.primary[500]}
            />
            <Text style={styles.anonymousText}>Keep my review anonymous</Text>
          </TouchableOpacity>
        </Card>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <Button
            title="Skip"
            onPress={handleSkip}
            variant="ghost"
            size="large"
            style={styles.skipButton}
          />
          <Button
            title={isLastParticipant ? "Complete Reviews" : "Next Person"}
            onPress={handleSubmitReview}
            variant="primary"
            size="large"
            style={styles.submitButton}
            disabled={!selectedRating}
            icon={isLastParticipant ? "checkmark" : "arrow-forward"}
            iconPosition="right"
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme: any) => {
  const typography = createTypographyStyles(theme);

  return StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.secondary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing[5],
    paddingVertical: theme.spacing[4],
    backgroundColor: theme.colors.background.primary,
    ...theme.shadows.sm,
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
    marginLeft: theme.spacing[4],
  },
  headerTitle: {
    ...typography.h5,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.bold,
  },
  headerSubtitle: {
    ...typography.caption,
    color: theme.colors.text.secondary,
    marginTop: theme.spacing[1],
  },
  content: {
    flex: 1,
    paddingHorizontal: theme.spacing[5],
    paddingTop: theme.spacing[4],
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    ...typography.body1,
    color: theme.colors.text.secondary,
  },
  eventCard: {
    marginBottom: theme.spacing[4],
    alignItems: 'center',
  },
  eventTitle: {
    ...typography.h5,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.bold,
  },
  eventDate: {
    ...typography.body2,
    color: theme.colors.text.secondary,
    marginTop: theme.spacing[1],
  },
  participantCard: {
    marginBottom: theme.spacing[4],
  },
  participantHeader: {
    flexDirection: 'row',
    marginBottom: theme.spacing[4],
  },
  participantPhoto: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginRight: theme.spacing[4],
  },
  participantInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  participantName: {
    ...typography.h6,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.bold,
  },
  participantUniversity: {
    ...typography.body2,
    color: theme.colors.text.secondary,
    marginTop: theme.spacing[1],
  },
  mutualConnections: {
    ...typography.caption,
    color: theme.colors.primary[500],
    marginTop: theme.spacing[1],
  },
  participantBio: {
    ...typography.body1,
    color: theme.colors.text.primary,
    lineHeight: 22,
    marginBottom: theme.spacing[4],
  },
  interestsContainer: {
    marginBottom: theme.spacing[4],
  },
  interestsTitle: {
    ...typography.body1,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.semibold,
    marginBottom: theme.spacing[2],
  },
  interestsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  interestChip: {
    backgroundColor: theme.colors.background.secondary,
    borderRadius: theme.borderRadius.full,
    paddingVertical: theme.spacing[1],
    paddingHorizontal: theme.spacing[3],
    marginRight: theme.spacing[2],
    marginBottom: theme.spacing[2],
  },
  interestText: {
    ...typography.caption,
    color: theme.colors.text.primary,
  },
  interactionsContainer: {
    marginBottom: theme.spacing[2],
  },
  interactionsTitle: {
    ...typography.body1,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.semibold,
    marginBottom: theme.spacing[2],
  },
  interactionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  interactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  interactionText: {
    ...typography.caption,
    color: theme.colors.text.secondary,
    marginLeft: theme.spacing[1],
  },
  ratingCard: {
    marginBottom: theme.spacing[4],
  },
  ratingTitle: {
    ...typography.h6,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.bold,
    textAlign: 'center',
    marginBottom: theme.spacing[2],
  },
  ratingSubtitle: {
    ...typography.body2,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    marginBottom: theme.spacing[6],
  },
  ratingOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: theme.spacing[4],
  },
  ratingOption: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: theme.spacing[4],
    paddingHorizontal: theme.spacing[2],
    marginHorizontal: theme.spacing[1],
    borderRadius: theme.borderRadius.lg,
    borderWidth: 2,
    borderColor: theme.colors.border.light,
    backgroundColor: theme.colors.background.primary,
  },
  ratingOptionText: {
    ...typography.body2,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.semibold,
    marginTop: theme.spacing[2],
  },
  ratingOptionTextSelected: {
    color: theme.colors.text.inverse,
  },
  ratingOptionDescription: {
    ...typography.caption,
    color: theme.colors.text.tertiary,
    textAlign: 'center',
    marginTop: theme.spacing[1],
  },
  ratingOptionDescriptionSelected: {
    color: theme.colors.text.inverse,
  },
  feedbackContainer: {
    marginBottom: theme.spacing[4],
  },
  anonymousToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  anonymousText: {
    ...typography.body2,
    color: theme.colors.text.primary,
    marginLeft: theme.spacing[2],
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: theme.spacing[6],
  },
  skipButton: {
    flex: 0.3,
  },
  submitButton: {
    flex: 0.65,
  },
  });
};

export default PostEventReviewScreen;

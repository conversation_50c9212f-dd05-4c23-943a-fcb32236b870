import { Request, Response } from 'express';
import { moderationService } from '../services/moderationService';
import { AuthRequest } from '../middleware/auth';
import { logger } from '../utils/logger';

export const createReport = async (req: AuthRequest, res: Response) => {
  try {
    const { reportedUserId, eventId, messageId, groupId, reason, description } = req.body;
    const reporterId = req.user!.id;

    if (!reportedUserId || !reason || !description) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    const report = await moderationService.createReport(reporterId, {
      reportedUserId,
      eventId,
      messageId,
      groupId,
      reason,
      description
    });

    res.status(201).json({ report, message: 'Report submitted successfully' });
  } catch (error: any) {
    logger.error('Create report error:', error);
    
    if (error.message === 'Cannot report yourself' || 
        error.message === 'Report already exists for this user/content') {
      return res.status(400).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to submit report' });
  }
};

export const getReports = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const status = req.query.status as string;
    const priority = req.query.priority as string;

    const result = await moderationService.getReports(page, limit, status, priority);
    res.json(result);
  } catch (error: any) {
    logger.error('Get reports error:', error);
    res.status(500).json({ error: 'Failed to fetch reports' });
  }
};

export const reviewReport = async (req: AuthRequest, res: Response) => {
  try {
    const { reportId } = req.params;
    const { resolution, actionType, duration } = req.body;
    const reviewerId = req.user!.id;

    if (!resolution) {
      return res.status(400).json({ error: 'Resolution is required' });
    }

    await moderationService.reviewReport(reportId, reviewerId, resolution, actionType);
    res.json({ message: 'Report reviewed successfully' });
  } catch (error: any) {
    logger.error('Review report error:', error);
    
    if (error.message === 'Report not found') {
      return res.status(404).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to review report' });
  }
};

export const getUserModerationHistory = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const history = await moderationService.getUserModerationHistory(userId);
    res.json({ history });
  } catch (error: any) {
    logger.error('Get moderation history error:', error);
    res.status(500).json({ error: 'Failed to fetch moderation history' });
  }
};

export const getContentFilters = async (req: Request, res: Response) => {
  try {
    const filters = await moderationService.getContentFilters();
    res.json(filters);
  } catch (error: any) {
    logger.error('Get content filters error:', error);
    res.status(500).json({ error: 'Failed to fetch content filters' });
  }
};
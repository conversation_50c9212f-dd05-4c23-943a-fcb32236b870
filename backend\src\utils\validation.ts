import { CreateEventData } from '../models/Event';

export const validateEventData = (data: any) => {
  const errors: string[] = [];

  if (!data.title || data.title.trim().length < 3) {
    errors.push('Title must be at least 3 characters long');
  }

  if (!data.description || data.description.trim().length < 10) {
    errors.push('Description must be at least 10 characters long');
  }

  if (!data.location || data.location.trim().length < 3) {
    errors.push('Location must be at least 3 characters long');
  }

  if (!data.startTime || !isValidDate(data.startTime)) {
    errors.push('Valid start time is required');
  }

  if (!data.endTime || !isValidDate(data.endTime)) {
    errors.push('Valid end time is required');
  }

  if (data.startTime && data.endTime && new Date(data.startTime) >= new Date(data.endTime)) {
    errors.push('End time must be after start time');
  }

  if (!data.capacity || data.capacity < 2 || data.capacity > 1000) {
    errors.push('Capacity must be between 2 and 1000');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

const isValidDate = (dateString: string): boolean => {
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date.getTime());
};

export const validateUserData = (data: any) => {
  const errors: string[] = [];

  if (!data.email || !isValidEmail(data.email)) {
    errors.push('Valid email is required');
  }

  if (!data.firstName || data.firstName.trim().length < 2) {
    errors.push('First name must be at least 2 characters long');
  }

  if (!data.lastName || data.lastName.trim().length < 2) {
    errors.push('Last name must be at least 2 characters long');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};
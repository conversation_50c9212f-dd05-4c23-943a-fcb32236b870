import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import { uploadEventImage as uploadEventImageMiddleware, uploadProfileImage as uploadProfileImageMiddleware } from '../middleware/upload';
import { uploadEventImage, uploadProfileImage } from '../controllers/uploadController';

const router = Router();

router.post('/event-image', authenticateToken, uploadEventImageMiddleware, uploadEventImage);
router.post('/profile-image', authenticateToken, uploadProfileImageMiddleware, uploadProfileImage);

export default router;
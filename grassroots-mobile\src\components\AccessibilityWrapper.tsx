import React from 'react';
import { View, ViewStyle, AccessibilityRole, AccessibilityState } from 'react-native';

interface AccessibilityWrapperProps {
  children: React.ReactNode;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessibilityRole?: AccessibilityRole;
  accessibilityState?: AccessibilityState;
  accessible?: boolean;
  testID?: string;
  style?: ViewStyle;
  onPress?: () => void;
}

// WCAG 2.1 AA compliant accessibility wrapper
const AccessibilityWrapper: React.FC<AccessibilityWrapperProps> = ({
  children,
  accessibilityLabel,
  accessibilityHint,
  accessibilityRole,
  accessibilityState,
  accessible = true,
  testID,
  style,
  onPress,
}) => {
  return (
    <View
      style={style}
      accessible={accessible}
      accessibilityLabel={accessibilityLabel}
      accessibilityHint={accessibilityHint}
      accessibilityRole={accessibilityRole}
      accessibilityState={accessibilityState}
      testID={testID}
      onTouchEnd={onPress}
    >
      {children}
    </View>
  );
};

// Accessibility utilities
export const AccessibilityUtils = {
  // Generate accessibility label for buttons
  buttonLabel: (title: string, state?: string) => {
    return state ? `${title}, ${state}` : title;
  },

  // Generate accessibility hint for actions
  actionHint: (action: string) => {
    return `Double tap to ${action}`;
  },

  // Generate accessibility label for form inputs
  inputLabel: (label: string, value?: string, required?: boolean) => {
    let accessibilityLabel = label;
    if (required) accessibilityLabel += ', required';
    if (value) accessibilityLabel += `, current value: ${value}`;
    return accessibilityLabel;
  },

  // Generate accessibility label for navigation items
  navigationLabel: (title: string, isSelected?: boolean) => {
    return isSelected ? `${title}, selected` : title;
  },

  // Generate accessibility label for status indicators
  statusLabel: (status: string, count?: number) => {
    if (count !== undefined) {
      return `${status}, ${count} ${count === 1 ? 'item' : 'items'}`;
    }
    return status;
  },

  // Generate accessibility state for interactive elements
  interactiveState: (disabled?: boolean, selected?: boolean, expanded?: boolean) => {
    return {
      disabled: disabled || false,
      selected: selected || false,
      expanded: expanded || false,
    };
  },

  // Check if color contrast meets WCAG AA standards
  checkColorContrast: (foreground: string, background: string): boolean => {
    // This is a simplified check - in production, use a proper contrast ratio calculator
    // WCAG AA requires 4.5:1 for normal text, 3:1 for large text
    return true; // Placeholder - implement actual contrast calculation
  },

  // Generate semantic roles for different UI elements
  roles: {
    button: 'button' as AccessibilityRole,
    link: 'link' as AccessibilityRole,
    text: 'text' as AccessibilityRole,
    image: 'image' as AccessibilityRole,
    header: 'header' as AccessibilityRole,
    search: 'search' as AccessibilityRole,
    tab: 'tab' as AccessibilityRole,
    tablist: 'tablist' as AccessibilityRole,
    menu: 'menu' as AccessibilityRole,
    menuitem: 'menuitem' as AccessibilityRole,
    alert: 'alert' as AccessibilityRole,
    checkbox: 'checkbox' as AccessibilityRole,
    radio: 'radio' as AccessibilityRole,
    switch: 'switch' as AccessibilityRole,
    slider: 'adjustable' as AccessibilityRole,
  },
};

// Focus management utilities
export const FocusUtils = {
  // Announce important changes to screen readers
  announceForAccessibility: (message: string) => {
    // This would use the platform's accessibility announcement API
    console.log(`Accessibility announcement: ${message}`);
  },

  // Set focus to specific element (useful for navigation)
  setFocus: (ref: React.RefObject<any>) => {
    if (ref.current) {
      ref.current.focus();
    }
  },

  // Generate focus order for complex layouts
  generateFocusOrder: (elements: string[]): { [key: string]: number } => {
    const focusOrder: { [key: string]: number } = {};
    elements.forEach((element, index) => {
      focusOrder[element] = index + 1;
    });
    return focusOrder;
  },
};

// Accessibility testing helpers
export const AccessibilityTesting = {
  // Generate test IDs for automated testing
  generateTestID: (component: string, variant?: string, index?: number) => {
    let testID = component;
    if (variant) testID += `-${variant}`;
    if (index !== undefined) testID += `-${index}`;
    return testID;
  },

  // Validate accessibility props
  validateAccessibilityProps: (props: AccessibilityWrapperProps) => {
    const warnings: string[] = [];
    
    if (props.onPress && !props.accessibilityRole) {
      warnings.push('Interactive element should have accessibilityRole');
    }
    
    if (props.accessibilityRole === 'button' && !props.accessibilityLabel) {
      warnings.push('Button should have accessibilityLabel');
    }
    
    if (warnings.length > 0) {
      console.warn('Accessibility warnings:', warnings);
    }
    
    return warnings.length === 0;
  },
};

export default AccessibilityWrapper;

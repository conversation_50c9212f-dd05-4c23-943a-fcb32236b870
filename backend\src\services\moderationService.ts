import { prisma } from '../config/database';
import { CreateReportData } from '../models/Report';
import { logger } from '../utils/logger';

export const moderationService = {
  async createReport(reporterId: string, data: CreateReportData) {
    // Prevent self-reporting
    if (reporterId === data.reportedUserId) {
      throw new Error('Cannot report yourself');
    }

    // Check for duplicate reports
    const existingReport = await prisma.report.findFirst({
      where: {
        reporterId,
        reportedUserId: data.reportedUserId,
        eventId: data.eventId,
        messageId: data.messageId,
        status: { in: ['PENDING', 'REVIEWED'] }
      }
    });

    if (existingReport) {
      throw new Error('Report already exists for this user/content');
    }

    const report = await prisma.report.create({
      data: {
        reporterId,
        reportedUserId: data.reportedUserId,
        eventId: data.eventId,
        messageId: data.messageId,
        groupId: data.groupId,
        reason: data.reason as any,
        description: data.description,
        status: 'PENDING',
        priority: this.calculatePriority(data.reason)
      }
    });

    // Auto-escalate high-priority reports
    if (report.priority === 'HIGH' || report.priority === 'URGENT') {
      await this.notifyModerators(report.id);
    }

    logger.info(`Report created: ${report.id} against user ${data.reportedUserId}`);
    return report;
  },

  calculatePriority(reason: string): 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT' {
    const priorityMap: Record<string, 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'> = {
      'HARASSMENT': 'HIGH',
      'SAFETY_CONCERN': 'URGENT',
      'INAPPROPRIATE_CONTENT': 'MEDIUM',
      'SPAM': 'LOW',
      'FAKE_PROFILE': 'MEDIUM',
      'OTHER': 'LOW'
    };
    return priorityMap[reason] || 'LOW';
  },

  async getReports(page: number = 1, limit: number = 20, status?: string, priority?: string) {
    const skip = (page - 1) * limit;
    const where: any = {};

    if (status) where.status = status;
    if (priority) where.priority = priority;

    const [reports, total] = await Promise.all([
      prisma.report.findMany({
        where,
        skip,
        take: limit,
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' }
        ],
        include: {
          reporter: {
            select: { id: true, firstName: true, lastName: true, email: true }
          },
          reportedUser: {
            select: { id: true, firstName: true, lastName: true, email: true }
          },
          event: {
            select: { id: true, title: true, startTime: true }
          }
        }
      }),
      prisma.report.count({ where })
    ]);

    return {
      reports,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    };
  },

  async reviewReport(reportId: string, reviewerId: string, resolution: string, actionType?: string) {
    const report = await prisma.report.findUnique({
      where: { id: reportId }
    });

    if (!report) {
      throw new Error('Report not found');
    }

    // Update report status
    await prisma.report.update({
      where: { id: reportId },
      data: {
        status: 'REVIEWED',
        reviewedAt: new Date(),
        reviewedBy: reviewerId,
        resolution
      }
    });

    // Take moderation action if specified
    if (actionType && actionType !== 'NO_ACTION') {
      await this.createModerationAction(report.reportedUserId, actionType, resolution, reviewerId);
    }

    logger.info(`Report ${reportId} reviewed by ${reviewerId}`);
  },

  async createModerationAction(userId: string, actionType: string, reason: string, createdBy: string, duration?: number) {
    const expiresAt = duration ? new Date(Date.now() + duration * 60 * 60 * 1000) : undefined;

    const action = await prisma.moderationAction.create({
      data: {
        userId,
        actionType: actionType as any,
        reason,
        duration,
        createdBy,
        expiresAt
      }
    });

    // Apply the action
    if (actionType === 'TEMPORARY_BAN' || actionType === 'PERMANENT_BAN') {
      await prisma.user.update({
        where: { id: userId },
        data: { isActive: false }
      });
    }

    logger.info(`Moderation action ${actionType} applied to user ${userId}`);
    return action;
  },

  async getUserModerationHistory(userId: string) {
    return await prisma.moderationAction.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      include: {
        createdByUser: {
          select: { id: true, firstName: true, lastName: true }
        }
      }
    });
  },

  async notifyModerators(reportId: string) {
    // Implementation for notifying moderators (email, Slack, etc.)
    logger.info(`High-priority report ${reportId} requires immediate attention`);
  },

  async getContentFilters() {
    // Return list of banned words/phrases for client-side filtering
    return {
      bannedWords: ['spam', 'scam', 'fake'],
      suspiciousPatterns: [/\b\d{3}-\d{3}-\d{4}\b/] // Phone numbers
    };
  }
};
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface Notification {
  id: string;
  type: 'event' | 'chat' | 'connection' | 'safety' | 'system';
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  actionData?: any;
}

interface NotificationsState {
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  error: string | null;
}

const initialState: NotificationsState = {
  notifications: [],
  unreadCount: 0,
  isLoading: false,
  error: null,
};

// Mock notifications data
const mockNotifications: Notification[] = [
  {
    id: '1',
    type: 'event',
    title: 'New Event Available!',
    message: 'Coffee & Connections is happening tomorrow at 9 AM',
    timestamp: '2025-07-20T10:30:00Z',
    isRead: false,
    actionData: { eventId: '1' }
  },
  {
    id: '2',
    type: 'chat',
    title: 'New Message',
    message: '<PERSON> sent a message in Coffee & Connections group',
    timestamp: '2025-07-20T09:15:00Z',
    isRead: false,
    actionData: { chatId: 'chat1' }
  },
  {
    id: '3',
    type: 'connection',
    title: 'New Connection!',
    message: '<PERSON> wants to connect with you after the Study Session event',
    timestamp: '2025-07-19T18:45:00Z',
    isRead: true,
    actionData: { userId: 'user1' }
  },
  {
    id: '4',
    type: 'safety',
    title: 'Safety Alert',
    message: 'Please review our updated community guidelines',
    timestamp: '2025-07-19T14:20:00Z',
    isRead: true,
    actionData: { type: 'guidelines' }
  },
  {
    id: '5',
    type: 'system',
    title: 'Profile Update',
    message: 'Your profile has been successfully updated',
    timestamp: '2025-07-19T12:00:00Z',
    isRead: true,
  }
];

// Async thunks
export const fetchNotifications = createAsyncThunk(
  'notifications/fetchNotifications',
  async () => {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    return mockNotifications;
  }
);

export const markNotificationAsRead = createAsyncThunk(
  'notifications/markAsRead',
  async (notificationId: string) => {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 300));
    return notificationId;
  }
);

export const markAllNotificationsAsRead = createAsyncThunk(
  'notifications/markAllAsRead',
  async () => {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 500));
    return true;
  }
);

export const addNotification = createAsyncThunk(
  'notifications/addNotification',
  async (notification: Omit<Notification, 'id' | 'timestamp'>) => {
    // TODO: Replace with actual API call
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
    };
    return newNotification;
  }
);

const notificationsSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateUnreadCount: (state) => {
      state.unreadCount = state.notifications.filter(n => !n.isRead).length;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch notifications
      .addCase(fetchNotifications.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchNotifications.fulfilled, (state, action) => {
        state.isLoading = false;
        state.notifications = action.payload;
        state.unreadCount = action.payload.filter(n => !n.isRead).length;
      })
      .addCase(fetchNotifications.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch notifications';
      })
      
      // Mark notification as read
      .addCase(markNotificationAsRead.fulfilled, (state, action) => {
        const notificationId = action.payload;
        const notification = state.notifications.find(n => n.id === notificationId);
        if (notification && !notification.isRead) {
          notification.isRead = true;
          state.unreadCount = Math.max(0, state.unreadCount - 1);
        }
      })
      
      // Mark all notifications as read
      .addCase(markAllNotificationsAsRead.fulfilled, (state) => {
        state.notifications.forEach(notification => {
          notification.isRead = true;
        });
        state.unreadCount = 0;
      })
      
      // Add new notification
      .addCase(addNotification.fulfilled, (state, action) => {
        state.notifications.unshift(action.payload);
        if (!action.payload.isRead) {
          state.unreadCount += 1;
        }
      });
  },
});

export const { clearError, updateUnreadCount } = notificationsSlice.actions;
export default notificationsSlice.reducer;

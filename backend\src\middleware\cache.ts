import { Request, Response, NextFunction } from 'express';
import { cacheService } from '../config/redis';
import { logger } from '../utils/logger';

export const cacheMiddleware = (ttlSeconds: number = 300) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Only cache GET requests
    if (req.method !== 'GET') {
      return next();
    }

    const cacheKey = `cache:${req.originalUrl}`;
    
    try {
      const cachedData = await cacheService.get(cacheKey);
      
      if (cachedData) {
        logger.info(`Cache hit for ${cacheKey}`);
        return res.json(cachedData);
      }

      // Store original json method
      const originalJson = res.json;
      
      // Override json method to cache response
      res.json = function(data: any) {
        // Cache successful responses only
        if (res.statusCode === 200) {
          cacheService.set(cacheKey, data, ttlSeconds);
          logger.info(`Cached response for ${cacheKey}`);
        }
        
        // Call original json method
        return originalJson.call(this, data);
      };

      next();
    } catch (error) {
      logger.error('Cache middleware error:', error);
      next();
    }
  };
};

export const invalidateCache = (patterns: string[]) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Store original json method
    const originalJson = res.json;
    
    res.json = function(data: any) {
      // Invalidate cache patterns after successful operations
      if (res.statusCode >= 200 && res.statusCode < 300) {
        patterns.forEach(pattern => {
          cacheService.invalidatePattern(pattern);
        });
      }
      
      return originalJson.call(this, data);
    };

    next();
  };
};
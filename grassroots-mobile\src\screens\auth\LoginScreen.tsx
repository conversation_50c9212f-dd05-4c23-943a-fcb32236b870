import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useDispatch } from 'react-redux';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { AppDispatch } from '../../store';

import { useTheme } from '../../contexts/ThemeContext';
import { googleAuth } from '../../store/slices/authSlice';
import { realGoogleAuthService } from '../../services/realGoogleAuth';

interface LoginScreenProps {
  navigation: any;
}

const LoginScreen: React.FC<LoginScreenProps> = ({ navigation }) => {
  const [isLoading, setIsLoading] = useState(false);
  const dispatch = useDispatch<AppDispatch>();
  const { theme } = useTheme();

  const handleGoogleAuth = async () => {
    setIsLoading(true);

    try {
      console.log('🔥 Starting REAL Google OAuth web flow...');

      // Use REAL Google OAuth that opens actual Google login page
      const googleResult = await realGoogleAuthService.signIn();

      console.log('📥 Google OAuth result type:', googleResult.type);

      if (googleResult.type === 'success' && googleResult.user) {
        console.log('🎉 REAL Google OAuth successful:', googleResult.user.email);

        // Prepare REAL Google OAuth data for backend
        const realGoogleUserData = {
          idToken: googleResult.idToken || '',
          accessToken: googleResult.accessToken || '',
          email: googleResult.user.email,
          firstName: googleResult.user.givenName || googleResult.user.name?.split(' ')[0] || 'User',
          lastName: googleResult.user.familyName || googleResult.user.name?.split(' ').slice(1).join(' ') || '',
          profilePicture: googleResult.user.photo || '',
          googleId: googleResult.user.id,
        };

        console.log('📤 Sending REAL Google OAuth data to backend:', {
          ...realGoogleUserData,
          idToken: realGoogleUserData.idToken ? '[PRESENT]' : '[MISSING]',
          accessToken: realGoogleUserData.accessToken ? '[PRESENT]' : '[MISSING]',
        });

        // Dispatch the Google auth action with REAL data
        const result = await dispatch(googleAuth(realGoogleUserData));
        console.log('📥 Backend auth result:', result.type);

        if (googleAuth.fulfilled.match(result)) {
          console.log('🎉 REAL Authentication successful!');
        } else {
          const errorMessage = result.payload as string || 'Authentication failed';
          console.error('❌ Backend auth failed:', errorMessage);
          Alert.alert('Login Failed', errorMessage);
        }
      } else if (googleResult.type === 'cancelled') {
        console.log('❌ User cancelled REAL Google OAuth');
      } else {
        console.log('❌ REAL Google OAuth failed:', googleResult.error);
        Alert.alert('Login Failed', googleResult.error || 'Google OAuth failed');
      }
    } catch (error: any) {
      console.error('❌ REAL Google OAuth error:', error);
      Alert.alert('Login Failed', 'Something went wrong. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };



  return (
    <LinearGradient
      colors={['#6366F1', '#8B5CF6']}
      style={styles.container}
    >
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.content}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={[styles.title, { color: theme.colors.text.primary }]}>
              Welcome to VIBE
            </Text>
            <Text style={[styles.subtitle, { color: theme.colors.text.secondary }]}>
              Connect with your college community
            </Text>
          </View>

          {/* Buttons */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.googleButton]}
              onPress={handleGoogleAuth}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <>
                  <Ionicons name="logo-google" size={24} color="#fff" />
                  <Text style={styles.buttonText}>Continue with Google</Text>
                </>
              )}
            </TouchableOpacity>
          </View>

          {/* Footer */}
          <View style={styles.footer}>
            <Text style={[styles.footerText, { color: theme.colors.text.secondary }]}>
              By continuing, you agree to our Terms of Service and Privacy Policy
            </Text>
          </View>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  header: {
    alignItems: 'center',
    marginBottom: 60,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.8,
  },
  buttonContainer: {
    gap: 16,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 12,
  },
  googleButton: {
    backgroundColor: '#4285F4',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  footer: {
    marginTop: 60,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 18,
  },
});

export default LoginScreen;

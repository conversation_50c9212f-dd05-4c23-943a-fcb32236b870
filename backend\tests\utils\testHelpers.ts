import { PrismaClient, User, Event, Group } from '@prisma/client';
import jwt from 'jsonwebtoken';

const prisma = new PrismaClient();

export interface TestUserData extends Partial<User> {}
export interface TestEventData extends Partial<Event> {}
export interface TestGroupData extends Partial<Group> {}

export const createTestUser = async (overrides: TestUserData = {}): Promise<User> => {
  return await prisma.user.create({
    data: {
      email: `test${Date.now()}@example.com`,
      firstName: 'Test',
      lastName: 'User',
      university: 'Test University',
      graduationYear: 2024,
      major: 'Computer Science',
      interests: ['technology', 'sports'],
      isVerified: true,
      isActive: true,
      ...overrides
    }
  });
};

export const createTestEvent = async (createdById: string, overrides: TestEventData = {}): Promise<Event> => {
  return await prisma.event.create({
    data: {
      title: 'Test Event',
      description: 'A test event for unit testing',
      location: 'Test Location',
      startTime: new Date(Date.now() + 24 * 60 * 60 * 1000),
      endTime: new Date(Date.now() + 25 * 60 * 60 * 1000),
      capacity: 20,
      createdById,
      ...overrides
    }
  });
};

export const createTestGroup = async (eventId: string, overrides: TestGroupData = {}): Promise<Group> => {
  return await prisma.group.create({
    data: {
      name: 'Test Group',
      description: 'A test group',
      eventId,
      maxMembers: 8,
      ...overrides
    }
  });
};

export const generateTestToken = (userId: string): string => {
  return jwt.sign({ userId }, process.env.JWT_SECRET || 'test-secret', { expiresIn: '1h' });
};

export const addUserToEvent = async (userId: string, eventId: string) => {
  return await prisma.eventParticipant.create({
    data: { userId, eventId }
  });
};

export const addUserToGroup = async (userId: string, groupId: string) => {
  return await prisma.groupMember.create({
    data: { userId, groupId, role: 'MEMBER' }
  });
};

export const cleanupDatabase = async (): Promise<void> => {
  const tables = [
    'directMessage',
    'connection', 
    'report',
    'moderationAction',
    'message',
    'groupMember',
    'group',
    'eventParticipant',
    'event',
    'refreshToken',
    'user'
  ];

  for (const table of tables) {
    await (prisma as any)[table].deleteMany();
  }
};




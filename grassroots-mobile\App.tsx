import React, { useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { Provider, useDispatch } from 'react-redux';
import { store } from './src/store';
import AppNavigator from './src/navigation/AppNavigator';
import { ThemeProvider, useTheme } from './src/contexts/ThemeContext';
import { initializeAuth } from './src/store/slices/authSlice';

// StatusBar component that responds to theme changes
const ThemedStatusBar: React.FC = () => {
  const { isDark } = useTheme();
  return <StatusBar style={isDark ? "light" : "dark"} />;
};

// App content component that has access to Redux
const AppContent: React.FC = () => {
  const dispatch = useDispatch();

  useEffect(() => {
    // Initialize authentication on app start
    dispatch(initializeAuth() as any);
  }, [dispatch]);

  return (
    <>
      <AppNavigator />
      <ThemedStatusBar />
    </>
  );
};

export default function App() {
  return (
    <Provider store={store}>
      <ThemeProvider>
        <AppContent />
      </ThemeProvider>
    </Provider>
  );
}

import { prisma } from '../config/database';
import { CreateGroupData } from '../models/Group';
import { logger } from '../utils/logger';

export const groupService = {
  async createGroupsForEvent(eventId: string) {
    // Get all participants for the event
    const participants = await prisma.eventParticipant.findMany({
      where: { eventId },
      include: { user: true }
    });

    if (participants.length < 4) {
      logger.info(`Not enough participants for group formation: ${participants.length}`);
      return;
    }

    // Calculate number of groups (4-6 people per group)
    const groupSize = Math.min(6, Math.max(4, Math.floor(participants.length / Math.ceil(participants.length / 6))));
    const numGroups = Math.ceil(participants.length / groupSize);

    // Shuffle participants for random grouping
    const shuffledParticipants = [...participants].sort(() => Math.random() - 0.5);

    const groups = [];
    for (let i = 0; i < numGroups; i++) {
      const groupParticipants = shuffledParticipants.slice(i * groupSize, (i + 1) * groupSize);
      
      const group = await prisma.group.create({
        data: {
          eventId,
          name: `Group ${i + 1}`,
          description: `Auto-generated group for event`,
          maxMembers: groupSize,
          currentMembers: groupParticipants.length,
        }
      });

      // Add members to group
      await prisma.groupMember.createMany({
        data: groupParticipants.map((participant, index) => ({
          groupId: group.id,
          userId: participant.userId,
          role: index === 0 ? 'ADMIN' : 'MEMBER' as const
        }))
      });

      groups.push(group);
      logger.info(`Created group ${group.id} with ${groupParticipants.length} members`);
    }

    return groups;
  },

  async getGroupsByEvent(eventId: string) {
    return await prisma.group.findMany({
      where: { eventId },
      include: {
        members: {
          include: {
            user: {
              select: { id: true, firstName: true, lastName: true, profilePicture: true }
            }
          }
        },
        event: {
          select: { id: true, title: true, startTime: true }
        }
      }
    });
  },

  async getGroupById(groupId: string) {
    return await prisma.group.findUnique({
      where: { id: groupId },
      include: {
        members: {
          include: {
            user: {
              select: { id: true, firstName: true, lastName: true, profilePicture: true }
            }
          }
        },
        event: {
          select: { id: true, title: true, startTime: true, location: true }
        }
      }
    });
  },

  async getUserGroups(userId: string) {
    return await prisma.groupMember.findMany({
      where: { userId },
      include: {
        group: {
          include: {
            event: {
              select: { id: true, title: true, startTime: true, location: true }
            },
            _count: { select: { members: true } }
          }
        }
      },
      orderBy: { joinedAt: 'desc' }
    });
  },

  async sendMessage(groupId: string, senderId: string, content: string, messageType: 'TEXT' | 'IMAGE' | 'VOICE' = 'TEXT') {
    // Verify user is member of group
    const membership = await prisma.groupMember.findUnique({
      where: {
        groupId_userId: { groupId, userId: senderId }
      }
    });

    if (!membership) {
      throw new Error('User is not a member of this group');
    }

    const message = await prisma.message.create({
      data: {
        groupId,
        senderId,
        content,
        messageType,
      },
      include: {
        sender: {
          select: { id: true, firstName: true, lastName: true, profilePicture: true }
        }
      }
    });

    logger.info(`Message sent in group ${groupId} by user ${senderId}`);
    return message;
  },

  async getGroupMessages(groupId: string, page: number = 1, limit: number = 50) {
    const skip = (page - 1) * limit;

    const messages = await prisma.message.findMany({
      where: { groupId },
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        sender: {
          select: { id: true, firstName: true, lastName: true, profilePicture: true }
        }
      }
    });

    return messages.reverse(); // Return in chronological order
  }
};
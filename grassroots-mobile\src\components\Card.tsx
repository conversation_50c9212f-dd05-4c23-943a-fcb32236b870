import React, { useRef } from 'react';
import {
  View,
  StyleSheet,
  ViewStyle,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { AnimationUtils } from '../utils/animations';

interface CardProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'outlined' | 'flat';
  padding?: 'none' | 'small' | 'medium' | 'large';
  margin?: 'none' | 'small' | 'medium' | 'large';
  onPress?: () => void;
  style?: ViewStyle;
  disabled?: boolean;
}

const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  padding = 'medium',
  margin = 'medium',
  onPress,
  style,
  disabled = false,
}) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const scaleValue = useRef(new Animated.Value(1)).current;
  const shadowValue = useRef(new Animated.Value(0)).current;
  const animationHandlers = onPress ? AnimationUtils.cardHover(scaleValue, shadowValue) : null;
  const paddingKey = `padding${padding.charAt(0).toUpperCase() + padding.slice(1)}` as keyof typeof styles;
  const marginKey = `margin${margin.charAt(0).toUpperCase() + margin.slice(1)}` as keyof typeof styles;

  const cardStyle = [
    styles.base,
    styles[variant],
    styles[paddingKey],
    styles[marginKey],
    disabled && styles.disabled,
    style,
  ];

  if (onPress) {
    return (
      <Animated.View
        style={[
          { transform: [{ scale: scaleValue }] }
        ]}
      >
        <TouchableOpacity
          style={cardStyle}
          onPress={onPress}
          disabled={disabled}
          activeOpacity={0.9}
          onPressIn={animationHandlers?.onPressIn}
          onPressOut={animationHandlers?.onPressOut}
        >
          {children}
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return (
    <View style={cardStyle}>
      {children}
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  base: {
    borderRadius: theme.borderRadius.lg,
    backgroundColor: theme.colors.background.card,
  },
  
  // Variants
  default: {
    ...theme.shadows.base,
  },
  
  elevated: {
    ...theme.shadows.md,
  },
  
  outlined: {
    borderWidth: 1,
    borderColor: theme.colors.border.light,
  },
  
  flat: {
    // No shadow or border
  },
  
  // Padding variants
  paddingNone: {
    padding: 0,
  },
  
  paddingSmall: {
    padding: theme.spacing[3],
  },
  
  paddingMedium: {
    padding: theme.spacing[5],
  },
  
  paddingLarge: {
    padding: theme.spacing[6],
  },
  
  // Margin variants
  marginNone: {
    margin: 0,
  },
  
  marginSmall: {
    marginBottom: theme.spacing[2],
  },
  
  marginMedium: {
    marginBottom: theme.spacing[4],
  },
  
  marginLarge: {
    marginBottom: theme.spacing[6],
  },
  
  // States
  disabled: {
    opacity: 0.6,
  },
});

export default Card;

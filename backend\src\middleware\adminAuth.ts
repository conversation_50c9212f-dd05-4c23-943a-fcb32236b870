import { Response, NextFunction } from 'express';
import { AuthRequest } from './auth';
import { prisma } from '../config/database';
import { logger } from '../utils/logger';

export const requireAdmin = async (req: AuthRequest, res: Response, next: NextFunction) => {
  try {
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Check if user has admin privileges
    // For now, we'll use email domain or a specific admin flag
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { email: true, isActive: true }
    });

    if (!user || !user.isActive) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Check if user is admin (you can modify this logic based on your requirements)
    const isAdmin = user.email.endsWith('@grassroots.admin') || 
                   process.env.ADMIN_EMAILS?.split(',').includes(user.email);

    if (!isAdmin) {
      logger.warn(`Unauthorized admin access attempt by user: ${userId}`);
      return res.status(403).json({ error: 'Admin privileges required' });
    }

    next();
  } catch (error) {
    logger.error('Admin auth middleware error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};
import { Server } from 'socket.io';
import { createServer } from 'http';
import Client from 'socket.io-client';
import { createTestUser, createTestEvent, createTestGroup, addUserToGroup, generateTestToken, cleanupDatabase } from '../utils/testHelpers';
import { initializeSocket } from '../../src/socket/socketHandlers';

describe('Socket.io Integration', () => {
  let io: Server;
  let serverSocket: any;
  let clientSocket: any;
  let httpServer: any;

  beforeAll((done) => {
    httpServer = createServer();
    io = new Server(httpServer);
    initializeSocket(io);
    
    httpServer.listen(() => {
      const port = (httpServer.address() as any).port;
      clientSocket = Client(`http://localhost:${port}`);
      
      io.on('connection', (socket) => {
        serverSocket = socket;
      });
      
      clientSocket.on('connect', done);
    });
  });

  afterAll(() => {
    io.close();
    clientSocket.close();
    httpServer.close();
  });

  afterEach(async () => {
    await cleanupDatabase();
  });

  describe('Authentication', () => {
    it('should authenticate socket connection with valid token', async () => {
      const user = await createTestUser();
      const token = generateTestToken(user.id);

      const authenticatedClient = Client(`http://localhost:${(httpServer.address() as any).port}`, {
        auth: { token }
      });

      return new Promise((resolve) => {
        authenticatedClient.on('connect', () => {
          expect(authenticatedClient.connected).toBe(true);
          authenticatedClient.close();
          resolve(true);
        });
      });
    });

    it('should reject connection with invalid token', async () => {
      const unauthenticatedClient = Client(`http://localhost:${(httpServer.address() as any).port}`, {
        auth: { token: 'invalid-token' }
      });

      return new Promise((resolve) => {
        unauthenticatedClient.on('connect_error', (error) => {
          expect(error.message).toBe('Authentication failed');
          resolve(true);
        });
      });
    });
  });

  describe('Group Messaging', () => {
    it('should handle group message sending', async () => {
      const user = await createTestUser();
      const event = await createTestEvent(user.id);
      const group = await createTestGroup(event.id);
      await addUserToGroup(user.id, group.id);

      return new Promise((resolve) => {
        serverSocket.on('send-message', (data: any) => {
          expect(data.groupId).toBe(group.id);
          expect(data.content).toBe('Test message');
          resolve(true);
        });

        clientSocket.emit('send-message', {
          groupId: group.id,
          content: 'Test message'
        });
      });
    });

    it('should handle typing indicators', async () => {
      const user = await createTestUser();
      const event = await createTestEvent(user.id);
      const group = await createTestGroup(event.id);

      return new Promise((resolve) => {
        serverSocket.on('typing', (data: any) => {
          expect(data.groupId).toBe(group.id);
          expect(data.isTyping).toBe(true);
          resolve(true);
        });

        clientSocket.emit('typing', {
          groupId: group.id,
          isTyping: true
        });
      });
    });
  });
});
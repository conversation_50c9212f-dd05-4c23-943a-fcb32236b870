# 🎯 **GRASSROOTS** Development Prompts

*Detailed prompts for each development phase to guide AI-assisted development*

---

## 🏗️ **PHASE 0: FOUNDATION PROMPTS**

### Prompt 0.1: Environment Setup
```
I need to set up a complete React Native development environment for the Grassroots social app. Please help me:

1. Install and configure Node.js, Expo CLI, and all necessary development tools
2. Set up VS Code with the best React Native extensions
3. Configure Android Studio and/or Xcode for device testing
4. Install PostgreSQL and set up a local database
5. Create a new Expo project with TypeScript template named "grassroots-mobile"
6. Set up a Node.js backend project with Express and TypeScript named "grassroots-backend"

Please provide step-by-step instructions for my operating system and include any troubleshooting tips.
```

### Prompt 0.2: Project Structure Setup
```
Help me create the optimal project structure for the Grassroots app. I need:

1. A monorepo structure with mobile app, backend, and admin panel
2. Proper folder organization for React Native with TypeScript
3. Backend structure with Express, Prisma, and PostgreSQL
4. Shared types and utilities between frontend and backend
5. Configuration files for <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>sky
6. Basic CI/CD setup with GitHub Actions
7. Initial package.json files with all necessary dependencies

Please create the complete folder structure and all configuration files.
```

### Prompt 0.3: Database Schema Design
```
Design the initial database schema for Grassroots using Prisma. The schema should include:

1. Users table with authentication and profile data
2. Events table for social experiences
3. Groups table for event participants
4. Messages table for chat functionality
5. Connections table for friend/spark relationships
6. Reports table for safety and moderation
7. Proper relationships, indexes, and constraints

Please create the Prisma schema file with all necessary models and relationships.
```

---

## 🔐 **PHASE 1: AUTHENTICATION & PROFILES PROMPTS**

### Prompt 1.1: Authentication System
```
Implement a complete authentication system for Grassroots using Firebase Auth and React Native. I need:

1. University email registration with .net domain validation
2. Email verification flow
3. Secure login/logout functionality
4. Password reset capability
5. Token management and refresh
6. Protected route navigation
7. Authentication state management with Redux

Please create all necessary components, services, and Redux slices for authentication.
```

### Prompt 1.2: Profile Creation Flow
```
Create a comprehensive user profile setup flow for Grassroots. The flow should include:

1. Welcome screen with app introduction
2. Photo upload with image compression and validation
3. 5 "Vibe Prompts" with engaging questions about personality and interests
4. Optional voice note recording (30 seconds max)
5. Interest tags selection
6. Profile preview and confirmation
7. Smooth navigation between steps with progress indicator

Design this as a multi-step wizard with beautiful UI and smooth animations.
```

### Prompt 1.3: Profile Management System
```
Build a complete profile management system that allows users to:

1. View their current profile with all information
2. Edit profile information including photo, prompts, and voice note
3. Manage privacy settings and visibility options
4. Update interest tags and preferences
5. View profile statistics (events attended, connections made)
6. Delete account functionality with proper data cleanup

Include proper validation, error handling, and user feedback throughout.
```

---

## 🎪 **PHASE 2: EVENT SYSTEM PROMPTS**

### Prompt 2.1: Event Discovery Interface
```
Create the main Event Discovery interface (Experience Hub) for Grassroots. This should include:

1. Scrollable feed of upcoming events with beautiful cards
2. Event filtering by category, date, location, and group size
3. Search functionality for finding specific events
4. Event detail modal with full information and opt-in button
5. Map integration showing event locations
6. Pull-to-refresh and infinite scroll
7. Empty states and loading animations

Focus on creating an engaging, Instagram-like feed experience that makes users excited to join events.
```

### Prompt 2.2: Event Creation System
```
Build a comprehensive event creation system for both admins and users. Include:

1. Event creation form with all necessary fields (title, description, location, time, capacity)
2. Location picker with map integration and venue search
3. Category and tag selection
4. Photo upload for event images
5. Recurring event options
6. Event preview before publishing
7. Draft saving functionality
8. Different creation flows for "Grassroots Curated" vs "Community Hosted" events

Make the interface intuitive and guide users through creating engaging events.
```

### Prompt 2.3: Opt-in System & Event Management
```
Implement the event opt-in system and user event management. This should include:

1. One-tap opt-in functionality with confirmation
2. Opt-in limits and capacity management
3. Waitlist system for popular events
4. User's event dashboard showing opted-in events
5. Opt-out functionality with proper notifications
6. Event reminders and notifications
7. Event status tracking (upcoming, active, completed)
8. Integration with calendar apps

Ensure the system handles edge cases like event cancellations and capacity changes.
```

---

## 💬 **PHASE 3: GROUP FORMATION & CHAT PROMPTS**

### Prompt 3.1: Group Formation Algorithm
```
Create an intelligent group formation algorithm for Grassroots events. The algorithm should:

1. Automatically create groups of 4-6 people 24 hours before events
2. Balance groups based on user preferences, interests, and demographics
3. Avoid putting the same people together repeatedly
4. Handle edge cases like odd numbers of participants
5. Consider user compatibility scores and past interactions
6. Allow manual admin override for special circumstances
7. Send notifications when groups are formed

Implement this as a background service that runs automatically and includes proper logging.
```

### Prompt 3.2: Real-time Chat System
```
Build a complete real-time chat system using Socket.io for group conversations. Include:

1. Real-time message sending and receiving
2. Message persistence and chat history
3. Typing indicators and read receipts
4. Emoji reactions and basic formatting
5. Image sharing with compression
6. Message moderation and filtering
7. Chat room management (join/leave)
8. Offline message queuing

Create a modern, WhatsApp-like chat interface that's intuitive and responsive.
```

### Prompt 3.3: Chat Moderation & Safety
```
Implement comprehensive chat moderation and safety features:

1. Automatic content filtering for inappropriate messages
2. Real-time message reporting system
3. Admin chat monitoring dashboard
4. User blocking and muting capabilities
5. Chat history export for investigations
6. Automated warning system for policy violations
7. Integration with the main reporting system
8. Emergency escalation procedures

Focus on creating a safe environment while maintaining user privacy and chat flow.
```

---

## 💖 **PHASE 4: POST-EVENT CONNECTIONS PROMPTS**

### Prompt 4.1: Post-Event Review System
```
Create an engaging post-event review and rating system:

1. Automatic trigger when events end
2. Anonymous profile cards for group members
3. Event experience rating and feedback
4. Photo sharing from the event (optional)
5. Event highlights and memorable moments
6. Attendance confirmation system
7. No-show reporting and tracking
8. Integration with user reputation system

Design this to feel like a fun, social experience rather than a chore.
```

### Prompt 4.2: Friend/Spark Matching System
```
Implement the core Friend/Spark/Pass matching functionality:

1. Anonymous profile review interface with swipe gestures
2. Clear distinction between "Friend" and "Spark" options
3. Mutual connection detection and notification
4. Connection success animations and celebrations
5. Connection probability scoring based on interactions
6. Recommendation system for likely matches
7. Connection history and statistics
8. Privacy controls for connection visibility

Make this feel magical and rewarding when connections are made.
```

### Prompt 4.3: Permanent Chat & Connection Management
```
Build the permanent 1-on-1 chat system and connection management:

1. Seamless transition from group to 1-on-1 chat after mutual connections
2. Connection-based chat access and permissions
3. Rich messaging features (voice notes, photos, location sharing)
4. Connection categorization (close friends, acquaintances, potential dates)
5. Connection activity tracking and engagement metrics
6. Connection search and filtering
7. Relationship status updates and milestones
8. Connection export and backup features

Create a comprehensive system for managing ongoing relationships formed through the app.
```

---

## 🛡️ **PHASE 5: SAFETY & MODERATION PROMPTS**

### Prompt 5.1: Comprehensive Reporting System
```
Build a multi-level reporting system for user safety:

1. Easy-access report buttons throughout the app
2. Categorized reporting options (harassment, inappropriate content, safety concerns)
3. Evidence collection system (screenshots, chat logs, voice recordings)
4. Anonymous reporting capabilities
5. Report tracking and status updates for users
6. Automated report prioritization based on severity
7. Integration with admin moderation tools
8. Follow-up system for resolved reports

Ensure the system is accessible, comprehensive, and encourages users to report issues.
```

### Prompt 5.2: Wall of Shame Implementation
```
Create the "Wall of Shame" public safety feature:

1. Public database of verified problematic users
2. Community voting system for adding users to the wall
3. Evidence requirements and verification process
4. Appeal system for wrongly accused users
5. Automatic removal after rehabilitation period
6. Integration with university safety offices
7. Privacy protection for reporters
8. Legal compliance and liability protection

Balance public safety with due process and privacy rights.
```

### Prompt 5.3: Admin Moderation Dashboard
```
Build a comprehensive admin moderation dashboard:

1. Real-time report queue with prioritization
2. User investigation tools and history
3. Chat monitoring and keyword alerts
4. Ban and suspension management system
5. Automated action logging and audit trails
6. Bulk moderation tools for efficiency
7. Analytics on safety metrics and trends
8. Integration with external safety services

Create powerful tools that enable efficient and fair moderation at scale.
```

---

## 🎨 **PHASE 6: UI/UX POLISH PROMPTS**

### Prompt 6.1: Design System Implementation
```
Implement a complete design system for Grassroots:

1. Color palette with primary, secondary, and accent colors
2. Typography system with consistent font sizes and weights
3. Component library with buttons, cards, inputs, and modals
4. Icon set with consistent style and sizing
5. Spacing and layout guidelines
6. Animation and transition standards
7. Dark mode support throughout the app
8. Accessibility compliance (WCAG 2.1 AA)

Create reusable components that maintain consistency across the entire app.
```

### Prompt 6.2: Animations & Micro-interactions
```
Add delightful animations and micro-interactions throughout Grassroots:

1. Smooth page transitions and navigation animations
2. Loading states with engaging animations
3. Success celebrations for connections and opt-ins
4. Gesture-based interactions (swipe, pull-to-refresh)
5. Haptic feedback for important actions
6. Skeleton loading for content
7. Parallax effects and subtle motion
8. Performance optimization for smooth 60fps

Focus on creating a premium, polished feel without sacrificing performance.
```

### Prompt 6.3: Performance Optimization
```
Optimize Grassroots for maximum performance and user experience:

1. Image optimization and lazy loading
2. Code splitting and bundle optimization
3. Memory leak prevention and cleanup
4. Network request optimization and caching
5. Database query optimization
6. Offline functionality for core features
7. App startup time optimization
8. Battery usage optimization

Ensure the app performs well on older devices and slower networks.
```

---

## 🚀 **PHASE 7: LAUNCH PREPARATION PROMPTS**

### Prompt 7.1: Production Deployment
```
Set up production infrastructure and deployment for Grassroots:

1. AWS/GCP production environment setup
2. Database migration and backup strategies
3. CDN configuration for static assets
4. SSL certificates and security hardening
5. Environment variable management
6. CI/CD pipeline for automated deployments
7. Monitoring and alerting systems
8. Scaling and load balancing configuration

Ensure the infrastructure can handle launch traffic and scale as needed.
```

### Prompt 7.2: App Store Preparation
```
Prepare Grassroots for App Store and Google Play Store submission:

1. App store listing optimization (ASO)
2. Screenshots and promotional materials
3. App descriptions and metadata
4. Privacy policy and terms of service
5. App store compliance and guidelines
6. Beta testing with TestFlight and Play Console
7. App review preparation and submission
8. Launch day coordination and monitoring

Create compelling store listings that drive downloads and user engagement.
```

### Prompt 7.3: Analytics & Monitoring Setup
```
Implement comprehensive analytics and monitoring for Grassroots:

1. User behavior tracking and funnel analysis
2. Performance monitoring and error tracking
3. Business metrics and KPI dashboards
4. A/B testing framework for feature optimization
5. Push notification analytics and optimization
6. Revenue tracking and financial reporting
7. User feedback collection and analysis
8. Crash reporting and debugging tools

Set up systems to understand user behavior and optimize the app post-launch.
```

---

## 🎯 **SPECIALIZED PROMPTS**

### Admin Panel Development
```
Create a comprehensive admin panel for Grassroots management:

1. User management and moderation tools
2. Event creation and management interface
3. Analytics dashboard with key metrics
4. Content moderation and reporting system
5. Partner location management
6. Push notification management
7. A/B testing configuration
8. System health monitoring

Build this as a responsive web application using Next.js with role-based access control.
```

### Partner Integration System
```
Develop a partner integration system for Grassroots Spots:

1. Partner onboarding and verification process
2. Location management and capacity tracking
3. Event booking and scheduling system
4. Partner analytics and performance metrics
5. Revenue sharing and payment processing
6. Partner communication tools
7. QR code generation for location check-ins
8. Partner feedback and rating system

Create a win-win system that benefits both partners and users.
```

### Campus Ambassador Tools
```
Build tools for campus ambassadors (Grassroots Connectors):

1. Ambassador dashboard with performance metrics
2. Event promotion and sharing tools
3. User recruitment and referral tracking
4. Community building and engagement features
5. Training materials and resources
6. Communication tools with admin team
7. Reward and recognition system
8. Campus-specific customization options

Empower ambassadors to effectively grow and manage their campus communities.
```

---

## 📱 **PLATFORM-SPECIFIC PROMPTS**

### iOS Optimization
```
Optimize Grassroots specifically for iOS:

1. iOS design guidelines compliance
2. iPhone and iPad responsive design
3. iOS-specific features (Siri shortcuts, widgets)
4. Apple Push Notification service integration
5. iOS accessibility features
6. App Store optimization for iOS
7. iOS performance optimization
8. Integration with iOS system features

Ensure the app feels native and takes advantage of iOS-specific capabilities.
```

### Android Optimization
```
Optimize Grassroots specifically for Android:

1. Material Design implementation
2. Android adaptive icons and themes
3. Android-specific features (shortcuts, widgets)
4. Firebase Cloud Messaging integration
5. Android accessibility features
6. Google Play Store optimization
7. Android performance optimization
8. Integration with Android system features

Create an Android experience that feels native and performant across device variations.
```

---

*These prompts provide detailed guidance for each development phase. Use them sequentially as you progress through building Grassroots, adapting them based on your specific needs and discoveries during development.*

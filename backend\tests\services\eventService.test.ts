import { eventService } from '../../src/services/eventService';
import { createTestUser, createTestEvent, cleanupDatabase } from '../utils/testHelpers';

describe('EventService', () => {
  afterEach(async () => {
    await cleanupDatabase();
  });

  describe('createEvent', () => {
    it('should create a new event', async () => {
      const user = await createTestUser();
      const eventData = {
        title: 'Test Event',
        description: 'Test Description',
        location: 'Test Location',
        startTime: new Date(Date.now() + 24 * 60 * 60 * 1000),
        endTime: new Date(Date.now() + 25 * 60 * 60 * 1000),
        capacity: 20
      };

      const event = await eventService.createEvent(user.id, eventData);

      expect(event.title).toBe(eventData.title);
      expect(event.createdById).toBe(user.id);
      expect(event.currentParticipants).toBe(0);
    });

    it('should throw error for past start time', async () => {
      const user = await createTestUser();
      const eventData = {
        title: 'Test Event',
        description: 'Test Description',
        location: 'Test Location',
        startTime: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
        endTime: new Date(Date.now() + 60 * 60 * 1000),
        capacity: 20
      };

      await expect(eventService.createEvent(user.id, eventData))
        .rejects.toThrow('Event start time cannot be in the past');
    });
  });

  describe('joinEvent', () => {
    it('should allow user to join event', async () => {
      const creator = await createTestUser();
      const participant = await createTestUser();
      const event = await createTestEvent(creator.id);

      const result = await eventService.joinEvent(event.id, participant.id);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Successfully joined event');
    });

    it('should prevent joining full event', async () => {
      const creator = await createTestUser();
      const event = await createTestEvent(creator.id, { capacity: 1 });
      
      // Fill the event
      const participant1 = await createTestUser();
      await eventService.joinEvent(event.id, participant1.id);

      // Try to join when full
      const participant2 = await createTestUser();
      const result = await eventService.joinEvent(event.id, participant2.id);

      expect(result.success).toBe(false);
      expect(result.message).toBe('Event is at full capacity');
    });
  });

  describe('getEvents', () => {
    it('should return paginated events', async () => {
      const user = await createTestUser();
      await createTestEvent(user.id, { title: 'Event 1' });
      await createTestEvent(user.id, { title: 'Event 2' });

      const result = await eventService.getEvents(1, 10);

      expect(result.events).toHaveLength(2);
      expect(result.pagination.total).toBe(2);
    });

    it('should filter events by search term', async () => {
      const user = await createTestUser();
      await createTestEvent(user.id, { title: 'Basketball Game' });
      await createTestEvent(user.id, { title: 'Study Group' });

      const result = await eventService.getEvents(1, 10, 'Basketball');

      expect(result.events).toHaveLength(1);
      expect(result.events[0].title).toBe('Basketball Game');
    });
  });
});
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { eventAPI, Event, EventQueryParams } from '../../services/eventAPI';

interface Event {
  id: string;
  title: string;
  description: string;
  category: string;
  eventType: 'curated' | 'community';
  startTime: string;
  endTime: string;
  location: {
    id: string;
    name: string;
    address: string;
    latitude: number;
    longitude: number;
  };
  capacity: number;
  currentAttendees: number;
  imageUrl?: string;
  tags: string[];
  createdBy: string;
  isOptedIn: boolean;
  groupSize: {
    min: number;
    max: number;
  };
}

interface EventsState {
  events: Event[];
  myEvents: Event[];
  featuredEvents: Event[];
  isLoading: boolean;
  error: string | null;
  filters: {
    category: string;
    date: string;
    location: string;
  };
  searchQuery: string;
}

const initialState: EventsState = {
  events: [],
  myEvents: [],
  featuredEvents: [],
  isLoading: false,
  error: null,
  filters: {
    category: 'all',
    date: 'all',
    location: 'all',
  },
  searchQuery: '',
};

// Mock events data
const mockEvents: Event[] = [
  {
    id: '1',
    title: 'Coffee & Connections',
    description: 'Start your morning with great coffee and even better conversations! Meet fellow students in a cozy café setting.',
    category: 'social',
    eventType: 'curated',
    startTime: '2025-07-12T09:00:00Z',
    endTime: '2025-07-12T11:00:00Z',
    location: {
      id: 'loc1',
      name: 'Campus Café',
      address: '123 University Ave',
      latitude: 40.7128,
      longitude: -74.0060,
    },
    capacity: 24,
    currentAttendees: 18,
    imageUrl: 'https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=400',
    tags: ['coffee', 'morning', 'networking'],
    createdBy: 'admin',
    isOptedIn: false,
    groupSize: { min: 4, max: 6 },
  },
  {
    id: '2',
    title: 'Taco Tuesday Fiesta',
    description: 'Spice up your Tuesday with delicious tacos and new friendships! Authentic Mexican food and great vibes.',
    category: 'food',
    eventType: 'curated',
    startTime: '2025-07-15T18:00:00Z',
    endTime: '2025-07-15T20:00:00Z',
    location: {
      id: 'loc2',
      name: 'El Mariachi Restaurant',
      address: '456 College St',
      latitude: 40.7589,
      longitude: -73.9851,
    },
    capacity: 30,
    currentAttendees: 12,
    imageUrl: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400',
    tags: ['food', 'mexican', 'dinner'],
    createdBy: 'admin',
    isOptedIn: true,
    groupSize: { min: 5, max: 6 },
  },
  {
    id: '3',
    title: 'Study Group: React Native',
    description: 'Learn React Native together! Bring your laptop and questions. Perfect for beginners and intermediate developers.',
    category: 'study',
    eventType: 'community',
    startTime: '2025-07-14T15:00:00Z',
    endTime: '2025-07-14T17:00:00Z',
    location: {
      id: 'loc3',
      name: 'Library Study Room B',
      address: '789 Academic Way',
      latitude: 40.7505,
      longitude: -73.9934,
    },
    capacity: 12,
    currentAttendees: 8,
    imageUrl: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400',
    tags: ['study', 'programming', 'react-native'],
    createdBy: 'user123',
    isOptedIn: false,
    groupSize: { min: 3, max: 4 },
  },
];

// Async thunks
export const fetchEvents = createAsyncThunk(
  'events/fetchEvents',
  async (params: EventQueryParams = {}, { rejectWithValue }) => {
    try {
      const response = await eventAPI.getEvents(params);
      return response.events;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch events');
    }
  }
);

export const optInToEvent = createAsyncThunk(
  'events/optIn',
  async (eventId: string, { rejectWithValue }) => {
    try {
      await eventAPI.joinEvent(eventId);
      return eventId;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to join event');
    }
  }
);

export const optOutOfEvent = createAsyncThunk(
  'events/optOut',
  async (eventId: string, { rejectWithValue }) => {
    try {
      await eventAPI.leaveEvent(eventId);
      return eventId;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to leave event');
    }
  }
);

export const createEvent = createAsyncThunk(
  'events/create',
  async (eventData: Omit<Event, 'id' | 'currentAttendees' | 'isOptedIn' | 'createdBy'>) => {
    // TODO: Replace with actual API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const newEvent: Event = {
      ...eventData,
      id: Date.now().toString(),
      currentAttendees: 1,
      isOptedIn: true,
      createdBy: 'current-user',
    };
    
    return newEvent;
  }
);

const eventsSlice = createSlice({
  name: 'events',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<Partial<typeof initialState.filters>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    updateEventOptIn: (state, action: PayloadAction<{ eventId: string; isOptedIn: boolean }>) => {
      const event = state.events.find(e => e.id === action.payload.eventId);
      if (event) {
        event.isOptedIn = action.payload.isOptedIn;
        event.currentAttendees += action.payload.isOptedIn ? 1 : -1;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Events
      .addCase(fetchEvents.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchEvents.fulfilled, (state, action) => {
        state.isLoading = false;
        state.events = action.payload;
        state.featuredEvents = action.payload.filter(event => event.eventType === 'curated').slice(0, 3);
      })
      .addCase(fetchEvents.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch events';
      })
      // Opt In
      .addCase(optInToEvent.fulfilled, (state, action) => {
        const event = state.events.find(e => e.id === action.payload);
        if (event) {
          event.isOptedIn = true;
          event.currentAttendees += 1;
        }
      })
      // Opt Out
      .addCase(optOutOfEvent.fulfilled, (state, action) => {
        const event = state.events.find(e => e.id === action.payload);
        if (event) {
          event.isOptedIn = false;
          event.currentAttendees -= 1;
        }
      })
      // Create Event
      .addCase(createEvent.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createEvent.fulfilled, (state, action) => {
        state.isLoading = false;
        state.events.unshift(action.payload);
        state.myEvents.unshift(action.payload);
      })
      .addCase(createEvent.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to create event';
      });
  },
});

export const { setFilters, setSearchQuery, clearError, updateEventOptIn } = eventsSlice.actions;
export default eventsSlice.reducer;

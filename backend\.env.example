# Database
DATABASE_URL="postgresql://username:password@localhost:5432/grassroots_db"
DATABASE_URL_TEST="postgresql://username:password@localhost:5432/grassroots_test"

# JWT
JWT_SECRET="your-super-secret-jwt-key-change-in-production"
JWT_REFRESH_SECRET="your-super-secret-refresh-key-change-in-production"
JWT_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="7d"

# Google OAuth
GOOGLE_CLIENT_ID="785207626289-426s8483mlj0mg562k586peu4v3d847v.apps.googleusercontent.com"

# Server
PORT=3000
NODE_ENV="development"

# Redis (for caching and sessions)
REDIS_URL="redis://localhost:6379"
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_PASSWORD=""

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH="./uploads"

# AWS S3 (for production file storage)
AWS_ACCESS_KEY_ID=""
AWS_SECRET_ACCESS_KEY=""
AWS_REGION=""
AWS_S3_BUCKET=""

# Google Cloud Storage (alternative to S3)
GOOGLE_CLOUD_PROJECT_ID=""
GOOGLE_CLOUD_STORAGE_BUCKET=""
GOOGLE_APPLICATION_CREDENTIALS=""

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
AUTH_RATE_LIMIT_MAX=10
MESSAGE_RATE_LIMIT_MAX=30

# Caching
CACHE_TTL_EVENTS=300
CACHE_TTL_EVENT_DETAILS=600
CACHE_TTL_USER_PROFILE=1800

# Logging
LOG_LEVEL="info"
SENTRY_DSN=""

# Admin
ADMIN_EMAIL="<EMAIL>"
ADMIN_SECRET_KEY="your-admin-secret-key"

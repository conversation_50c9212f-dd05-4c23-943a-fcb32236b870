import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';

// Haptic feedback utility for enhanced user experience
export class HapticUtils {
  // Check if haptics are available on the device
  static isAvailable(): boolean {
    return Platform.OS === 'ios' || Platform.OS === 'android';
  }

  // Light haptic feedback for subtle interactions
  static light(): void {
    if (this.isAvailable()) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  }

  // Medium haptic feedback for standard interactions
  static medium(): void {
    if (this.isAvailable()) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
  }

  // Heavy haptic feedback for important interactions
  static heavy(): void {
    if (this.isAvailable()) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    }
  }

  // Success haptic feedback
  static success(): void {
    if (this.isAvailable()) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
  }

  // Warning haptic feedback
  static warning(): void {
    if (this.isAvailable()) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
    }
  }

  // Error haptic feedback
  static error(): void {
    if (this.isAvailable()) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }
  }

  // Selection haptic feedback for pickers and selectors
  static selection(): void {
    if (this.isAvailable()) {
      Haptics.selectionAsync();
    }
  }

  // Custom haptic patterns for specific interactions
  static buttonPress(): void {
    this.light();
  }

  static cardTap(): void {
    this.medium();
  }

  static swipeAction(): void {
    this.light();
  }

  static pullToRefresh(): void {
    this.medium();
  }

  static connectionMade(): void {
    this.success();
  }

  static eventJoined(): void {
    this.success();
  }

  static messageReceived(): void {
    this.light();
  }

  static messageSent(): void {
    this.medium();
  }

  static navigationTransition(): void {
    this.light();
  }

  static modalOpen(): void {
    this.medium();
  }

  static modalClose(): void {
    this.light();
  }

  static toggleSwitch(): void {
    this.medium();
  }

  static sliderChange(): void {
    this.light();
  }

  static formSubmit(): void {
    this.heavy();
  }

  static formError(): void {
    this.error();
  }

  static deleteAction(): void {
    this.warning();
  }

  static confirmAction(): void {
    this.heavy();
  }

  // Haptic feedback for specific app actions
  static vibeMatch(): void {
    // Special pattern for when users match
    if (this.isAvailable()) {
      setTimeout(() => this.medium(), 0);
      setTimeout(() => this.light(), 100);
      setTimeout(() => this.medium(), 200);
    }
  }

  static eventDiscovered(): void {
    this.light();
  }

  static profileUpdate(): void {
    this.success();
  }

  static chatOpened(): void {
    this.light();
  }

  static photoUploaded(): void {
    this.success();
  }

  static voiceNoteRecorded(): void {
    this.medium();
  }

  static safetyReport(): void {
    this.heavy();
  }

  // Accessibility haptic feedback
  static accessibilityFocus(): void {
    this.light();
  }

  static accessibilityAction(): void {
    this.medium();
  }

  static accessibilityAlert(): void {
    this.warning();
  }
}

// Hook for haptic feedback with user preferences
export const useHaptics = () => {
  // In a real app, this would check user preferences for haptic feedback
  const isEnabled = true; // This would come from user settings

  return {
    light: () => isEnabled && HapticUtils.light(),
    medium: () => isEnabled && HapticUtils.medium(),
    heavy: () => isEnabled && HapticUtils.heavy(),
    success: () => isEnabled && HapticUtils.success(),
    warning: () => isEnabled && HapticUtils.warning(),
    error: () => isEnabled && HapticUtils.error(),
    selection: () => isEnabled && HapticUtils.selection(),
    
    // App-specific haptics
    buttonPress: () => isEnabled && HapticUtils.buttonPress(),
    cardTap: () => isEnabled && HapticUtils.cardTap(),
    swipeAction: () => isEnabled && HapticUtils.swipeAction(),
    connectionMade: () => isEnabled && HapticUtils.connectionMade(),
    eventJoined: () => isEnabled && HapticUtils.eventJoined(),
    vibeMatch: () => isEnabled && HapticUtils.vibeMatch(),
    messageReceived: () => isEnabled && HapticUtils.messageReceived(),
    messageSent: () => isEnabled && HapticUtils.messageSent(),
    navigationTransition: () => isEnabled && HapticUtils.navigationTransition(),
    modalOpen: () => isEnabled && HapticUtils.modalOpen(),
    modalClose: () => isEnabled && HapticUtils.modalClose(),
    formSubmit: () => isEnabled && HapticUtils.formSubmit(),
    formError: () => isEnabled && HapticUtils.formError(),
    deleteAction: () => isEnabled && HapticUtils.deleteAction(),
    confirmAction: () => isEnabled && HapticUtils.confirmAction(),
  };
};

export default HapticUtils;

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id               String   @id @default(uuid())
  email            String   @unique
  firstName        String
  lastName         String
  profilePicture   String?
  bio              String?
  university       String
  graduationYear   Int
  major            String
  interests        String[]
  googleId         String?  @unique
  isVerified       Boolean  @default(false)
  isActive         Boolean  @default(true)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relationships
  createdEvents     Event[]           @relation("EventCreator")
  eventParticipants EventParticipant[]
  groupMemberships  GroupMember[]
  messages          Message[]
  refreshTokens     RefreshToken[]
  sentReports       Report[]          @relation("Reporter")
  receivedReports   Report[]          @relation("ReportedUser")
  sentConnections   Connection[]      @relation("ConnectionFrom")
  receivedConnections Connection[]    @relation("ConnectionTo")
  directMessages    DirectMessage[]   @relation("DirectMessages")
  moderationActions ModerationAction[] @relation("ModerationActions")
  createdModerationActions ModerationAction[] @relation("CreatedModerationActions")

  @@map("users")
}

model RefreshToken {
  id        String   @id @default(uuid())
  token     String   @unique
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("refresh_tokens")
}

model Event {
  id                String              @id @default(cuid())
  title             String
  description       String
  location          String
  startTime         DateTime
  endTime           DateTime
  capacity          Int
  imageUrl          String?
  isActive          Boolean             @default(true)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  
  // Relations
  createdById       String
  createdBy         User                @relation("CreatedEvents", fields: [createdById], references: [id])
  participants      EventParticipant[]
  groups            Group[]
  reports           Report[]            @relation("EventReports")
  
  @@map("events")
}

model EventParticipant {
  id        String   @id @default(cuid())
  eventId   String
  userId    String
  joinedAt  DateTime @default(now())
  
  // Relations
  event     Event    @relation(fields: [eventId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@unique([eventId, userId])
  @@map("event_participants")
}

model Group {
  id             String        @id @default(cuid())
  eventId        String
  name           String
  description    String?
  maxMembers     Int           @default(8)
  currentMembers Int           @default(0)
  createdAt      DateTime      @default(now())
  
  // Relations
  event          Event         @relation(fields: [eventId], references: [id], onDelete: Cascade)
  members        GroupMember[]
  messages       Message[]
  
  @@map("groups")
}

model GroupMember {
  id       String   @id @default(cuid())
  groupId  String
  userId   String
  joinedAt DateTime @default(now())
  role     Role     @default(MEMBER)
  
  // Relations
  group    Group    @relation(fields: [groupId], references: [id], onDelete: Cascade)
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@unique([groupId, userId])
  @@map("group_members")
}

model Message {
  id          String      @id @default(cuid())
  groupId     String
  senderId    String
  content     String?
  messageType MessageType @default(TEXT)
  fileUrl     String?
  fileName    String?
  fileSize    Int?
  isEdited    Boolean     @default(false)
  editedAt    DateTime?
  createdAt   DateTime    @default(now())
  
  // Relations
  group       Group       @relation(fields: [groupId], references: [id], onDelete: Cascade)
  sender      User        @relation(fields: [senderId], references: [id], onDelete: Cascade)
  
  @@map("messages")
}

model Connection {
  id             String            @id @default(cuid())
  eventId        String
  fromUserId     String
  toUserId       String
  connectionType ConnectionType
  status         ConnectionStatus  @default(PENDING)
  createdAt      DateTime          @default(now())
  mutualAt       DateTime?
  
  // Relations
  event          Event             @relation(fields: [eventId], references: [id], onDelete: Cascade)
  fromUser       User              @relation("ConnectionFrom", fields: [fromUserId], references: [id], onDelete: Cascade)
  toUser         User              @relation("ConnectionTo", fields: [toUserId], references: [id], onDelete: Cascade)
  directMessages DirectMessage[]
  
  @@unique([eventId, fromUserId, toUserId])
  @@map("connections")
}

model DirectMessage {
  id           String      @id @default(cuid())
  connectionId String
  senderId     String
  content      String
  messageType  MessageType @default(TEXT)
  isRead       Boolean     @default(false)
  createdAt    DateTime    @default(now())
  
  // Relations
  connection   Connection  @relation(fields: [connectionId], references: [id], onDelete: Cascade)
  sender       User        @relation("DirectMessages", fields: [senderId], references: [id], onDelete: Cascade)
  
  @@map("direct_messages")
}

model Report {
  id             String       @id @default(cuid())
  reporterId     String
  reportedUserId String
  eventId        String?
  messageId      String?
  groupId        String?
  reason         ReportReason
  description    String
  status         ReportStatus @default(PENDING)
  priority       Priority     @default(LOW)
  createdAt      DateTime     @default(now())
  reviewedAt     DateTime?
  reviewedBy     String?
  resolution     String?
  
  // Relations
  reporter       User         @relation("Reporter", fields: [reporterId], references: [id], onDelete: Cascade)
  reportedUser   User         @relation("ReportedUser", fields: [reportedUserId], references: [id], onDelete: Cascade)
  event          Event?       @relation("EventReports", fields: [eventId], references: [id], onDelete: Cascade)
  
  @@map("reports")
}

model ModerationAction {
  id            String           @id @default(cuid())
  userId        String
  actionType    ModerationActionType
  reason        String
  duration      Int?             // in hours
  createdBy     String
  createdAt     DateTime         @default(now())
  expiresAt     DateTime?
  
  // Relations
  user          User             @relation("ModerationActions", fields: [userId], references: [id], onDelete: Cascade)
  createdByUser User             @relation("CreatedModerationActions", fields: [createdBy], references: [id])
  
  @@map("moderation_actions")
}

// Enums
enum EventParticipantStatus {
  JOINED
  LEFT
  BANNED
}

enum Role {
  MEMBER
  ADMIN
}

enum MessageType {
  TEXT
  IMAGE
  VOICE
  SYSTEM
}

enum ConnectionType {
  FRIEND
  SPARK
  PASS
}

enum ConnectionStatus {
  PENDING
  MUTUAL
  DECLINED
}

enum ReportReason {
  HARASSMENT
  INAPPROPRIATE_CONTENT
  SPAM
  FAKE_PROFILE
  SAFETY_CONCERN
  OTHER
}

enum ReportStatus {
  PENDING
  REVIEWED
  RESOLVED
  DISMISSED
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum ModerationActionType {
  WARNING
  TEMPORARY_BAN
  PERMANENT_BAN
  CONTENT_REMOVAL
}



import { apiClient } from './api';

export interface Location {
  id: string;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
}

export interface Event {
  id: string;
  title: string;
  description: string;
  eventType: 'CURATED' | 'COMMUNITY';
  category: string;
  location: Location;
  startTime: string;
  endTime: string;
  capacity: number;
  minGroupSize: number;
  maxGroupSize: number;
  createdBy: {
    id: string;
    firstName: string;
    lastName: string;
    profilePhotoUrl?: string;
  };
  status: 'ACTIVE' | 'COMPLETED' | 'CANCELLED' | 'DRAFT';
  imageUrl?: string;
  tags: string[];
  isOptedIn: boolean;
  currentAttendees: number;
  userGroup?: {
    id: string;
    groupNumber: number;
    status: string;
    chatRoomId: string;
    members: Array<{
      id: string;
      firstName: string;
      lastName: string;
      profilePhotoUrl?: string;
    }>;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateEventData {
  title: string;
  description: string;
  eventType: 'CURATED' | 'COMMUNITY';
  category: string;
  startTime: string;
  endTime: string;
  capacity: number;
  minGroupSize: number;
  maxGroupSize: number;
  tags?: string[];
  location: {
    name: string;
    address: string;
    latitude: number;
    longitude: number;
  };
}

export interface EventsResponse {
  events: Event[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface EventQueryParams {
  page?: number;
  limit?: number;
  category?: string;
  eventType?: 'CURATED' | 'COMMUNITY';
  status?: 'ACTIVE' | 'COMPLETED' | 'CANCELLED' | 'DRAFT';
  search?: string;
  startDate?: string;
  endDate?: string;
}

export const eventAPI = {
  async getEvents(params: EventQueryParams = {}): Promise<EventsResponse> {
    const queryString = new URLSearchParams(
      Object.entries(params).reduce((acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = value.toString();
        }
        return acc;
      }, {} as Record<string, string>)
    ).toString();

    const endpoint = `/events${queryString ? `?${queryString}` : ''}`;
    const response = await apiClient.get<EventsResponse>(endpoint);
    return response.data!;
  },

  async getEventById(id: string): Promise<Event> {
    const response = await apiClient.get<{ event: Event }>(`/events/${id}`);
    return response.data!.event;
  },

  async createEvent(data: CreateEventData): Promise<Event> {
    const response = await apiClient.post<{ event: Event }>('/events', data);
    return response.data!.event;
  },

  async updateEvent(id: string, data: Partial<CreateEventData>): Promise<Event> {
    const response = await apiClient.put<{ event: Event }>(`/events/${id}`, data);
    return response.data!.event;
  },

  async joinEvent(id: string): Promise<void> {
    await apiClient.post(`/events/${id}/join`);
  },

  async leaveEvent(id: string): Promise<void> {
    await apiClient.delete(`/events/${id}/leave`);
  },

  async uploadEventImage(imageUri: string): Promise<string> {
    const formData = new FormData();
    formData.append('image', {
      uri: imageUri,
      type: 'image/jpeg',
      name: 'event.jpg',
    } as any);
    formData.append('type', 'event');

    const response = await apiClient.postFormData<{ imageUrl: string }>('/upload/image', formData);
    return response.data!.imageUrl;
  },
};

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../store';
import { GradientBackground, Card, Button } from '../../components';
import { useTheme } from '../../contexts/ThemeContext';
import { createTypographyStyles } from '../../utils/styles';
import {
  fetchNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  Notification
} from '../../store/slices/notificationsSlice';

interface NotificationsScreenProps {
  navigation: any;
}



const NotificationsScreen: React.FC<NotificationsScreenProps> = ({ navigation }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { notifications, unreadCount, isLoading } = useSelector((state: RootState) => state.notifications);
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const typography = createTypographyStyles(theme);

  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    dispatch(fetchNotifications());
  }, [dispatch]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await dispatch(fetchNotifications());
    setRefreshing(false);
  };

  const markAsRead = (notificationId: string) => {
    dispatch(markNotificationAsRead(notificationId));
  };

  const markAllAsRead = () => {
    dispatch(markAllNotificationsAsRead());
  };

  const handleNotificationPress = (notification: Notification) => {
    markAsRead(notification.id);
    
    // Navigate based on notification type
    switch (notification.type) {
      case 'event':
        if (notification.actionData?.eventId) {
          // Would navigate to event detail
          console.log('Navigate to event:', notification.actionData.eventId);
        }
        break;
      case 'chat':
        if (notification.actionData?.chatId) {
          navigation.navigate('Chat', { chatRoomId: notification.actionData.chatId });
        }
        break;
      case 'connection':
        navigation.getParent()?.navigate('Profile', { screen: 'Connections' });
        break;
      case 'safety':
        navigation.getParent()?.navigate('Profile', { screen: 'WallOfShame' });
        break;
      default:
        break;
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'event': return 'calendar';
      case 'chat': return 'chatbubble';
      case 'connection': return 'people';
      case 'safety': return 'shield-checkmark';
      case 'system': return 'settings';
      default: return 'notifications';
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'event': return theme.colors.primary[500];
      case 'chat': return theme.colors.accent.blue;
      case 'connection': return theme.colors.accent.pink;
      case 'safety': return theme.colors.warning[500];
      case 'system': return theme.colors.text.secondary;
      default: return theme.colors.text.secondary;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };



  const renderNotification = (notification: Notification) => (
    <TouchableOpacity
      key={notification.id}
      onPress={() => handleNotificationPress(notification)}
      activeOpacity={0.7}
    >
      <Card
        variant="default"
        padding="medium"
        style={StyleSheet.flatten([
          styles.notificationCard,
          !notification.isRead && styles.unreadCard
        ])}
      >
        <View style={styles.notificationContent}>
          <View style={styles.notificationHeader}>
            <View style={[
              styles.iconContainer,
              { backgroundColor: `${getNotificationColor(notification.type)}20` }
            ]}>
              <Ionicons 
                name={getNotificationIcon(notification.type) as any}
                size={20} 
                color={getNotificationColor(notification.type)} 
              />
            </View>
            <View style={styles.notificationText}>
              <Text style={[
                styles.notificationTitle,
                !notification.isRead && styles.unreadTitle
              ]}>
                {notification.title}
              </Text>
              <Text style={styles.notificationMessage}>
                {notification.message}
              </Text>
            </View>
            <View style={styles.notificationMeta}>
              <Text style={styles.timestamp}>
                {formatTimestamp(notification.timestamp)}
              </Text>
              {!notification.isRead && <View style={styles.unreadDot} />}
            </View>
          </View>
        </View>
      </Card>
    </TouchableOpacity>
  );

  return (
    <GradientBackground variant="primary" style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color={theme.colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Notifications</Text>
          {unreadCount > 0 && (
            <TouchableOpacity onPress={markAllAsRead}>
              <Text style={styles.markAllRead}>Mark all read</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Notifications List */}
        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              tintColor={theme.colors.primary[500]}
            />
          }
        >
          {notifications.length > 0 ? (
            <View style={styles.notificationsList}>
              {notifications.map(renderNotification)}
            </View>
          ) : (
            <View style={styles.emptyState}>
              <Ionicons name="notifications-off" size={64} color={theme.colors.text.tertiary} />
              <Text style={styles.emptyTitle}>No Notifications</Text>
              <Text style={styles.emptySubtitle}>
                You're all caught up! New notifications will appear here.
              </Text>
            </View>
          )}
        </ScrollView>
      </SafeAreaView>
    </GradientBackground>
  );
};

const createStyles = (theme: any) => {
  const typography = createTypographyStyles(theme);
  
  return StyleSheet.create({
    container: {
      flex: 1,
    },
    safeArea: {
      flex: 1,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: theme.spacing[5],
      paddingVertical: theme.spacing[4],
      backgroundColor: theme.colors.background.primary,
    },
    backButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: theme.colors.background.secondary,
      justifyContent: 'center',
      alignItems: 'center',
    },
    headerTitle: {
      ...typography.h4,
      color: theme.colors.text.primary,
      fontWeight: theme.typography.fontWeight.bold,
    },
    markAllRead: {
      ...typography.body2,
      color: theme.colors.primary[500],
      fontWeight: theme.typography.fontWeight.semibold,
    },
    scrollView: {
      flex: 1,
    },
    notificationsList: {
      padding: theme.spacing[5],
      gap: theme.spacing[3],
    },
    notificationCard: {
      marginBottom: theme.spacing[2],
    },
    unreadCard: {
      borderLeftWidth: 3,
      borderLeftColor: theme.colors.primary[500],
    },
    notificationContent: {
      flex: 1,
    },
    notificationHeader: {
      flexDirection: 'row',
      alignItems: 'flex-start',
    },
    iconContainer: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: theme.spacing[3],
    },
    notificationText: {
      flex: 1,
    },
    notificationTitle: {
      ...typography.body1,
      color: theme.colors.text.primary,
      fontWeight: theme.typography.fontWeight.medium,
      marginBottom: theme.spacing[1],
    },
    unreadTitle: {
      fontWeight: theme.typography.fontWeight.bold,
    },
    notificationMessage: {
      ...typography.body2,
      color: theme.colors.text.secondary,
      lineHeight: 20,
    },
    notificationMeta: {
      alignItems: 'flex-end',
    },
    timestamp: {
      ...typography.caption,
      color: theme.colors.text.tertiary,
      marginBottom: theme.spacing[1],
    },
    unreadDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: theme.colors.primary[500],
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: theme.spacing[16],
    },
    emptyTitle: {
      ...typography.h5,
      color: theme.colors.text.primary,
      marginTop: theme.spacing[4],
      marginBottom: theme.spacing[2],
    },
    emptySubtitle: {
      ...typography.body1,
      color: theme.colors.text.secondary,
      textAlign: 'center',
      paddingHorizontal: theme.spacing[8],
    },
  });
};

export default NotificationsScreen;

import { authService } from '../../src/services/authService';
import { createTestUser, cleanupDatabase } from '../utils/testHelpers';
import { prisma } from '../setup';

describe('AuthService', () => {
  afterEach(async () => {
    await cleanupDatabase();
  });

  describe('generateTokens', () => {
    it('should generate access and refresh tokens', async () => {
      const user = await createTestUser();
      const tokens = await authService.generateTokens(user.id);

      expect(tokens.accessToken).toBeDefined();
      expect(tokens.refreshToken).toBeDefined();
      expect(typeof tokens.accessToken).toBe('string');
      expect(typeof tokens.refreshToken).toBe('string');

      // Verify refresh token is stored in database
      const storedToken = await prisma.refreshToken.findFirst({
        where: { userId: user.id }
      });
      expect(storedToken).toBeTruthy();
      expect(storedToken?.token).toBe(tokens.refreshToken);
    });
  });

  describe('refreshAccessToken', () => {
    it('should refresh access token with valid refresh token', async () => {
      const user = await createTestUser();
      const tokens = await authService.generateTokens(user.id);
      
      const newTokens = await authService.refreshAccessToken(tokens.refreshToken);
      
      expect(newTokens.accessToken).toBeDefined();
      expect(newTokens.accessToken).not.toBe(tokens.accessToken);
    });

    it('should throw error for invalid refresh token', async () => {
      await expect(authService.refreshAccessToken('invalid-token'))
        .rejects.toThrow('Invalid refresh token');
    });
  });

  describe('revokeRefreshToken', () => {
    it('should revoke refresh token', async () => {
      const user = await createTestUser();
      const tokens = await authService.generateTokens(user.id);
      
      await authService.revokeRefreshToken(tokens.refreshToken);
      
      const storedToken = await prisma.refreshToken.findFirst({
        where: { token: tokens.refreshToken }
      });
      expect(storedToken).toBeNull();
    });
  });
});
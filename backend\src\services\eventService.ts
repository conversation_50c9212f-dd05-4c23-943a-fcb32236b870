import { prisma } from '../config/database';
import { CreateEventData, EventQueryParams } from '../models/Event';
import { logger } from '../utils/logger';

export const eventService = {
  async getEvents(params: EventQueryParams) {
    const { page = 1, limit = 10, search, location, startDate, endDate } = params;
    const skip = (page - 1) * limit;

    const where: any = {
      isActive: true,
      endTime: { gte: new Date() }, // Only future events
    };

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (location) {
      where.location = { contains: location, mode: 'insensitive' };
    }

    if (startDate) {
      where.startTime = { gte: new Date(startDate) };
    }

    if (endDate) {
      where.endTime = { lte: new Date(endDate) };
    }

    const [events, total] = await Promise.all([
      prisma.event.findMany({
        where,
        skip,
        take: limit,
        orderBy: { startTime: 'asc' },
        include: {
          createdBy: {
            select: { id: true, firstName: true, lastName: true, profilePicture: true }
          },
          _count: { select: { participants: true } }
        }
      }),
      prisma.event.count({ where })
    ]);

    return {
      events: events.map(event => ({
        ...event,
        currentParticipants: event._count.participants
      })),
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    };
  },

  async getEventById(id: string) {
    return await prisma.event.findUnique({
      where: { id },
      include: {
        createdBy: {
          select: { id: true, firstName: true, lastName: true, profilePicture: true }
        },
        participants: {
          include: {
            user: {
              select: { id: true, firstName: true, lastName: true, profilePicture: true }
            }
          }
        },
        groups: {
          include: {
            members: {
              include: {
                user: {
                  select: { id: true, firstName: true, lastName: true }
                }
              }
            }
          }
        }
      }
    });
  },

  async createEvent(data: CreateEventData & { createdById: string }) {
    const event = await prisma.event.create({
      data: {
        title: data.title,
        description: data.description,
        location: data.location,
        startTime: new Date(data.startTime),
        endTime: new Date(data.endTime),
        capacity: data.capacity,
        imageUrl: data.imageUrl,
        createdById: data.createdById,
        isActive: true,
      },
      include: {
        createdBy: {
          select: { id: true, firstName: true, lastName: true, profilePicture: true }
        }
      }
    });

    logger.info(`Event created: ${event.id} by user ${data.createdById}`);
    return event;
  },

  async joinEvent(eventId: string, userId: string) {
    // Check if event exists and is active
    const event = await prisma.event.findUnique({
      where: { id: eventId },
      include: { _count: { select: { participants: true } } }
    });

    if (!event || !event.isActive) {
      throw new Error('Event not found');
    }

    if (event._count.participants >= event.capacity) {
      throw new Error('Event is full');
    }

    // Check if user already joined
    const existingParticipant = await prisma.eventParticipant.findUnique({
      where: {
        eventId_userId: { eventId, userId }
      }
    });

    if (existingParticipant) {
      throw new Error('Already joined');
    }

    // Add participant
    await prisma.eventParticipant.create({
      data: { eventId, userId }
    });

    logger.info(`User ${userId} joined event ${eventId}`);
    return { message: 'Successfully joined event', event };
  },

  async leaveEvent(eventId: string, userId: string) {
    const participant = await prisma.eventParticipant.findUnique({
      where: {
        eventId_userId: { eventId, userId }
      }
    });

    if (!participant) {
      throw new Error('Not a participant');
    }

    await prisma.eventParticipant.delete({
      where: {
        eventId_userId: { eventId, userId }
      }
    });

    logger.info(`User ${userId} left event ${eventId}`);
  }
};
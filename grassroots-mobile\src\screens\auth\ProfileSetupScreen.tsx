import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  Dimensions
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useDispatch, useSelector } from 'react-redux';
import * as ImagePicker from 'expo-image-picker';
import { Ionicons } from '@expo/vector-icons';
import { AppDispatch, RootState } from '../../store';
import {
  updateProfile,
  setProfileSetupStep,
  initializeProfile
} from '../../store/slices/userSlice';
import { Button, Input } from '../../components';
import GradientBackground from '../../components/GradientBackground';
import { useTheme } from '../../contexts/ThemeContext';
import { createTypographyStyles } from '../../utils/styles';

const { width } = Dimensions.get('window');

interface ProfileSetupScreenProps {
  navigation: any;
}

// Predefined interests
const INTERESTS = [
  'Coffee', 'Music', 'Sports', 'Art', 'Reading', 'Gaming', 'Cooking', 'Travel',
  'Photography', 'Fitness', 'Movies', 'Dancing', 'Hiking', 'Technology', 'Fashion',
  'Food', 'Nature', 'Yoga', 'Meditation', 'Volunteering', 'Learning', 'Socializing'
];

// Vibe prompts
const VIBE_PROMPTS = [
  'What makes you genuinely happy?',
  'Describe your perfect weekend',
  'What\'s a fun fact about you?',
  'What\'s your biggest passion?',
  'How do you like to spend your free time?'
];

const ProfileSetupScreen: React.FC<ProfileSetupScreenProps> = ({ navigation }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { profile, profileSetupStep, isLoading } = useSelector((state: RootState) => state.user);
  const { user } = useSelector((state: RootState) => state.auth);
  const { theme } = useTheme();
  const styles = createStyles(theme);

  // Local state
  const [bio, setBio] = useState('');
  const [selectedInterests, setSelectedInterests] = useState<string[]>([]);
  const [vibeAnswers, setVibeAnswers] = useState<string[]>(['', '', '']);
  const [profileImage, setProfileImage] = useState<string | null>(null);

  useEffect(() => {
    // Initialize profile if not exists
    if (!profile && user) {
      dispatch(initializeProfile({
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
      }));
    }
  }, [profile, user, dispatch]);

  const handleImagePicker = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission needed', 'We need camera roll permissions to select a photo.');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      setProfileImage(result.assets[0].uri);
    }
  };

  const handleInterestToggle = (interest: string) => {
    if (selectedInterests.includes(interest)) {
      setSelectedInterests(prev => prev.filter(i => i !== interest));
    } else if (selectedInterests.length < 8) {
      setSelectedInterests(prev => [...prev, interest]);
    } else {
      Alert.alert('Limit reached', 'You can select up to 8 interests.');
    }
  };

  const handleNext = () => {
    if (profileSetupStep < 3) {
      dispatch(setProfileSetupStep(profileSetupStep + 1));
    } else {
      handleComplete();
    }
  };

  const handleBack = () => {
    if (profileSetupStep > 0) {
      dispatch(setProfileSetupStep(profileSetupStep - 1));
    }
  };

  const handleComplete = async () => {
    try {
      // Update profile with all data including photo
      await dispatch(updateProfile({
        profilePhoto: profileImage || 'https://i.pravatar.cc/150?img=8',
        bio: bio || 'New VIBE user ready to connect!',
        interests: selectedInterests.length > 0 ? selectedInterests : ['socializing'],
        vibePrompts: VIBE_PROMPTS.slice(0, 3).map((question, index) => ({
          question,
          answer: vibeAnswers[index] || 'No answer provided'
        }))
      })).unwrap();

      console.log('Profile setup completed successfully');
    } catch (error) {
      console.error('Profile setup error:', error);
      Alert.alert('Error', 'Failed to complete profile setup. Please try again.');
    }
  };

  const handleSkipForNow = async () => {
    try {
      await dispatch(updateProfile({
        profilePhoto: 'https://i.pravatar.cc/150?img=8',
        bio: 'New VIBE user ready to connect!',
        interests: ['socializing'],
        vibePrompts: [
          { question: 'What makes you happy?', answer: 'Meeting new people' },
          { question: 'Perfect weekend?', answer: 'Exploring with friends' },
          { question: 'Fun fact about you?', answer: 'Always up for an adventure' }
        ]
      })).unwrap();
    } catch (error) {
      console.error('Skip setup error:', error);
    }
  };

  const renderProgressBar = () => (
    <View style={styles.progressContainer}>
      {[0, 1, 2, 3].map((step) => (
        <View
          key={step}
          style={[
            styles.progressDot,
            step <= profileSetupStep && styles.progressDotActive
          ]}
        />
      ))}
    </View>
  );

  const renderStepContent = () => {
    switch (profileSetupStep) {
      case 0:
        return renderWelcomeStep();
      case 1:
        return renderPhotoStep();
      case 2:
        return renderBioStep();
      case 3:
        return renderInterestsStep();
      default:
        return renderWelcomeStep();
    }
  };

  const renderWelcomeStep = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Welcome to VIBE! 🌟</Text>
      <Text style={styles.stepSubtitle}>
        Let's create your profile to help you connect with amazing people on campus.
      </Text>

      <View style={styles.featureList}>
        <View style={styles.featureItem}>
          <Ionicons name="camera" size={24} color={theme.colors.primary[500]} />
          <Text style={styles.featureText}>Add your best photo</Text>
        </View>
        <View style={styles.featureItem}>
          <Ionicons name="heart" size={24} color={theme.colors.accent.pink} />
          <Text style={styles.featureText}>Share your interests</Text>
        </View>
        <View style={styles.featureItem}>
          <Ionicons name="people" size={24} color={theme.colors.accent.blue} />
          <Text style={styles.featureText}>Connect with your vibe</Text>
        </View>
      </View>

      <Button
        title="Let's Get Started!"
        onPress={handleNext}
        variant="primary"
        size="large"
        fullWidth
        icon="arrow-forward"
        iconPosition="right"
      />

      <Button
        title="Skip for now"
        onPress={handleSkipForNow}
        variant="ghost"
        size="medium"
        style={styles.skipButton}
      />
    </View>
  );

  const renderPhotoStep = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Add Your Photo 📸</Text>
      <Text style={styles.stepSubtitle}>
        Show your personality! A good photo helps others connect with you.
      </Text>

      <TouchableOpacity style={styles.photoContainer} onPress={handleImagePicker}>
        {profileImage ? (
          <Image source={{ uri: profileImage }} style={styles.profileImage} />
        ) : (
          <View style={styles.photoPlaceholder}>
            <Ionicons name="camera" size={48} color={theme.colors.text.tertiary} />
            <Text style={styles.photoPlaceholderText}>Tap to add photo</Text>
          </View>
        )}
      </TouchableOpacity>

      <View style={styles.buttonContainer}>
        <Button
          title="Back"
          onPress={handleBack}
          variant="secondary"
          size="medium"
          style={styles.backButton}
        />
        <Button
          title="Continue"
          onPress={handleNext}
          variant="primary"
          size="medium"
          style={styles.continueButton}
        />
      </View>
    </View>
  );

  const renderBioStep = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Tell Us About Yourself ✨</Text>
      <Text style={styles.stepSubtitle}>
        Write a short bio that shows your personality and what you're looking for.
      </Text>

      <Input
        placeholder="I'm a friendly person who loves..."
        value={bio}
        onChangeText={setBio}
        multiline
        numberOfLines={4}
        maxLength={200}
        style={styles.bioInput}
      />

      <View style={styles.buttonContainer}>
        <Button
          title="Back"
          onPress={handleBack}
          variant="secondary"
          size="medium"
          style={styles.backButton}
        />
        <Button
          title="Continue"
          onPress={handleNext}
          variant="primary"
          size="medium"
          style={styles.continueButton}
          disabled={bio.trim().length < 10}
        />
      </View>
    </View>
  );

  const renderInterestsStep = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>What Are You Into? 🎯</Text>
      <Text style={styles.stepSubtitle}>
        Select up to 8 interests to help us match you with like-minded people.
      </Text>

      <Text style={styles.selectedCount}>
        {selectedInterests.length}/8 selected
      </Text>

      <ScrollView style={styles.interestsContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.interestsGrid}>
          {INTERESTS.map((interest) => (
            <TouchableOpacity
              key={interest}
              style={[
                styles.interestChip,
                selectedInterests.includes(interest) && styles.interestChipSelected
              ]}
              onPress={() => handleInterestToggle(interest)}
            >
              <Text
                style={[
                  styles.interestChipText,
                  selectedInterests.includes(interest) && styles.interestChipTextSelected
                ]}
              >
                {interest}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>

      <View style={styles.buttonContainer}>
        <Button
          title="Back"
          onPress={handleBack}
          variant="secondary"
          size="medium"
          style={styles.backButton}
        />
        <Button
          title="Complete Setup"
          onPress={handleComplete}
          variant="primary"
          size="medium"
          style={styles.continueButton}
          disabled={selectedInterests.length === 0}
          loading={isLoading}
        />
      </View>
    </View>
  );

  return (
    <GradientBackground variant="primary" style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.header}>
          {renderProgressBar()}
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {renderStepContent()}
        </ScrollView>
      </SafeAreaView>
    </GradientBackground>
  );
};

const createStyles = (theme: any) => {
  const typography = createTypographyStyles(theme);

  return StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    paddingHorizontal: theme.spacing[6],
    paddingVertical: theme.spacing[4],
  },
  content: {
    flex: 1,
    paddingHorizontal: theme.spacing[6],
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: theme.colors.neutral[300],
    marginHorizontal: theme.spacing[1],
  },
  progressDotActive: {
    backgroundColor: theme.colors.primary[500],
  },
  stepContainer: {
    flex: 1,
    paddingVertical: theme.spacing[6],
    paddingBottom: theme.spacing[10],
  },
  stepTitle: {
    ...typography.h3,
    color: theme.colors.text.primary,
    textAlign: 'center',
    marginBottom: theme.spacing[3],
  },
  stepSubtitle: {
    ...typography.body1,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    marginBottom: theme.spacing[8],
    lineHeight: 24,
  },
  featureList: {
    marginBottom: theme.spacing[10],
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing[4],
    paddingHorizontal: theme.spacing[4],
  },
  featureText: {
    ...typography.body1,
    color: theme.colors.text.primary,
    marginLeft: theme.spacing[4],
  },
  photoContainer: {
    alignSelf: 'center',
    marginBottom: theme.spacing[8],
  },
  profileImage: {
    width: 150,
    height: 150,
    borderRadius: 75,
  },
  photoPlaceholder: {
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: theme.colors.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: theme.colors.border.light,
    borderStyle: 'dashed',
  },
  photoPlaceholderText: {
    ...typography.body2,
    color: theme.colors.text.tertiary,
    marginTop: theme.spacing[2],
  },
  bioInput: {
    marginBottom: theme.spacing[6],
  },
  selectedCount: {
    ...typography.body2,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    marginBottom: theme.spacing[4],
  },
  interestsContainer: {
    flex: 1,
    marginBottom: theme.spacing[6],
  },
  interestsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  interestChip: {
    backgroundColor: theme.colors.background.secondary,
    borderRadius: theme.borderRadius.full,
    paddingVertical: theme.spacing[2],
    paddingHorizontal: theme.spacing[4],
    marginBottom: theme.spacing[2],
    borderWidth: 1,
    borderColor: theme.colors.border.light,
    minWidth: (width - theme.spacing[12] - theme.spacing[2]) / 3,
    alignItems: 'center',
  },
  interestChipSelected: {
    backgroundColor: theme.colors.primary[500],
    borderColor: theme.colors.primary[500],
  },
  interestChipText: {
    ...typography.body2,
    color: theme.colors.text.primary,
    fontWeight: '500' as any,
  },
  interestChipTextSelected: {
    color: theme.colors.text.inverse,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: theme.spacing[4],
    paddingBottom: theme.spacing[6],
    marginTop: 'auto',
  },
  backButton: {
    flex: 0.45,
  },
  continueButton: {
    flex: 0.45,
  },
  skipButton: {
    marginTop: theme.spacing[4],
  },
  });
};

export default ProfileSetupScreen;

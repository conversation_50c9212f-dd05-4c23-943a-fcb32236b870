import React, { useRef } from 'react';
import {
  View,
  StyleSheet,
  Animated,
  ViewStyle,
} from 'react-native';
import {
  PanGestureHandler,
  State,
  PanGestureHandlerGestureEvent,
} from 'react-native-gesture-handler';
import { useTheme } from '../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';

interface SwipeableCardProps {
  children: React.ReactNode;
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  leftAction?: {
    icon: keyof typeof Ionicons.glyphMap;
    color: string;
    label: string;
  };
  rightAction?: {
    icon: keyof typeof Ionicons.glyphMap;
    color: string;
    label: string;
  };
  swipeThreshold?: number;
  style?: ViewStyle;
  disabled?: boolean;
}

const SwipeableCard: React.FC<SwipeableCardProps> = ({
  children,
  onSwipeLeft,
  onSwipeRight,
  leftAction = {
    icon: 'heart',
    color: '#EC4899',
    label: 'Like',
  },
  rightAction = {
    icon: 'close',
    color: '#EF4444',
    label: 'Pass',
  },
  swipeThreshold = 120,
  style,
  disabled = false,
}) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  
  const translateX = useRef(new Animated.Value(0)).current;
  const opacity = useRef(new Animated.Value(1)).current;
  const scale = useRef(new Animated.Value(1)).current;

  const onGestureEvent = Animated.event(
    [{ nativeEvent: { translationX: translateX } }],
    { useNativeDriver: true }
  );

  const onHandlerStateChange = (event: PanGestureHandlerGestureEvent) => {
    if (disabled) return;

    const { state, translationX } = event.nativeEvent;

    if (state === State.END) {
      const shouldSwipeLeft = translationX > swipeThreshold;
      const shouldSwipeRight = translationX < -swipeThreshold;

      if (shouldSwipeLeft && onSwipeLeft) {
        // Animate out to the left
        Animated.parallel([
          Animated.timing(translateX, {
            toValue: 500,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(opacity, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(scale, {
            toValue: 0.8,
            duration: 300,
            useNativeDriver: true,
          }),
        ]).start(() => {
          onSwipeLeft();
          resetCard();
        });
      } else if (shouldSwipeRight && onSwipeRight) {
        // Animate out to the right
        Animated.parallel([
          Animated.timing(translateX, {
            toValue: -500,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(opacity, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(scale, {
            toValue: 0.8,
            duration: 300,
            useNativeDriver: true,
          }),
        ]).start(() => {
          onSwipeRight();
          resetCard();
        });
      } else {
        // Snap back to center
        Animated.spring(translateX, {
          toValue: 0,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }).start();
      }
    }
  };

  const resetCard = () => {
    translateX.setValue(0);
    opacity.setValue(1);
    scale.setValue(1);
  };

  const leftActionOpacity = translateX.interpolate({
    inputRange: [0, swipeThreshold],
    outputRange: [0, 1],
    extrapolate: 'clamp',
  });

  const rightActionOpacity = translateX.interpolate({
    inputRange: [-swipeThreshold, 0],
    outputRange: [1, 0],
    extrapolate: 'clamp',
  });

  const cardRotation = translateX.interpolate({
    inputRange: [-200, 0, 200],
    outputRange: ['-10deg', '0deg', '10deg'],
    extrapolate: 'clamp',
  });

  return (
    <View style={[styles.container, style]}>
      {/* Left Action (Like) */}
      <Animated.View
        style={[
          styles.actionContainer,
          styles.leftAction,
          { opacity: leftActionOpacity },
        ]}
      >
        <View style={[styles.actionIcon, { backgroundColor: leftAction.color }]}>
          <Ionicons
            name={leftAction.icon}
            size={32}
            color={theme.colors.text.inverse}
          />
        </View>
      </Animated.View>

      {/* Right Action (Pass) */}
      <Animated.View
        style={[
          styles.actionContainer,
          styles.rightAction,
          { opacity: rightActionOpacity },
        ]}
      >
        <View style={[styles.actionIcon, { backgroundColor: rightAction.color }]}>
          <Ionicons
            name={rightAction.icon}
            size={32}
            color={theme.colors.text.inverse}
          />
        </View>
      </Animated.View>

      {/* Swipeable Card */}
      <PanGestureHandler
        onGestureEvent={onGestureEvent}
        onHandlerStateChange={onHandlerStateChange}
        enabled={!disabled}
      >
        <Animated.View
          style={[
            styles.card,
            {
              opacity,
              transform: [
                { translateX },
                { scale },
                { rotate: cardRotation },
              ],
            },
          ]}
        >
          {children}
        </Animated.View>
      </PanGestureHandler>
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    position: 'relative',
    marginVertical: theme.spacing[2],
  },
  card: {
    backgroundColor: theme.colors.background.card,
    borderRadius: theme.borderRadius.lg,
    ...theme.shadows.md,
  },
  actionContainer: {
    position: 'absolute',
    top: '50%',
    zIndex: -1,
    justifyContent: 'center',
    alignItems: 'center',
    width: 80,
    height: 80,
  },
  leftAction: {
    left: theme.spacing[4],
    transform: [{ translateY: -40 }],
  },
  rightAction: {
    right: theme.spacing[4],
    transform: [{ translateY: -40 }],
  },
  actionIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    ...theme.shadows.lg,
  },
});

export default SwipeableCard;

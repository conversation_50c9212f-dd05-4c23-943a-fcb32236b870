import { StyleSheet, ViewStyle, TextStyle } from 'react-native';

// Common style utilities for the VIBE app
export const createCommonStyles = (theme: any) => StyleSheet.create({
  // Layout utilities
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },

  safeContainer: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  spaceBetween: {
    justifyContent: 'space-between',
  },
  
  // Padding utilities
  paddingHorizontal: {
    paddingHorizontal: theme.spacing[5],
  },
  
  paddingVertical: {
    paddingVertical: theme.spacing[5],
  },
  
  padding: {
    padding: theme.spacing[5],
  },
  
  // Margin utilities
  marginBottom: {
    marginBottom: theme.spacing[4],
  },
  
  marginTop: {
    marginTop: theme.spacing[4],
  },
  
  // Card styles
  card: {
    backgroundColor: theme.colors.background.card,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing[5],
    marginBottom: theme.spacing[4],
    ...theme.shadows.base,
  },
  
  cardElevated: {
    backgroundColor: theme.colors.background.card,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing[5],
    marginBottom: theme.spacing[4],
    ...theme.shadows.md,
  },
  
  // Button styles
  buttonPrimary: {
    backgroundColor: theme.colors.primary[500],
    borderRadius: theme.borderRadius.full,
    paddingVertical: theme.spacing[4],
    paddingHorizontal: theme.spacing[6],
    alignItems: 'center',
    justifyContent: 'center',
    ...theme.shadows.base,
  },
  
  buttonSecondary: {
    backgroundColor: theme.colors.background.primary,
    borderRadius: theme.borderRadius.full,
    paddingVertical: theme.spacing[4],
    paddingHorizontal: theme.spacing[6],
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: theme.colors.primary[500],
  },
  
  buttonGhost: {
    backgroundColor: 'transparent',
    borderRadius: theme.borderRadius.full,
    paddingVertical: theme.spacing[4],
    paddingHorizontal: theme.spacing[6],
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  // Text styles
  textPrimary: {
    color: theme.colors.text.primary,
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.normal,
  },
  
  textSecondary: {
    color: theme.colors.text.secondary,
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.normal,
  },
  
  textHeading: {
    color: theme.colors.text.primary,
    fontSize: theme.typography.fontSize['3xl'],
    fontWeight: theme.typography.fontWeight.bold,
  },
  
  textSubheading: {
    color: theme.colors.text.primary,
    fontSize: theme.typography.fontSize.xl,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  
  textCaption: {
    color: theme.colors.text.tertiary,
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.normal,
  },
  
  textLink: {
    color: theme.colors.text.link,
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.medium,
  },
  
  // Input styles
  input: {
    backgroundColor: theme.colors.background.secondary,
    borderRadius: theme.borderRadius.md,
    paddingVertical: theme.spacing[4],
    paddingHorizontal: theme.spacing[4],
    borderWidth: 1,
    borderColor: theme.colors.border.light,
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.text.primary,
  },
  
  inputFocused: {
    borderColor: theme.colors.primary[500],
    borderWidth: 2,
  },
  
  // Shadow utilities
  shadowSm: theme.shadows.sm,
  shadowBase: theme.shadows.base,
  shadowMd: theme.shadows.md,
  shadowLg: theme.shadows.lg,
  shadowXl: theme.shadows.xl,
});

// Typography styles
export const createTypographyStyles = (theme: any) => StyleSheet.create({
  // Headings
  h1: {
    fontSize: theme.typography.fontSize['6xl'],
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.text.primary,
    lineHeight: theme.typography.fontSize['6xl'] * theme.typography.lineHeight.tight,
  },
  
  h2: {
    fontSize: theme.typography.fontSize['5xl'],
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.text.primary,
    lineHeight: theme.typography.fontSize['5xl'] * theme.typography.lineHeight.tight,
  },
  
  h3: {
    fontSize: theme.typography.fontSize['4xl'],
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.text.primary,
    lineHeight: theme.typography.fontSize['4xl'] * theme.typography.lineHeight.tight,
  },
  
  h4: {
    fontSize: theme.typography.fontSize['3xl'],
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.text.primary,
    lineHeight: theme.typography.fontSize['3xl'] * theme.typography.lineHeight.normal,
  },
  
  h5: {
    fontSize: theme.typography.fontSize['2xl'],
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.text.primary,
    lineHeight: theme.typography.fontSize['2xl'] * theme.typography.lineHeight.normal,
  },
  
  h6: {
    fontSize: theme.typography.fontSize.xl,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.text.primary,
    lineHeight: theme.typography.fontSize.xl * theme.typography.lineHeight.normal,
  },
  
  // Body text
  body1: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.normal,
    color: theme.colors.text.primary,
    lineHeight: theme.typography.fontSize.base * theme.typography.lineHeight.relaxed,
  },
  
  body2: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.normal,
    color: theme.colors.text.secondary,
    lineHeight: theme.typography.fontSize.sm * theme.typography.lineHeight.relaxed,
  },
  
  // Special text styles
  caption: {
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.normal,
    color: theme.colors.text.tertiary,
    lineHeight: theme.typography.fontSize.xs * theme.typography.lineHeight.normal,
  },
  
  overline: {
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.text.secondary,
    textTransform: 'uppercase',
    letterSpacing: theme.typography.letterSpacing.wide,
  },
  
  button: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold,
    textAlign: 'center',
  },
  
  link: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.medium,
    color: theme.colors.text.link,
    textDecorationLine: 'underline',
  },
});

// Animation utilities
export const animations = {
  // Fade animations
  fadeIn: {
    from: { opacity: 0 },
    to: { opacity: 1 },
  },
  
  fadeOut: {
    from: { opacity: 1 },
    to: { opacity: 0 },
  },
  
  // Scale animations
  scaleIn: {
    from: { transform: [{ scale: 0.8 }], opacity: 0 },
    to: { transform: [{ scale: 1 }], opacity: 1 },
  },
  
  scaleOut: {
    from: { transform: [{ scale: 1 }], opacity: 1 },
    to: { transform: [{ scale: 0.8 }], opacity: 0 },
  },
  
  // Slide animations
  slideInUp: {
    from: { transform: [{ translateY: 50 }], opacity: 0 },
    to: { transform: [{ translateY: 0 }], opacity: 1 },
  },
  
  slideInDown: {
    from: { transform: [{ translateY: -50 }], opacity: 0 },
    to: { transform: [{ translateY: 0 }], opacity: 1 },
  },
  
  slideInLeft: {
    from: { transform: [{ translateX: -50 }], opacity: 0 },
    to: { transform: [{ translateX: 0 }], opacity: 1 },
  },
  
  slideInRight: {
    from: { transform: [{ translateX: 50 }], opacity: 0 },
    to: { transform: [{ translateX: 0 }], opacity: 1 },
  },
};

// Helper functions for creating dynamic styles
export const createButtonStyle = (theme: any, variant: 'primary' | 'secondary' | 'ghost' = 'primary'): ViewStyle => {
  const baseStyle = {
    borderRadius: theme.borderRadius.full,
    paddingVertical: theme.spacing[4],
    paddingHorizontal: theme.spacing[6],
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  };

  switch (variant) {
    case 'primary':
      return {
        ...baseStyle,
        backgroundColor: theme.colors.primary[500],
        ...theme.shadows.base,
      };
    case 'secondary':
      return {
        ...baseStyle,
        backgroundColor: theme.colors.background.primary,
        borderWidth: 2,
        borderColor: theme.colors.primary[500],
      };
    case 'ghost':
      return {
        ...baseStyle,
        backgroundColor: 'transparent',
      };
    default:
      return baseStyle;
  }
};

export const createTextStyle = (theme: any, variant: 'primary' | 'secondary' | 'inverse' = 'primary'): TextStyle => {
  const baseStyle = {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold as TextStyle['fontWeight'],
    textAlign: 'center' as const,
  };

  switch (variant) {
    case 'primary':
      return {
        ...baseStyle,
        color: theme.colors.text.inverse,
      };
    case 'secondary':
      return {
        ...baseStyle,
        color: theme.colors.primary[500],
      };
    case 'inverse':
      return {
        ...baseStyle,
        color: theme.colors.text.primary,
      };
    default:
      return baseStyle;
  }
};

// Export a backward-compatible typography function
export const typography = (theme: any) => createTypographyStyles(theme);

// Export a backward-compatible commonStyles function
export const commonStyles = (theme: any) => createCommonStyles(theme);

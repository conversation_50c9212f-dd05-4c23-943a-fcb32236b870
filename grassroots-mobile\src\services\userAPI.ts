import { apiClient } from './api';

export interface UserProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  universityDomain: string;
  profilePhotoUrl?: string;
  bio?: string;
  voiceNoteUrl?: string;
  dateOfBirth?: string;
  gender?: string;
  interests: string[];
  verificationStatus: string;
  isAdmin: boolean;
  adminRole?: string;
  lastActiveAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UpdateProfileData {
  firstName?: string;
  lastName?: string;
  bio?: string;
  dateOfBirth?: string;
  gender?: string;
  interests?: string[];
}

export interface UserStats {
  eventsJoined: number;
  connectionsCount: number;
  messagesCount: number;
}

export const userAPI = {
  async getProfile(): Promise<UserProfile> {
    const response = await apiClient.get<{ user: UserProfile }>('/users/profile');
    return response.data!.user;
  },

  async updateProfile(data: UpdateProfileData): Promise<UserProfile> {
    const response = await apiClient.put<{ user: UserProfile }>('/users/profile', data);
    return response.data!.user;
  },

  async uploadProfilePhoto(photoUri: string): Promise<string> {
    const formData = new FormData();
    formData.append('image', {
      uri: photoUri,
      type: 'image/jpeg',
      name: 'profile.jpg',
    } as any);
    formData.append('type', 'profile');

    const response = await apiClient.postFormData<{ imageUrl: string }>('/upload/image', formData);
    
    // Update user profile with new photo URL
    await this.updateProfile({ profilePhotoUrl: response.data!.imageUrl } as any);
    
    return response.data!.imageUrl;
  },

  async uploadVoiceNote(audioUri: string): Promise<string> {
    const formData = new FormData();
    formData.append('voice', {
      uri: audioUri,
      type: 'audio/m4a',
      name: 'voice-note.m4a',
    } as any);

    const response = await apiClient.postFormData<{ voiceUrl: string }>('/upload/voice', formData);
    
    // Update user profile with new voice note URL
    await this.updateProfile({ voiceNoteUrl: response.data!.voiceUrl } as any);
    
    return response.data!.voiceUrl;
  },

  async getUserStats(): Promise<UserStats> {
    const response = await apiClient.get<{ stats: UserStats }>('/users/stats');
    return response.data!.stats;
  },

  async deleteAccount(): Promise<void> {
    await apiClient.delete('/users/account');
  },
};

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  RefreshControl,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useDispatch, useSelector } from 'react-redux';
import { Ionicons } from '@expo/vector-icons';
import { AppDispatch, RootState } from '../../store';
import { fetchWallOfShame, WallOfShameEntry } from '../../store/slices/safetySlice';
import { Card, Button } from '../../components';
import { useTheme } from '../../contexts/ThemeContext';
import { createTypographyStyles } from '../../utils/styles';

interface WallOfShameScreenProps {
  navigation: any;
}

const WallOfShameScreen: React.FC<WallOfShameScreenProps> = ({ navigation }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { wallOfShame, isLoading } = useSelector((state: RootState) => state.safety);
  const { theme } = useTheme();
  const styles = createStyles(theme);

  const [showWarningModal, setShowWarningModal] = useState(true);

  useEffect(() => {
    dispatch(fetchWallOfShame());
  }, [dispatch]);

  const handleRefresh = () => {
    dispatch(fetchWallOfShame());
  };

  const getWarningLevelColor = (level: 'yellow' | 'orange' | 'red') => {
    switch (level) {
      case 'yellow': return theme.colors.secondary[500];
      case 'orange': return '#FF8C00';
      case 'red': return theme.colors.accent.pink;
    }
  };

  const getWarningLevelText = (level: 'yellow' | 'orange' | 'red') => {
    switch (level) {
      case 'yellow': return 'Caution';
      case 'orange': return 'Warning';
      case 'red': return 'Danger';
    }
  };

  const renderShameEntry = (entry: WallOfShameEntry) => (
    <Card key={entry.id} variant="elevated" padding="large" style={styles.shameCard}>
      <View style={styles.shameHeader}>
        <View style={styles.userInfo}>
          <Image source={{ uri: entry.userPhoto }} style={styles.userPhoto} />
          <View style={styles.userDetails}>
            <Text style={styles.userName}>{entry.userName}</Text>
            <Text style={styles.university}>{entry.university}</Text>
          </View>
        </View>
        <View style={[styles.warningBadge, { backgroundColor: getWarningLevelColor(entry.warningLevel) }]}>
          <Text style={styles.warningText}>{getWarningLevelText(entry.warningLevel)}</Text>
        </View>
      </View>

      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{entry.reportCount}</Text>
          <Text style={styles.statLabel}>Total Reports</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{entry.verifiedReports}</Text>
          <Text style={styles.statLabel}>Verified</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>
            {Math.floor((Date.now() - new Date(entry.lastIncident).getTime()) / (1000 * 60 * 60 * 24))}d
          </Text>
          <Text style={styles.statLabel}>Last Incident</Text>
        </View>
      </View>

      <View style={styles.incidentTypes}>
        <Text style={styles.incidentTypesTitle}>Reported for:</Text>
        <View style={styles.incidentChips}>
          {entry.incidentTypes.map((type, index) => (
            <View key={index} style={styles.incidentChip}>
              <Text style={styles.incidentChipText}>
                {type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </Text>
            </View>
          ))}
        </View>
      </View>

      <View style={styles.shameActions}>
        <Button
          title="Report This User"
          onPress={() => navigation.navigate('SafetyReport', {
            reportedUserId: entry.userId,
            reportedUserName: entry.userName,
            reportedUserPhoto: entry.userPhoto,
          })}
          variant="secondary"
          size="small"
          icon="flag"
          iconPosition="left"
        />
      </View>
    </Card>
  );

  const renderWarningModal = () => {
    if (!showWarningModal) return null;

    return (
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Ionicons name="warning" size={48} color={theme.colors.secondary[500]} />
            <Text style={styles.modalTitle}>Community Safety Warning</Text>
          </View>
          
          <Text style={styles.modalText}>
            This Wall of Shame displays users who have received multiple verified reports for inappropriate behavior. 
            This information is shared to help keep our community safe.
          </Text>
          
          <Text style={styles.modalSubtext}>
            • Yellow: Multiple reports, use caution{'\n'}
            • Orange: Verified inappropriate behavior{'\n'}
            • Red: Serious safety concerns, avoid interaction
          </Text>

          <View style={styles.modalActions}>
            <Button
              title="I Understand"
              onPress={() => setShowWarningModal(false)}
              variant="primary"
              size="large"
            />
          </View>
        </View>
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="shield-checkmark" size={64} color={theme.colors.primary[500]} />
      <Text style={styles.emptyTitle}>Community is Safe</Text>
      <Text style={styles.emptySubtitle}>
        Great news! There are currently no users with verified safety concerns in your area.
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Wall of Shame</Text>
        <TouchableOpacity onPress={() => Alert.alert(
          'About Wall of Shame',
          'This feature displays users who have received multiple verified reports for inappropriate behavior. It helps keep our community safe by providing transparency about user conduct.'
        )}>
          <Ionicons name="information-circle" size={24} color={theme.colors.text.secondary} />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={isLoading} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Safety Notice */}
        <Card variant="default" padding="large" style={styles.safetyNotice}>
          <View style={styles.noticeHeader}>
            <Ionicons name="shield" size={24} color={theme.colors.primary[500]} />
            <Text style={styles.noticeTitle}>Community Safety</Text>
          </View>
          <Text style={styles.noticeText}>
            Users listed here have received multiple verified reports. Exercise caution and report any inappropriate behavior.
          </Text>
        </Card>

        {wallOfShame.length === 0 ? (
          renderEmptyState()
        ) : (
          <View style={styles.shameList}>
            <Text style={styles.listTitle}>
              {wallOfShame.length} user{wallOfShame.length !== 1 ? 's' : ''} with safety concerns
            </Text>
            {wallOfShame.map(renderShameEntry)}
          </View>
        )}

        {/* Report Button */}
        <Card variant="flat" padding="large" style={styles.reportCard}>
          <Text style={styles.reportTitle}>Experienced inappropriate behavior?</Text>
          <Text style={styles.reportSubtitle}>
            Help keep our community safe by reporting users who violate our guidelines.
          </Text>
          <Button
            title="Submit a Report"
            onPress={() => navigation.navigate('SafetyReport', {
              reportedUserId: '',
              reportedUserName: '',
              reportedUserPhoto: '',
            })}
            variant="primary"
            size="medium"
            icon="flag"
            iconPosition="left"
            style={styles.reportButton}
          />
        </Card>
      </ScrollView>

      {renderWarningModal()}
    </SafeAreaView>
  );
};

const createStyles = (theme: any) => {
  const typography = createTypographyStyles(theme);

  return StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.secondary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: theme.spacing[5],
    paddingVertical: theme.spacing[4],
    backgroundColor: theme.colors.background.primary,
    ...theme.shadows.sm,
  },
  headerTitle: {
    ...typography.h5,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.bold,
  },
  content: {
    flex: 1,
    paddingHorizontal: theme.spacing[5],
    paddingTop: theme.spacing[4],
  },
  safetyNotice: {
    marginBottom: theme.spacing[4],
    backgroundColor: theme.colors.secondary[50],
    borderColor: theme.colors.secondary[200],
    borderWidth: 1,
  },
  noticeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing[2],
  },
  noticeTitle: {
    ...typography.h6,
    color: theme.colors.secondary[700],
    fontWeight: theme.typography.fontWeight.bold,
    marginLeft: theme.spacing[2],
  },
  noticeText: {
    ...typography.body2,
    color: theme.colors.secondary[600],
    lineHeight: 20,
  },
  listTitle: {
    ...typography.h6,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.bold,
    marginBottom: theme.spacing[4],
  },
  shameList: {
    marginBottom: theme.spacing[6],
  },
  shameCard: {
    marginBottom: theme.spacing[4],
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.accent.pink,
  },
  shameHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing[4],
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  userPhoto: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: theme.spacing[3],
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    ...typography.h6,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.bold,
  },
  university: {
    ...typography.body2,
    color: theme.colors.text.secondary,
    marginTop: theme.spacing[1],
  },
  warningBadge: {
    paddingVertical: theme.spacing[1],
    paddingHorizontal: theme.spacing[3],
    borderRadius: theme.borderRadius.full,
  },
  warningText: {
    ...typography.caption,
    color: theme.colors.text.inverse,
    fontWeight: theme.typography.fontWeight.bold,
    fontSize: 12,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: theme.spacing[4],
    paddingVertical: theme.spacing[3],
    backgroundColor: theme.colors.background.secondary,
    borderRadius: theme.borderRadius.md,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    ...typography.h5,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.bold,
  },
  statLabel: {
    ...typography.caption,
    color: theme.colors.text.secondary,
    marginTop: theme.spacing[1],
  },
  incidentTypes: {
    marginBottom: theme.spacing[4],
  },
  incidentTypesTitle: {
    ...typography.body2,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.semibold,
    marginBottom: theme.spacing[2],
  },
  incidentChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  incidentChip: {
    backgroundColor: theme.colors.accent.pink + '20',
    borderColor: theme.colors.accent.pink,
    borderWidth: 1,
    borderRadius: theme.borderRadius.full,
    paddingVertical: theme.spacing[1],
    paddingHorizontal: theme.spacing[3],
    marginRight: theme.spacing[2],
    marginBottom: theme.spacing[2],
  },
  incidentChipText: {
    ...typography.caption,
    color: theme.colors.accent.pink,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  shameActions: {
    alignItems: 'flex-end',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: theme.spacing[16],
  },
  emptyTitle: {
    ...typography.h5,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.bold,
    marginTop: theme.spacing[4],
    marginBottom: theme.spacing[2],
  },
  emptySubtitle: {
    ...typography.body1,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: theme.spacing[8],
  },
  reportCard: {
    marginBottom: theme.spacing[6],
    backgroundColor: theme.colors.background.tertiary,
    alignItems: 'center',
  },
  reportTitle: {
    ...typography.h6,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.bold,
    marginBottom: theme.spacing[2],
    textAlign: 'center',
  },
  reportSubtitle: {
    ...typography.body2,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: theme.spacing[4],
  },
  reportButton: {
    paddingHorizontal: theme.spacing[8],
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalContent: {
    backgroundColor: theme.colors.background.primary,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing[6],
    marginHorizontal: theme.spacing[5],
    maxWidth: 400,
    width: '100%',
  },
  modalHeader: {
    alignItems: 'center',
    marginBottom: theme.spacing[4],
  },
  modalTitle: {
    ...typography.h5,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.bold,
    marginTop: theme.spacing[3],
    textAlign: 'center',
  },
  modalText: {
    ...typography.body1,
    color: theme.colors.text.primary,
    lineHeight: 24,
    marginBottom: theme.spacing[4],
    textAlign: 'center',
  },
  modalSubtext: {
    ...typography.body2,
    color: theme.colors.text.secondary,
    lineHeight: 22,
    marginBottom: theme.spacing[6],
  },
  modalActions: {
    alignItems: 'center',
  },
  });
};

export default WallOfShameScreen;

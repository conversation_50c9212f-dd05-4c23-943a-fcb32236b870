export interface Event {
  id: string;
  title: string;
  description: string;
  location: string;
  startTime: Date;
  endTime: Date;
  capacity: number;
  currentParticipants: number;
  imageUrl?: string;
  createdById: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface EventParticipant {
  id: string;
  eventId: string;
  userId: string;
  joinedAt: Date;
}

export interface CreateEventData {
  title: string;
  description: string;
  location: string;
  startTime: string;
  endTime: string;
  capacity: number;
  imageUrl?: string;
}

export interface EventQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  location?: string;
  startDate?: string;
  endDate?: string;
}
import { Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

// Design System Configuration
export const designTokens = {
  // Animation durations
  animation: {
    fast: 150,
    normal: 250,
    slow: 350,
    slower: 500,
  },

  // Elevation levels
  elevation: {
    none: 0,
    low: 2,
    medium: 4,
    high: 8,
    highest: 16,
  },

  // Opacity levels
  opacity: {
    disabled: 0.4,
    secondary: 0.6,
    primary: 0.8,
    full: 1.0,
  },

  // Screen dimensions
  screen: {
    width,
    height,
    isSmall: width < 375,
    isMedium: width >= 375 && width < 414,
    isLarge: width >= 414,
  },
};

// VIBE Design System - Light Theme
const lightTheme = {
  // Brand Colors - Modern minimalistic palette
  colors: {
    // Primary Brand Colors - Modern indigo (consistent with dark theme)
    primary: {
      50: '#EEF2FF',
      100: '#E0E7FF',
      200: '#C7D2FE',
      300: '#A5B4FC',
      400: '#818CF8',
      500: '#6366F1', // Modern indigo - consistent with dark theme
      600: '#4F46E5',
      700: '#4338CA',
      800: '#3730A3',
      900: '#312E81',
    },

    // Secondary Colors - Complementary cyan/teal (consistent with dark theme)
    secondary: {
      50: '#ECFEFF',
      100: '#CFFAFE',
      200: '#A5F3FC',
      300: '#67E8F9',
      400: '#22D3EE',
      500: '#06B6D4', // Modern cyan
      600: '#0891B2',
      700: '#0E7490',
      800: '#155E75',
      900: '#164E63',
    },
    
    // Accent Colors - Modern and vibrant palette (consistent with dark theme)
    accent: {
      purple: '#8B5CF6',   // Violet
      pink: '#EC4899',     // Pink
      blue: '#06B6D4',     // Cyan
      green: '#10B981',    // Emerald
      orange: '#F59E0B',   // Amber
      red: '#EF4444',      // Red
      yellow: '#EAB308',   // Yellow
      teal: '#14B8A6',     // Teal
    },

    // Neutral Colors - Clean slate grays (consistent with dark theme)
    neutral: {
      50: '#F8FAFC',
      100: '#F1F5F9',
      200: '#E2E8F0',
      300: '#CBD5E1',
      400: '#94A3B8',
      500: '#64748B',
      600: '#475569',
      700: '#334155',
      800: '#1E293B',
      900: '#0F172A',
    },

    // Semantic Colors - Modern and consistent
    success: '#10B981',  // Emerald
    warning: '#F59E0B',  // Amber
    error: '#EF4444',    // Red
    info: '#06B6D4',     // Cyan
    
    // Background Colors - Clean and minimal
    background: {
      primary: '#FFFFFF',
      secondary: '#FAFAFA',
      tertiary: '#F4F4F5',
      card: '#FFFFFF',
      overlay: 'rgba(0, 0, 0, 0.4)',

      // Gradient backgrounds for light theme
      gradient: {
        primary: ['#FFFFFF', '#F8F9FA'],
        secondary: ['#F8F9FA', '#E9ECEF'],
        accent: ['#16A34A', '#22C55E'],
        card: ['#FFFFFF', '#F8F9FA'],
      },
    },

    // Text Colors - Modern and readable (consistent with dark theme)
    text: {
      primary: '#0F172A',        // Dark slate
      secondary: '#334155',      // Medium slate
      tertiary: '#64748B',       // Light slate
      inverse: '#F8FAFC',        // Almost white (for dark backgrounds)
      accent: '#6366F1',         // Primary indigo
      muted: '#94A3B8',          // Muted slate
      link: '#8B5CF6',           // Purple for links
      placeholder: '#94A3B8',    // Placeholder text
    },

    // Border Colors - Modern and subtle
    border: {
      light: '#E2E8F0',
      medium: '#CBD5E1',
      dark: '#94A3B8',
    },
  },

  // Border Radius System
  borderRadius: {
    none: 0,
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    '2xl': 20,
    '3xl': 24,
    full: 9999,
  },

  // Shadow System - Modern and subtle
  shadows: {
    sm: {
      shadowColor: '#0F172A',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 3,
      elevation: 1,
    },
    base: {
      shadowColor: '#0F172A',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 6,
      elevation: 3,
    },
    lg: {
      shadowColor: '#0F172A',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 12,
      elevation: 6,
    },
    xl: {
      shadowColor: '#0F172A',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.2,
      shadowRadius: 20,
      elevation: 10,
    },
    colored: {
      shadowColor: '#6366F1',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 4,
    },
  },
};

// VIBE Design System - Modern Dark Theme
const basedarkTheme = {
  colors: {
    // Primary Brand Colors - Modern indigo system
    primary: {
      50: '#EEF2FF',
      100: '#E0E7FF',
      200: '#C7D2FE',
      300: '#A5B4FC',
      400: '#818CF8',
      500: '#6366F1', // Main indigo - modern and appealing
      600: '#4F46E5',
      700: '#4338CA',
      800: '#3730A3',
      900: '#312E81',
    },

    // Secondary Colors - Complementary cyan/teal
    secondary: {
      50: '#ECFEFF',
      100: '#CFFAFE',
      200: '#A5F3FC',
      300: '#67E8F9',
      400: '#22D3EE',
      500: '#06B6D4', // Main cyan - fresh and modern
      600: '#0891B2',
      700: '#0E7490',
      800: '#155E75',
      900: '#164E63',
    },

    // Accent Colors - Vibrant and modern palette
    accent: {
      purple: '#8B5CF6',   // Violet
      pink: '#EC4899',     // Pink
      blue: '#06B6D4',     // Cyan
      green: '#10B981',    // Emerald
      orange: '#F59E0B',   // Amber
      red: '#EF4444',      // Red
      yellow: '#EAB308',   // Yellow
      teal: '#14B8A6',     // Teal
    },

    // Neutral Colors - Cool slate grays for modern feel
    neutral: {
      50: '#F8FAFC',   // Lightest
      100: '#F1F5F9',
      200: '#E2E8F0',
      300: '#CBD5E1',
      400: '#94A3B8',
      500: '#64748B',
      600: '#475569',
      700: '#334155',
      800: '#1E293B',
      900: '#0F172A',  // Darkest
    },

    // Semantic Colors - High contrast for dark mode
    success: '#10B981',  // Emerald
    warning: '#F59E0B',  // Amber
    error: '#EF4444',    // Red
    info: '#06B6D4',     // Cyan

    // Modern Dark Backgrounds - Rich and sophisticated
    background: {
      primary: '#0F172A',        // Rich dark slate
      secondary: '#1E293B',      // Lighter slate
      tertiary: '#334155',       // Medium slate
      card: '#1E293B',          // Card background
      overlay: 'rgba(15, 23, 42, 0.95)',

      // Beautiful gradient backgrounds
      gradient: {
        primary: ['#0F172A', '#1E293B', '#334155'],     // Slate progression
        secondary: ['#1E293B', '#334155', '#475569'],   // Lighter slate progression
        accent: ['#6366F1', '#8B5CF6', '#EC4899'],      // Indigo to purple to pink
        card: ['#1E293B', '#334155'],                   // Subtle card gradient
        hero: ['#0F172A', '#1E293B', '#312E81'],        // Hero section gradient
        feature: ['#312E81', '#4338CA', '#6366F1'],     // Feature highlight gradient
      },
    },

    // Text Colors - High contrast and readable
    text: {
      primary: '#F8FAFC',        // Almost white
      secondary: '#CBD5E1',      // Light slate
      tertiary: '#94A3B8',       // Medium slate
      inverse: '#0F172A',        // Dark slate (for light backgrounds)
      accent: '#6366F1',         // Primary indigo
      muted: '#64748B',          // Muted slate
      link: '#8B5CF6',           // Purple for links
      placeholder: '#64748B',    // Placeholder text
    },

    // Border Colors - Subtle and elegant
    border: {
      light: '#334155',
      medium: '#475569',
      dark: '#64748B',
    },
  },

  // Border Radius System
  borderRadius: {
    none: 0,
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    '2xl': 20,
    '3xl': 24,
    full: 9999,
  },

  // Shadow System - Enhanced for dark mode with colored shadows
  shadows: {
    sm: {
      shadowColor: '#6366F1',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 3,
      elevation: 2,
    },
    base: {
      shadowColor: '#6366F1',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.15,
      shadowRadius: 6,
      elevation: 4,
    },
    lg: {
      shadowColor: '#6366F1',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.2,
      shadowRadius: 12,
      elevation: 8,
    },
    xl: {
      shadowColor: '#6366F1',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.25,
      shadowRadius: 20,
      elevation: 12,
    },
    glow: {
      shadowColor: '#8B5CF6',
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.3,
      shadowRadius: 10,
      elevation: 6,
    },
  },
  
  // Typography System
  typography: {
    // Font Families
    fonts: {
      primary: 'System', // iOS: San Francisco, Android: Roboto
      secondary: 'System',
      mono: 'Courier New',
    },
    
    // Font Sizes
    fontSize: {
      xs: 12,
      sm: 14,
      base: 16,
      lg: 18,
      xl: 20,
      '2xl': 24,
      '3xl': 28,
      '4xl': 32,
      '5xl': 36,
      '6xl': 42,
    },
    
    // Font Weights
    fontWeight: {
      light: '300' as const,
      normal: '400' as const,
      medium: '500' as const,
      semibold: '600' as const,
      bold: '700' as const,
      extrabold: '800' as const,
    },
    
    // Line Heights
    lineHeight: {
      tight: 1.2,
      normal: 1.4,
      relaxed: 1.6,
      loose: 1.8,
    },
    
    // Letter Spacing
    letterSpacing: {
      tight: -0.5,
      normal: 0,
      wide: 0.5,
      wider: 1,
    },
  },
  
  // Spacing System (based on 4px grid)
  spacing: {
    0: 0,
    1: 4,
    2: 8,
    3: 12,
    4: 16,
    5: 20,
    6: 24,
    7: 28,
    8: 32,
    9: 36,
    10: 40,
    12: 48,
    16: 64,
    20: 80,
    24: 96,
    32: 128,
  },
  
  // Layout
  layout: {
    screen: {
      width,
      height,
    },
    container: {
      maxWidth: width - 40,
      paddingHorizontal: 20,
    },
    card: {
      borderRadius: 16,
      padding: 20,
    },
  },
  
  // Animation System - Modern and smooth
  animation: {
    duration: {
      fast: 150,
      normal: 300,
      slow: 500,
      slower: 800,
    },
    easing: {
      ease: 'ease',
      easeIn: 'ease-in',
      easeOut: 'ease-out',
      easeInOut: 'ease-in-out',
      spring: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    },
    scale: {
      enter: 0.95,
      exit: 1.05,
    },
  },

  // Modern Transitions
  transitions: {
    all: 'all 300ms ease-in-out',
    colors: 'color 200ms ease-in-out, background-color 200ms ease-in-out, border-color 200ms ease-in-out',
    transform: 'transform 300ms cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    opacity: 'opacity 200ms ease-in-out',
  },
  
  // Z-Index Scale
  zIndex: {
    hide: -1,
    auto: 'auto',
    base: 0,
    docked: 10,
    dropdown: 1000,
    sticky: 1100,
    banner: 1200,
    overlay: 1300,
    modal: 1400,
    popover: 1500,
    skipLink: 1600,
    toast: 1700,
    tooltip: 1800,
  },
};



// Theme Selection Logic
const getTheme = (isDark: boolean = false) => {
  const baseTheme = isDark ? basedarkTheme : lightTheme;

  return {
    ...baseTheme,
    ...designTokens,
    isDark,

    // Enhanced colors with gradients
    colors: {
      ...baseTheme.colors,
      gradients: {
        primary: ['#6366F1', '#8B5CF6'],
        secondary: ['#06B6D4', '#14B8A6'],
        accent: ['#8B5CF6', '#EC4899'],
        background: isDark ? ['#0F172A', '#1E293B'] : ['#F8FAFC', '#F1F5F9'],
        card: isDark ? ['#1E293B', '#334155'] : ['#FFFFFF', '#F8FAFC'],
        hero: isDark ? ['#0F172A', '#312E81'] : ['#6366F1', '#8B5CF6'],
        feature: isDark ? ['#312E81', '#6366F1'] : ['#06B6D4', '#14B8A6'],
        glow: ['#6366F1', '#8B5CF6', '#EC4899'],
        rainbow: ['#6366F1', '#8B5CF6', '#EC4899', '#F59E0B', '#10B981'],
        sunset: ['#F59E0B', '#EC4899', '#8B5CF6'],
        ocean: ['#06B6D4', '#14B8A6', '#10B981'],
        fire: ['#EF4444', '#F59E0B', '#EAB308'],
      },
    },

    // Typography System (shared between themes)
    typography: {
      fontFamily: {
        regular: 'System',
        medium: 'System',
        semibold: 'System',
        bold: 'System',
      },
      fontSize: {
        xs: 12,
        sm: 14,
        base: 16,
        lg: 18,
        xl: 20,
        '2xl': 24,
        '3xl': 30,
        '4xl': 36,
        '5xl': 48,
      },
      fontWeight: {
        light: '300',
        normal: '400',
        medium: '500',
        semibold: '600',
        bold: '700',
        extrabold: '800',
      },
      lineHeight: {
        tight: 1.2,
        normal: 1.4,
        relaxed: 1.6,
        loose: 1.8,
      },
      letterSpacing: {
        tight: -0.5,
        normal: 0,
        wide: 0.5,
        wider: 1,
      },
    },

    // Spacing System
    spacing: {
      0: 0,
      1: 4,
      2: 8,
      3: 12,
      4: 16,
      5: 20,
      6: 24,
      8: 32,
      10: 40,
      12: 48,
      16: 64,
      20: 80,
      24: 96,
    },

    // Border Radius
    borderRadius: {
      none: 0,
      sm: 4,
      md: 8,
      lg: 12,
      xl: 16,
      '2xl': 20,
      '3xl': 24,
      full: 9999,
    },

    // Shadows
    shadows: {
      none: {
        shadowColor: 'transparent',
        shadowOffset: { width: 0, height: 0 },
        shadowOpacity: 0,
        shadowRadius: 0,
        elevation: 0,
      },
      sm: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 2,
      },
      base: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 4,
      },
      md: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 8,
        elevation: 8,
      },
      lg: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.2,
        shadowRadius: 16,
        elevation: 16,
      },
    },
  };
};

// Default theme (dark mode - beautiful purple/pink theme)
export const theme = getTheme(true);

// Light theme
export const lightThemeExport = getTheme(false);

// Dark theme (same as default)
export const darkTheme = getTheme(true);

// Gradient Utilities for Beautiful UI
export const gradients = {
  // Primary gradients
  primary: ['#6366F1', '#8B5CF6'],
  secondary: ['#06B6D4', '#14B8A6'],
  accent: ['#8B5CF6', '#EC4899'],

  // Dark theme specific gradients
  dark: {
    background: ['#0F172A', '#1E293B'],
    card: ['#1E293B', '#334155'],
    hero: ['#0F172A', '#312E81'],
    feature: ['#312E81', '#6366F1'],
    glow: ['#6366F1', '#8B5CF6', '#EC4899'],
  },

  // Light theme specific gradients
  light: {
    background: ['#F8FAFC', '#F1F5F9'],
    card: ['#FFFFFF', '#F8FAFC'],
    hero: ['#6366F1', '#8B5CF6'],
    feature: ['#06B6D4', '#14B8A6'],
    subtle: ['#F1F5F9', '#E2E8F0'],
  },

  // Special effect gradients
  rainbow: ['#6366F1', '#8B5CF6', '#EC4899', '#F59E0B', '#10B981'],
  sunset: ['#F59E0B', '#EC4899', '#8B5CF6'],
  ocean: ['#06B6D4', '#14B8A6', '#10B981'],
  fire: ['#EF4444', '#F59E0B', '#EAB308'],
};

// Theme hook for dynamic switching (removed to avoid conflict with ThemeContext)
// Use useTheme from ThemeContext instead

export default theme;

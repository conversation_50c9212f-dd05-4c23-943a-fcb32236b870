import { groupService } from '../../src/services/groupService';
import { createTestUser, createTestEvent, createTestGroup, addUserToEvent, cleanupDatabase } from '../utils/testHelpers';

describe('GroupService', () => {
  afterEach(async () => {
    await cleanupDatabase();
  });

  describe('createGroupsForEvent', () => {
    it('should create balanced groups for event participants', async () => {
      const creator = await createTestUser();
      const event = await createTestEvent(creator.id, { capacity: 16 });
      
      // Add 16 participants
      const participants = [];
      for (let i = 0; i < 16; i++) {
        const user = await createTestUser({ firstName: `User${i}` });
        await addUserToEvent(user.id, event.id);
        participants.push(user);
      }

      const groups = await groupService.createGroupsForEvent(event.id);

      expect(groups).toHaveLength(2); // 16 participants / 8 max per group = 2 groups
      expect(groups[0].maxMembers).toBe(8);
      expect(groups[1].maxMembers).toBe(8);
    });

    it('should handle odd number of participants', async () => {
      const creator = await createTestUser();
      const event = await createTestEvent(creator.id, { capacity: 10 });
      
      // Add 10 participants
      for (let i = 0; i < 10; i++) {
        const user = await createTestUser({ firstName: `User${i}` });
        await addUserToEvent(user.id, event.id);
      }

      const groups = await groupService.createGroupsForEvent(event.id);

      expect(groups).toHaveLength(2);
      // Should distribute evenly: 5 and 5
      const totalCapacity = groups.reduce((sum, group) => sum + group.maxMembers, 0);
      expect(totalCapacity).toBe(10);
    });
  });

  describe('sendMessage', () => {
    it('should send message to group', async () => {
      const user = await createTestUser();
      const event = await createTestEvent(user.id);
      const group = await createTestGroup(event.id);
      
      const message = await groupService.sendMessage(group.id, user.id, {
        content: 'Hello group!',
        messageType: 'TEXT'
      });

      expect(message.content).toBe('Hello group!');
      expect(message.senderId).toBe(user.id);
      expect(message.groupId).toBe(group.id);
    });

    it('should throw error for non-member sending message', async () => {
      const user = await createTestUser();
      const nonMember = await createTestUser();
      const event = await createTestEvent(user.id);
      const group = await createTestGroup(event.id);

      await expect(groupService.sendMessage(group.id, nonMember.id, {
        content: 'Hello group!',
        messageType: 'TEXT'
      })).rejects.toThrow('User is not a member of this group');
    });
  });

  describe('getGroupMessages', () => {
    it('should return paginated group messages', async () => {
      const user = await createTestUser();
      const event = await createTestEvent(user.id);
      const group = await createTestGroup(event.id);
      
      // Send multiple messages
      for (let i = 0; i < 5; i++) {
        await groupService.sendMessage(group.id, user.id, {
          content: `Message ${i}`,
          messageType: 'TEXT'
        });
      }

      const result = await groupService.getGroupMessages(group.id, 1, 3);

      expect(result.messages).toHaveLength(3);
      expect(result.pagination.total).toBe(5);
      expect(result.pagination.totalPages).toBe(2);
    });
  });
});
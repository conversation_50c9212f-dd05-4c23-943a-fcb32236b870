import React, { useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  Animated,
  ViewStyle,
  ActivityIndicator,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import Typography from './Typography';

interface LoadingProps {
  type?: 'spinner' | 'skeleton' | 'pulse';
  size?: 'small' | 'medium' | 'large';
  text?: string;
  style?: ViewStyle;
}

interface SkeletonProps {
  width?: number | string;
  height?: number | string;
  borderRadius?: number;
  style?: ViewStyle;
}

// Skeleton component for content loading
export const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = 20,
  borderRadius,
  style,
}) => {
  const { theme } = useTheme();
  const pulseAnim = useRef(new Animated.Value(0.3)).current;
  const finalBorderRadius = borderRadius ?? theme.borderRadius.sm;

  useEffect(() => {
    const pulse = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 0.3,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );
    pulse.start();

    return () => pulse.stop();
  }, []);

  return (
    <Animated.View
      style={[
        {
          width: width as any,
          height: height as any,
          borderRadius: finalBorderRadius,
          backgroundColor: theme.colors.neutral[200],
          opacity: pulseAnim,
        },
        style,
      ]}
    />
  );
};

// Event card skeleton
export const EventCardSkeleton: React.FC = () => {
  const { theme } = useTheme();
  const styles = createStyles(theme);

  return (
    <View style={styles.eventCardSkeleton}>
      <Skeleton height={120} borderRadius={theme.borderRadius.lg} />
      <View style={styles.eventCardContent}>
        <Skeleton width="80%" height={16} />
        <View style={styles.skeletonRow}>
          <Skeleton width="40%" height={12} />
          <Skeleton width="30%" height={12} />
        </View>
        <Skeleton width="60%" height={12} />
      </View>
    </View>
  );
};

// Chat item skeleton
export const ChatItemSkeleton: React.FC = () => {
  const { theme } = useTheme();
  const styles = createStyles(theme);

  return (
    <View style={styles.chatItemSkeleton}>
      <Skeleton width={50} height={50} borderRadius={25} />
      <View style={styles.chatItemContent}>
        <View style={styles.skeletonRow}>
          <Skeleton width="60%" height={14} />
          <Skeleton width="20%" height={10} />
        </View>
        <Skeleton width="80%" height={12} />
        <Skeleton width="40%" height={10} />
      </View>
    </View>
  );
};

// Profile skeleton
export const ProfileSkeleton: React.FC = () => {
  const { theme } = useTheme();
  const styles = createStyles(theme);

  return (
    <View style={styles.profileSkeleton}>
      <Skeleton width={100} height={100} borderRadius={50} />
      <View style={styles.profileContent}>
        <Skeleton width="60%" height={20} />
        <Skeleton width="40%" height={14} />
        <View style={styles.skeletonRow}>
          <Skeleton width={60} height={60} borderRadius={theme.borderRadius.lg} />
          <Skeleton width={60} height={60} borderRadius={theme.borderRadius.lg} />
          <Skeleton width={60} height={60} borderRadius={theme.borderRadius.lg} />
        </View>
      </View>
    </View>
  );
};

// Main Loading component
const Loading: React.FC<LoadingProps> = ({
  type = 'spinner',
  size = 'medium',
  text,
  style,
}) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const spinValue = useRef(new Animated.Value(0)).current;
  const pulseValue = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    if (type === 'pulse') {
      const pulse = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseValue, {
            toValue: 0.6,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(pulseValue, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
        ])
      );
      pulse.start();
      return () => pulse.stop();
    }
  }, [type]);

  const getSpinnerSize = () => {
    switch (size) {
      case 'small': return 20;
      case 'large': return 40;
      default: return 30;
    }
  };

  if (type === 'skeleton') {
    return <Skeleton style={style} />;
  }

  if (type === 'pulse') {
    return (
      <Animated.View style={[styles.container, { opacity: pulseValue }, style]}>
        <View style={[styles.pulseCircle, styles[size]]} />
        {text && (
          <Typography variant="body2" color="secondary" style={styles.text}>
            {text}
          </Typography>
        )}
      </Animated.View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      <ActivityIndicator
        size={getSpinnerSize()}
        color={theme.colors.primary[500]}
      />
      {text && (
        <Typography variant="body2" color="secondary" style={styles.text}>
          {text}
        </Typography>
      )}
    </View>
  );
};

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing[4],
  },
  text: {
    marginTop: theme.spacing[2],
    textAlign: 'center',
  },
  pulseCircle: {
    backgroundColor: theme.colors.primary[500],
    borderRadius: 50,
  },
  small: {
    width: 20,
    height: 20,
  },
  medium: {
    width: 30,
    height: 30,
  },
  large: {
    width: 40,
    height: 40,
  },
  eventCardSkeleton: {
    backgroundColor: theme.colors.background.primary,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing[4],
    marginBottom: theme.spacing[4],
    ...theme.shadows.sm,
  },
  eventCardContent: {
    marginTop: theme.spacing[3],
    gap: theme.spacing[2],
  },
  chatItemSkeleton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing[4],
    backgroundColor: theme.colors.background.primary,
  },
  chatItemContent: {
    flex: 1,
    marginLeft: theme.spacing[3],
    gap: theme.spacing[1],
  },
  profileSkeleton: {
    alignItems: 'center',
    padding: theme.spacing[6],
    backgroundColor: theme.colors.background.primary,
  },
  profileContent: {
    alignItems: 'center',
    marginTop: theme.spacing[4],
    gap: theme.spacing[3],
  },
  skeletonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: theme.spacing[3],
  },
});

export default Loading;

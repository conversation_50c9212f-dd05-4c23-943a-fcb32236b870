import { prisma } from '../config/database';
import { cacheService } from '../config/redis';
import { logger } from '../utils/logger';

export const optimizedEventService = {
  async getEvents(page: number = 1, limit: number = 10, search?: string, location?: string) {
    const cacheKey = `events:page:${page}:limit:${limit}:search:${search || ''}:location:${location || ''}`;
    
    // Try to get from cache first
    const cachedResult = await cacheService.get(cacheKey);
    if (cachedResult) {
      logger.info(`Cache hit for events query: ${cacheKey}`);
      return cachedResult;
    }

    const skip = (page - 1) * limit;
    const where: any = {
      isActive: true,
      startTime: { gte: new Date() }
    };

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (location) {
      where.location = { contains: location, mode: 'insensitive' };
    }

    // Optimized query with selected fields only
    const [events, total] = await Promise.all([
      prisma.event.findMany({
        where,
        skip,
        take: limit,
        orderBy: { startTime: 'asc' },
        select: {
          id: true,
          title: true,
          description: true,
          location: true,
          startTime: true,
          endTime: true,
          capacity: true,
          currentParticipants: true,
          imageUrl: true,
          createdBy: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              profilePicture: true
            }
          },
          _count: {
            select: { participants: true }
          }
        }
      }),
      prisma.event.count({ where })
    ]);

    const result = {
      events,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    };

    // Cache for 5 minutes
    await cacheService.set(cacheKey, result, 300);
    
    return result;
  },

  async getEventById(eventId: string) {
    const cacheKey = `event:${eventId}`;
    
    const cachedEvent = await cacheService.get(cacheKey);
    if (cachedEvent) {
      return cachedEvent;
    }

    const event = await prisma.event.findUnique({
      where: { id: eventId },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            profilePicture: true,
            university: true
          }
        },
        participants: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                profilePicture: true
              }
            }
          }
        },
        groups: {
          select: {
            id: true,
            name: true,
            currentMembers: true,
            maxMembers: true
          }
        }
      }
    });

    if (event) {
      // Cache for 10 minutes
      await cacheService.set(cacheKey, event, 600);
    }

    return event;
  }
};
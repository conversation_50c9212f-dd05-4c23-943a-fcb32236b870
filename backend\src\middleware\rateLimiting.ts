import rateLimit from 'express-rate-limit';
import { redis } from '../config/redis';
import { Request, Response } from 'express';
import { logger } from '../utils/logger';

// Redis store for rate limiting
class RedisStore {
  async increment(key: string): Promise<{ totalHits: number; resetTime: Date }> {
    try {
      const current = await redis.incr(key);
      if (current === 1) {
        await redis.expire(key, 900); // 15 minutes
      }
      const ttl = await redis.ttl(key);
      return { 
        totalHits: current, 
        resetTime: new Date(Date.now() + ttl * 1000) 
      };
    } catch (error) {
      logger.error('Redis rate limit error:', error);
      // Fallback to allowing request if Red<PERSON> fails
      return { totalHits: 1, resetTime: new Date(Date.now() + 900000) };
    }
  }

  async decrement(key: string): Promise<void> {
    try {
      await redis.decr(key);
    } catch (error) {
      logger.error('Redis decrement error:', error);
    }
  }

  async resetKey(key: string): Promise<void> {
    try {
      await redis.del(key);
    } catch (error) {
      logger.error('Redis reset key error:', error);
    }
  }
}

const redisStore = new RedisStore();

// General API rate limiting
export const generalLimiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '1000'),
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // Skip rate limiting for health checks
    return req.path === '/health';
  }
});

// Authentication endpoints - stricter limits
export const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.AUTH_RATE_LIMIT_MAX || '10'),
  message: {
    error: 'Too many authentication attempts, please try again later.',
    retryAfter: '15 minutes'
  },
  skipSuccessfulRequests: true,
  keyGenerator: (req: Request) => {
    // Use IP + user agent for better tracking
    return `${req.ip}-${req.get('User-Agent')}`;
  }
});

// Event creation - moderate limits
export const eventCreationLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5,
  keyGenerator: (req: Request) => {
    return (req as any).user?.id || req.ip;
  },
  message: {
    error: 'Too many events created, please try again later.',
    retryAfter: '1 hour'
  }
});

// Message sending - prevent spam
export const messageLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: parseInt(process.env.MESSAGE_RATE_LIMIT_MAX || '30'),
  keyGenerator: (req: Request) => {
    return (req as any).user?.id || req.ip;
  },
  message: {
    error: 'Too many messages sent, please slow down.',
    retryAfter: '1 minute'
  }
});

// Report submission - prevent abuse
export const reportLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10,
  keyGenerator: (req: Request) => {
    return (req as any).user?.id || req.ip;
  },
  message: {
    error: 'Too many reports submitted, please try again later.',
    retryAfter: '1 hour'
  }
});

// File upload limits
export const uploadLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 20,
  keyGenerator: (req: Request) => {
    return (req as any).user?.id || req.ip;
  },
  message: {
    error: 'Too many file uploads, please try again later.',
    retryAfter: '15 minutes'
  }
});

// Admin endpoints - very strict
export const adminLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Higher limit for admin operations
  keyGenerator: (req: Request) => {
    return (req as any).user?.id || req.ip;
  },
  message: {
    error: 'Too many admin requests, please try again later.',
    retryAfter: '15 minutes'
  }
});

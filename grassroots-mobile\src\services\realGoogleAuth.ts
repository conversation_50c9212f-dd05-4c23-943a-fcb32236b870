import * as AuthSession from 'expo-auth-session';
import * as WebBrowser from 'expo-web-browser';

// Complete the auth session for web browser
WebBrowser.maybeCompleteAuthSession();

// REAL Google OAuth Configuration using the existing project
const GOOGLE_OAUTH_CONFIG = {
  clientId: '************-426s8483mlj0mg562k586peu4v3d847v.apps.googleusercontent.com',
  scopes: ['openid', 'profile', 'email'],
  additionalParameters: {
    prompt: 'select_account',
  },
};

export interface RealGoogleUser {
  id: string;
  name: string | null;
  email: string;
  photo: string | null;
  familyName: string | null;
  givenName: string | null;
}

export interface RealGoogleAuthResult {
  type: 'success' | 'cancelled' | 'error';
  user?: RealGoogleUser;
  idToken?: string;
  accessToken?: string;
  error?: string;
}

class RealGoogleAuthService {
  async signIn(): Promise<RealGoogleAuthResult> {
    try {
      console.log('🔥 Starting REAL Google OAuth web flow...');

      // Create redirect URI for Expo - use the specific redirect URI you configured
      const redirectUri = AuthSession.makeRedirectUri({
        scheme: 'grassroots-mobile',
        path: 'auth',
      });

      console.log('🌐 Redirect URI:', redirectUri);

      // Create auth request for REAL Google OAuth
      const request = new AuthSession.AuthRequest({
        clientId: GOOGLE_OAUTH_CONFIG.clientId,
        scopes: GOOGLE_OAUTH_CONFIG.scopes,
        redirectUri,
        responseType: AuthSession.ResponseType.Code,
        additionalParameters: GOOGLE_OAUTH_CONFIG.additionalParameters,
        codeChallenge: undefined, // Let Expo handle PKCE
      });

      console.log('🚀 Opening REAL Google login page...');

      // Get Google's discovery document
      const discovery = await AuthSession.fetchDiscoveryAsync('https://accounts.google.com');

      // This opens the REAL Google login page with account selection
      const result = await request.promptAsync(discovery);

      console.log('📥 Google OAuth result:', result.type);
      console.log('📥 Full result:', JSON.stringify(result, null, 2));

      if (result.type === 'success') {
        console.log('✅ REAL Google OAuth successful!');
        console.log('📋 Auth code received:', result.params.code);

        // Exchange code for tokens
        const tokenResult = await AuthSession.exchangeCodeAsync(
          {
            clientId: GOOGLE_OAUTH_CONFIG.clientId,
            code: result.params.code,
            redirectUri,
          },
          discovery
        );

        console.log('🎫 Got REAL Google tokens');

        // Get REAL user info from Google
        const userInfoResponse = await fetch(
          `https://www.googleapis.com/oauth2/v2/userinfo?access_token=${tokenResult.accessToken}`
        );
        
        if (!userInfoResponse.ok) {
          throw new Error(`Failed to fetch user info: ${userInfoResponse.status}`);
        }
        
        const userInfo = await userInfoResponse.json();
        console.log('👤 REAL Google user info:', userInfo.email);

        return {
          type: 'success',
          user: {
            id: userInfo.id,
            name: userInfo.name,
            email: userInfo.email,
            photo: userInfo.picture,
            familyName: userInfo.family_name,
            givenName: userInfo.given_name,
          },
          idToken: tokenResult.idToken,
          accessToken: tokenResult.accessToken,
        };
      } else if (result.type === 'cancel') {
        console.log('❌ User cancelled REAL Google OAuth');
        return {
          type: 'cancelled',
        };
      } else {
        console.log('❌ REAL Google OAuth failed:', result);
        return {
          type: 'error',
          error: result.error?.message || 'Google OAuth failed',
        };
      }
    } catch (error: any) {
      console.error('❌ REAL Google OAuth error:', error);
      return {
        type: 'error',
        error: error.message || 'Google OAuth failed',
      };
    }
  }

  async signOut(): Promise<void> {
    try {
      console.log('🔥 Signing out from REAL Google OAuth...');
      // Revoke tokens if available
      await WebBrowser.dismissBrowser();
      console.log('✅ REAL Google OAuth sign-out successful');
    } catch (error) {
      console.error('❌ REAL Google OAuth sign-out error:', error);
    }
  }
}

export const realGoogleAuthService = new RealGoogleAuthService();

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useDispatch, useSelector } from 'react-redux';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { AppDispatch, RootState } from '../../store';
import { fetchEvents, optInToEvent, setFilters, setSearchQuery } from '../../store/slices/eventsSlice';
import { incrementEventsAttended } from '../../store/slices/userSlice';
import { Button, Input, EventCardSkeleton, OptimizedImage } from '../../components';
import GradientBackground from '../../components/GradientBackground';
import { useTheme } from '../../contexts/ThemeContext';
import { createTypographyStyles } from '../../utils/styles';

const { width } = Dimensions.get('window');

const EventsScreen = ({ navigation }: { navigation: any }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { events, isLoading, filters, searchQuery } = useSelector((state: RootState) => state.events);
  const { theme } = useTheme();
  const styles = createStyles(theme);

  const [refreshing, setRefreshing] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'All', icon: 'apps' },
    { id: 'social', name: 'Social', icon: 'people' },
    { id: 'food', name: 'Food', icon: 'restaurant' },
    { id: 'study', name: 'Study', icon: 'book' },
    { id: 'sports', name: 'Sports', icon: 'fitness' },
    { id: 'creative', name: 'Creative', icon: 'brush' },
  ];

  useEffect(() => {
    dispatch(fetchEvents({ category: selectedCategory }));
  }, [dispatch, selectedCategory]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await dispatch(fetchEvents({ category: selectedCategory }));
    setRefreshing(false);
  };

  const handleOptIn = (eventId: string) => {
    dispatch(optInToEvent(eventId));
    // Update user analytics when joining an event
    dispatch(incrementEventsAttended());
  };

  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId);
    dispatch(setFilters({ category: categoryId }));
  };

  const handleSearch = (query: string) => {
    dispatch(setSearchQuery(query));
  };

  const filteredEvents = events.filter(event =>
    searchQuery === '' ||
    event.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    event.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderEventCard = (event: any) => {
    const hasImage = event.imageUrl && event.imageUrl.trim() !== '';

    return (
      <TouchableOpacity
        key={event.id}
        style={[styles.eventCard, !hasImage && styles.eventCardNoImage]}
        onPress={() => navigation.navigate('EventDetail', { event })}
        activeOpacity={0.9}
      >
        {hasImage && (
          <>
            <OptimizedImage
              source={{ uri: event.imageUrl }}
              style={styles.eventImage}
              resizeMode="cover"
              lazy={false}
              progressive={true}
              cacheKey={`event-${event.id}`}
            />
            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.8)']}
              style={styles.eventGradient}
            />
          </>
        )}

        <View style={[styles.eventContent, !hasImage && styles.eventContentNoImage]}>
          <View style={[styles.eventBadge, !hasImage && styles.eventBadgeNoImage]}>
            <Text style={[styles.eventBadgeText, !hasImage && styles.eventBadgeTextNoImage]}>
              {event.eventType === 'curated' ? '✨ VIBE Curated' : '👥 Community'}
            </Text>
          </View>
          <Text style={[styles.eventTitle, !hasImage && styles.eventTitleNoImage]}>{event.title}</Text>
          <Text style={[styles.eventLocation, !hasImage && styles.eventLocationNoImage]}>
            📍 {event.location.name}
          </Text>
          <Text style={[styles.eventTime, !hasImage && styles.eventTimeNoImage]}>
            🕐 {new Date(event.startTime).toLocaleDateString()} at{' '}
            {new Date(event.startTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </Text>
          <View style={styles.eventFooter}>
            <Text style={[styles.eventAttendees, !hasImage && styles.eventAttendeesNoImage]}>
              {event.currentAttendees}/{event.capacity} joined
            </Text>
            <Button
              title={event.isOptedIn ? '✓ Joined' : 'Join'}
              onPress={() => handleOptIn(event.id)}
              variant={event.isOptedIn ? 'secondary' : 'primary'}
              size="small"
            />
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderCategoryFilter = () => (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={styles.categoriesContainer}
      contentContainerStyle={styles.categoriesContent}
    >
      {categories.map((category) => (
        <TouchableOpacity
          key={category.id}
          style={[
            styles.categoryChip,
            selectedCategory === category.id && styles.categoryChipActive
          ]}
          onPress={() => handleCategorySelect(category.id)}
        >
          <Ionicons
            name={category.icon as any}
            size={20}
            color={selectedCategory === category.id ? theme.colors.text.inverse : theme.colors.primary[500]}
          />
          <Text
            style={[
              styles.categoryChipText,
              selectedCategory === category.id && styles.categoryChipTextActive
            ]}
          >
            {category.name}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );

  return (
    <GradientBackground variant="primary" style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        {/* Fixed Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Discover Events</Text>
          <Text style={styles.subtitle}>Find your next adventure</Text>
        </View>

      {/* Fixed Search Bar */}
      <View style={styles.searchSection}>
        <Input
          placeholder="Search events..."
          value={searchQuery}
          onChangeText={handleSearch}
          leftIcon="search"
          style={styles.searchInput}
        />
      </View>

      {/* Fixed Category Filter */}
      <View style={styles.categorySection}>
        {renderCategoryFilter()}
      </View>

      {/* Scrollable Events List */}
      <View style={styles.eventsSection}>
        <ScrollView
          style={styles.eventsScrollView}
          contentContainerStyle={styles.eventsContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              tintColor={theme.colors.primary[500]}
              colors={[theme.colors.primary[500]]}
              progressBackgroundColor={theme.colors.background.primary}
            />
          }
          showsVerticalScrollIndicator={false}
        >
          {isLoading && !refreshing ? (
            <View style={styles.eventsList}>
              {[...Array(3)].map((_, index) => (
                <EventCardSkeleton key={index} />
              ))}
            </View>
          ) : filteredEvents.length > 0 ? (
            <View style={styles.eventsList}>
              {filteredEvents.map(renderEventCard)}
            </View>
          ) : (
            <View style={styles.emptyContainer}>
              <Ionicons name="calendar-outline" size={64} color={theme.colors.text.tertiary} />
              <Text style={styles.emptyTitle}>No events found</Text>
              <Text style={styles.emptySubtitle}>
                {searchQuery ? 'Try adjusting your search' : 'Check back later for new events'}
              </Text>
            </View>
          )}
        </ScrollView>
        </View>
      </SafeAreaView>
    </GradientBackground>
  );
};

const createStyles = (theme: any) => {
  const typography = createTypographyStyles(theme);

  return StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    backgroundColor: theme.colors.background.primary,
    paddingHorizontal: theme.spacing[5],
    paddingVertical: theme.spacing[3],
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border.light,
  },
  title: {
    ...typography.h4,
    color: theme.colors.text.primary,
    fontWeight: theme.typography.fontWeight.bold,
    textAlign: 'center',
  },
  subtitle: {
    ...typography.body2,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    marginTop: theme.spacing[1],
  },
  searchSection: {
    backgroundColor: theme.colors.background.primary,
    paddingHorizontal: theme.spacing[5],
    paddingVertical: theme.spacing[3],
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border.light,
  },
  searchInput: {
    marginBottom: 0,
  },
  categorySection: {
    backgroundColor: theme.colors.background.primary,
    paddingVertical: theme.spacing[3],
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border.light,
  },
  categoriesContainer: {
    paddingHorizontal: theme.spacing[5],
  },
  categoriesContent: {
    gap: theme.spacing[2],
  },
  categoryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.background.secondary,
    borderRadius: theme.borderRadius.full,
    paddingVertical: 6,
    paddingHorizontal: theme.spacing[3],
    marginRight: theme.spacing[2],
    borderWidth: 1,
    borderColor: theme.colors.border.light,
    height: 36,
  },
  categoryChipActive: {
    backgroundColor: theme.colors.primary[500],
    borderColor: theme.colors.primary[500],
  },
  categoryChipText: {
    ...typography.body2,
    color: theme.colors.primary[500],
    marginLeft: theme.spacing[1],
    fontWeight: theme.typography.fontWeight.medium,
  },
  categoryChipTextActive: {
    color: theme.colors.text.inverse,
  },
  eventsSection: {
    flex: 1,
  },
  eventsScrollView: {
    flex: 1,
  },
  eventsContent: {
    paddingHorizontal: theme.spacing[5],
    paddingTop: theme.spacing[4],
    paddingBottom: theme.spacing[6],
  },
  eventsList: {
    gap: theme.spacing[4],
  },
  eventCard: {
    height: 220,
    borderRadius: theme.borderRadius.lg,
    overflow: 'hidden',
    ...theme.shadows.md,
    backgroundColor: theme.colors.background.card,
    position: 'relative',
  },
  eventImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    top: 0,
    left: 0,
  },
  eventGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'flex-end',
  },
  eventContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: theme.spacing[4],
    zIndex: 2,
  },
  eventBadge: {
    alignSelf: 'flex-start',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingHorizontal: theme.spacing[2],
    paddingVertical: theme.spacing[1],
    borderRadius: theme.borderRadius.md,
    marginBottom: theme.spacing[2],
  },
  eventBadgeText: {
    ...typography.caption,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.text.primary,
  },
  eventTitle: {
    ...typography.h6,
    color: theme.colors.text.inverse,
    marginBottom: theme.spacing[1],
  },
  eventLocation: {
    ...typography.body2,
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: theme.spacing[1],
  },
  eventTime: {
    ...typography.body2,
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: theme.spacing[3],
  },
  eventFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  eventAttendees: {
    ...typography.caption,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  loadingContainer: {
    padding: theme.spacing[10],
    alignItems: 'center',
  },
  loadingText: {
    ...typography.body1,
    color: theme.colors.text.secondary,
  },
  emptyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: theme.spacing[16],
    minHeight: 200,
  },
  emptyTitle: {
    ...typography.h5,
    color: theme.colors.text.primary,
    marginTop: theme.spacing[4],
    marginBottom: theme.spacing[2],
  },
  emptySubtitle: {
    ...typography.body1,
    color: theme.colors.text.secondary,
    textAlign: 'center',
  },
  // Styles for events without images
  eventCardNoImage: {
    backgroundColor: theme.colors.background.card,
    height: 'auto',
    minHeight: 180,
  },
  eventContentNoImage: {
    position: 'relative',
    padding: theme.spacing[4],
    zIndex: 1,
  },
  eventBadgeNoImage: {
    backgroundColor: theme.colors.primary[100],
  },
  eventBadgeTextNoImage: {
    color: theme.colors.primary[700],
  },
  eventTitleNoImage: {
    color: theme.colors.text.primary,
  },
  eventLocationNoImage: {
    color: theme.colors.text.secondary,
  },
  eventTimeNoImage: {
    color: theme.colors.text.secondary,
  },
  eventAttendeesNoImage: {
    color: theme.colors.text.tertiary,
  },
  });
};

export default EventsScreen;

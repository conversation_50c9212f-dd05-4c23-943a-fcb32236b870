import { Request, Response } from 'express';
import { eventService } from '../services/eventService';
import { AuthRequest } from '../middleware/auth';
import { logger } from '../utils/logger';
import { validateEventData } from '../utils/validation';

export const getEvents = async (req: Request, res: Response) => {
  try {
    const queryParams = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 10,
      search: req.query.search as string,
      location: req.query.location as string,
      startDate: req.query.startDate as string,
      endDate: req.query.endDate as string,
    };

    const result = await eventService.getEvents(queryParams);
    res.json(result);
  } catch (error: any) {
    logger.error('Get events error:', error);
    res.status(500).json({ error: 'Failed to fetch events' });
  }
};

export const getEventById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const event = await eventService.getEventById(id);
    
    if (!event) {
      return res.status(404).json({ error: 'Event not found' });
    }

    res.json({ event });
  } catch (error: any) {
    logger.error('Get event by ID error:', error);
    res.status(500).json({ error: 'Failed to fetch event' });
  }
};

export const createEvent = async (req: AuthRequest, res: Response) => {
  try {
    const validation = validateEventData(req.body);
    if (!validation.isValid) {
      return res.status(400).json({ error: validation.errors });
    }

    const eventData = {
      ...req.body,
      createdById: req.user!.id,
    };

    const event = await eventService.createEvent(eventData);
    res.status(201).json({ event });
  } catch (error: any) {
    logger.error('Create event error:', error);
    res.status(500).json({ error: 'Failed to create event' });
  }
};

export const joinEvent = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user!.id;

    const result = await eventService.joinEvent(id, userId);
    res.json(result);
  } catch (error: any) {
    logger.error('Join event error:', error);
    
    if (error.message === 'Event not found') {
      return res.status(404).json({ error: error.message });
    }
    if (error.message === 'Event is full' || error.message === 'Already joined') {
      return res.status(400).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to join event' });
  }
};

export const leaveEvent = async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user!.id;

    await eventService.leaveEvent(id, userId);
    res.json({ message: 'Successfully left event' });
  } catch (error: any) {
    logger.error('Leave event error:', error);
    
    if (error.message === 'Event not found' || error.message === 'Not a participant') {
      return res.status(404).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to leave event' });
  }
};
import { prisma } from '../config/database';
import { CreateConnectionData } from '../models/Connection';
import { logger } from '../utils/logger';

export const connectionService = {
  async createConnection(fromUserId: string, data: CreateConnectionData) {
    // Check if event exists and user participated
    const participation = await prisma.eventParticipant.findUnique({
      where: {
        eventId_userId: { eventId: data.eventId, userId: fromUserId }
      }
    });

    if (!participation) {
      throw new Error('User did not participate in this event');
    }

    // Check if connection already exists
    const existingConnection = await prisma.connection.findFirst({
      where: {
        eventId: data.eventId,
        fromUserId,
        toUserId: data.toUserId
      }
    });

    if (existingConnection) {
      throw new Error('Connection already exists');
    }

    // Create the connection
    const connection = await prisma.connection.create({
      data: {
        eventId: data.eventId,
        fromUserId,
        toUserId: data.toUserId,
        connectionType: data.connectionType,
        status: 'PENDING'
      }
    });

    // Check for mutual connection
    if (data.connectionType !== 'PASS') {
      await this.checkMutualConnection(data.eventId, fromUserId, data.toUserId);
    }

    logger.info(`Connection created: ${connection.id} from ${fromUserId} to ${data.toUserId}`);
    return connection;
  },

  async checkMutualConnection(eventId: string, userId1: string, userId2: string) {
    const connections = await prisma.connection.findMany({
      where: {
        eventId,
        OR: [
          { fromUserId: userId1, toUserId: userId2 },
          { fromUserId: userId2, toUserId: userId1 }
        ],
        connectionType: { in: ['FRIEND', 'SPARK'] }
      }
    });

    if (connections.length === 2) {
      // Mutual connection found
      await prisma.connection.updateMany({
        where: {
          id: { in: connections.map(c => c.id) }
        },
        data: {
          status: 'MUTUAL',
          mutualAt: new Date()
        }
      });

      logger.info(`Mutual connection established between ${userId1} and ${userId2}`);
      return true;
    }

    return false;
  },

  async getUserConnections(userId: string) {
    return await prisma.connection.findMany({
      where: {
        OR: [
          { fromUserId: userId },
          { toUserId: userId }
        ],
        status: 'MUTUAL'
      },
      include: {
        fromUser: {
          select: { id: true, firstName: true, lastName: true, profilePicture: true }
        },
        toUser: {
          select: { id: true, firstName: true, lastName: true, profilePicture: true }
        },
        event: {
          select: { id: true, title: true, startTime: true, location: true }
        }
      },
      orderBy: { mutualAt: 'desc' }
    });
  },

  async getEventParticipantsForReview(eventId: string, userId: string) {
    // Get all participants except the current user
    const participants = await prisma.eventParticipant.findMany({
      where: {
        eventId,
        userId: { not: userId }
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            profilePicture: true,
            bio: true,
            interests: true,
            university: true
          }
        }
      }
    });

    // Get existing connections to filter out already reviewed users
    const existingConnections = await prisma.connection.findMany({
      where: {
        eventId,
        fromUserId: userId
      }
    });

    const reviewedUserIds = existingConnections.map(c => c.toUserId);
    
    return participants.filter(p => !reviewedUserIds.includes(p.userId));
  },

  async sendDirectMessage(connectionId: string, senderId: string, content: string) {
    // Verify connection exists and is mutual
    const connection = await prisma.connection.findUnique({
      where: { id: connectionId }
    });

    if (!connection || connection.status !== 'MUTUAL') {
      throw new Error('Invalid connection');
    }

    if (connection.fromUserId !== senderId && connection.toUserId !== senderId) {
      throw new Error('Unauthorized to send message');
    }

    const message = await prisma.directMessage.create({
      data: {
        connectionId,
        senderId,
        content,
        messageType: 'TEXT'
      },
      include: {
        sender: {
          select: { id: true, firstName: true, lastName: true, profilePicture: true }
        }
      }
    });

    logger.info(`Direct message sent in connection ${connectionId}`);
    return message;
  },

  async getDirectMessages(connectionId: string, userId: string, page: number = 1, limit: number = 50) {
    // Verify user is part of the connection
    const connection = await prisma.connection.findUnique({
      where: { id: connectionId }
    });

    if (!connection || (connection.fromUserId !== userId && connection.toUserId !== userId)) {
      throw new Error('Unauthorized access');
    }

    const skip = (page - 1) * limit;

    const messages = await prisma.directMessage.findMany({
      where: { connectionId },
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        sender: {
          select: { id: true, firstName: true, lastName: true, profilePicture: true }
        }
      }
    });

    return messages.reverse();
  }
};